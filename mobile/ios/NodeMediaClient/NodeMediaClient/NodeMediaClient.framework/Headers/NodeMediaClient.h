//
//  NodeMediaClient.h
//  v2.9.6
//
//  Created by <PERSON><PERSON><PERSON> (<EMAIL>) on 16/12/29.
//  Copyright © 2017 NodeMedia. All rights reserved.
//

#import <UIKit/UIKit.h>

//! Project version number for NodeMediaClient.
FOUNDATION_EXPORT double NodeMediaClientVersionNumber;

//! Project version string for NodeMediaClient.
FOUNDATION_EXPORT const unsigned char NodeMediaClientVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <NodeMediaClient/PublicHeader.h>

#define NM_LOGLEVEL_ERROR 0
#define NM_LOGLEVEL_INFO 1
#define NM_LOGLEVEL_DEBUG 2

#import <NodeMediaClient/NodePlayer.h>
#import <NodeMediaClient/NodePublisher.h>

