<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>P4P</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>4.4.3</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb2174425329675478</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.653995377488-a9rlf11v44a1100uragkoje0ke975pns</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>app.playaz4playaz.ios</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.653995377488-a9rlf11v44a1100uragkoje0ke975pns</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
        	<string>p4p-uat.duckdns.org</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>playaz4playaz</string>
			</array>
 		</dict>
		
	</array>
	<key>CFBundleVersion</key>
	<string>4.4.3.2874</string>
	<key>FacebookAppID</key>
	<string>2174425329675478</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>Tailgate v3</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fbapi</string>
		<string>fb-messenger-share-api</string>
		<string>fbauth2</string>
		<string>fbshareextension</string>
		<string>mailto</string>
		<string>message</string>
		<string>readdle-spark</string>
		<string>airmail</string>
		<string>ms-outlook</string>
		<string>googlegmail</string>
		<string>inbox-gmail</string>
		<string>ymail</string>
		<string>superhuman</string>
		<string>yandexmail</string>
		<string>fastmail</string>
		<string>protonmail</string>
		<string>szn-email</string>
		<string>fb</string>
		<string>instagram</string>
		<string>https</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
		 <!-- Do not change NSAllowsArbitraryLoads to true, or you will risk app rejection! -->
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>We use camera to do private connects, user avatar updates and posts.</string>
	 <!-- TEMPORARY DESCRIPTION - CURRENTLY THE APP DOESN'T ASK FOR LOCATION. BUT APPSTORE CONNECT REQUIRES THIS -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We use your device location to suggest the most appropriate language. Location is used only to recommend a language, it is not stored or shared without your consent.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>We use microphone to do private connects, post videos and similar in app social interactions.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>We use this when adding posts to feed, and save the new media.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>To be used  on public or private posts, user avatars, and other related data.</string>
	<key>UIAppFonts</key>
	<array>
		<string>CeraPro-Bold.otf</string>
		<string>CeraPro-Medium.otf</string>
		<string>CeraPro-Regular.otf</string>
		<string>CeraPro-Thin.otf</string>
		<string>CeraStencilPRO-Bold.otf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
		<string>voip</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>Launch Screen</string>
	<key>UIMainStoryboardFile</key>
	<string>Launch Screen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleLightContent</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>


</dict>
</plist>
