$RNFirebaseAsStaticFramework = true

def node_require(script)
# Resolve script with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  "require.resolve(
    '#{script}',
    {paths: [process.argv[1]]},
  )", __dir__]).strip
end

node_require('react-native/scripts/react_native_pods.rb')
node_require('react-native-permissions/scripts/setup.rb')

platform :ios, min_ios_version_supported
prepare_react_native_project!


linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

# ⬇️ uncomment wanted permissions
setup_permissions([
  'AppTrackingTransparency',
  # 'Bluetooth',
  # 'Calendars',
  # 'CalendarsWriteOnly',
  'Camera',
  # 'Contacts',
  # 'FaceID',
  # 'LocationAccuracy',
  # 'LocationAlways',
  # 'LocationWhenInUse',
  # 'MediaLibrary',
  'Microphone',
  # 'Motion',
  'Notifications',
  'PhotoLibrary',
  # 'PhotoLibraryAddOnly',
  # 'Reminders',
  # 'Siri',
  # 'SpeechRecognition',
  # 'StoreKit',
])

# # Convert all permission pods into static libraries
# pre_install do |installer|
#   Pod::Installer::Xcode::TargetValidator.send(:define_method, :verify_no_static_framework_transitive_dependencies) {}

#   installer.pod_targets.each do |pod|
#     if pod.name.eql?('RNPermissions') || pod.name.start_with?('Permission-')
#       def pod.build_type;
#         Pod::BuildType.static_library # >= 1.9
#       end
#     end
#   end
# end

target 'Playaz4Playaz' do
  # use_frameworks! :linkage => :static
  config = use_native_modules!
  # use_modular_headers!

  # Lock TOCropViewController to compatible version (fixes UIGlassEffect error)
  pod 'TOCropViewController', '~> 2.7.4'

  # Flags change depending on the env values.
  # flags = get_default_flags()

  pod 'GoogleUtilities', :modular_headers => true
  pod 'GoogleSignIn', '~> 9.0'
  pod 'FirebaseCore', :modular_headers => true
  

  pod 'glog', :podspec => '../node_modules/react-native/third-party-podspecs/glog.podspec', :modular_headers => false
  # pod 'NodeMediaClient', '~> 3.2'

  use_react_native!(
    :path => config[:reactNativePath],
    # Hermes is now enabled by default. Disable by setting this flag to false.
    # Upcoming versions of React Native may rely on get_default_flags(), but
    # we make it explicit here to aid in the React Native upgrade process.
    # :hermes_enabled => flags[:hermes_enabled],
    # :fabric_enabled => flags[:fabric_enabled],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  post_install do |installer|
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = "arm64"
      end
    end

    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
  end
end
