// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* Playaz4PlayazTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* Playaz4PlayazTests.m */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		255521275FEACB44A0188BA7 /* libPods-Playaz4Playaz.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 449AFD5019F07916E70925D5 /* libPods-Playaz4Playaz.a */; };
		4E340F2828ABC86500373588 /* CallKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4E340F2728ABC86500373588 /* CallKit.framework */; };
		4E340F2A28ABC86D00373588 /* Intents.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4E340F2928ABC86D00373588 /* Intents.framework */; };
		4E5DEE7A2997B844003A9FDD /* File.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4E5DEE792997B844003A9FDD /* File.swift */; };
		4E62E5BD28DE215400E427A7 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 4E62E5BB28DE214500E427A7 /* GoogleService-Info.plist */; };
		4EB06F6E28EEE7DF0022984E /* Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 4EB06F6D28EEE7DF0022984E /* Launch Screen.storyboard */; };
		4EB06F6F28EEE7DF0022984E /* Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 4EB06F6D28EEE7DF0022984E /* Launch Screen.storyboard */; };
		5F819746192F751CFE4D8087 /* libPods-Playaz4Playaz-Playaz4PlayazTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = B7376B65F32BD3AF2471A44C /* libPods-Playaz4Playaz-Playaz4PlayazTests.a */; };
		AE577CAE1CC2496787CF97DA /* CeraStencilPRO-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 1D832E11981D4F05AD12120C /* CeraStencilPRO-Bold.otf */; };
		BF1603C70F4A460CB0CF6194 /* CeraPro-Regular.otf in Resources */ = {isa = PBXBuildFile; fileRef = DCE89B45A94646C5BDE73009 /* CeraPro-Regular.otf */; };
		D1F4232415D24BEE9C5A2797 /* CeraPro-Medium.otf in Resources */ = {isa = PBXBuildFile; fileRef = 1F4B165241674A64AEFD8F4C /* CeraPro-Medium.otf */; };
		E3FD591E4B804A6385C72C17 /* CeraPro-Thin.otf in Resources */ = {isa = PBXBuildFile; fileRef = 7B2E1131B2614475AF3ED423 /* CeraPro-Thin.otf */; };
		EB2B60DF53344576A35218FC /* CeraPro-Bold.otf in Resources */ = {isa = PBXBuildFile; fileRef = 31DB88D5319044BB84824014 /* CeraPro-Bold.otf */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = Playaz4Playaz;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* Playaz4PlayazTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = Playaz4PlayazTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* Playaz4PlayazTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Playaz4PlayazTests.m; sourceTree = "<group>"; };
		0EEA5A825BD30449D361F636 /* Pods-Playaz4Playaz-Playaz4PlayazTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Playaz4Playaz-Playaz4PlayazTests.debug.xcconfig"; path = "Target Support Files/Pods-Playaz4Playaz-Playaz4PlayazTests/Pods-Playaz4Playaz-Playaz4PlayazTests.debug.xcconfig"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* P4P.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = P4P.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = Playaz4Playaz/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = Playaz4Playaz/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = Playaz4Playaz/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = Playaz4Playaz/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = Playaz4Playaz/main.m; sourceTree = "<group>"; };
		1D832E11981D4F05AD12120C /* CeraStencilPRO-Bold.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "CeraStencilPRO-Bold.otf"; path = "../src/assets/fonts/CeraStencilPRO-Bold.otf"; sourceTree = "<group>"; };
		1F4B165241674A64AEFD8F4C /* CeraPro-Medium.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "CeraPro-Medium.otf"; path = "../src/assets/fonts/CeraPro-Medium.otf"; sourceTree = "<group>"; };
		31DB88D5319044BB84824014 /* CeraPro-Bold.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "CeraPro-Bold.otf"; path = "../src/assets/fonts/CeraPro-Bold.otf"; sourceTree = "<group>"; };
		449AFD5019F07916E70925D5 /* libPods-Playaz4Playaz.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-Playaz4Playaz.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		4E340F2728ABC86500373588 /* CallKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CallKit.framework; path = System/Library/Frameworks/CallKit.framework; sourceTree = SDKROOT; };
		4E340F2928ABC86D00373588 /* Intents.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Intents.framework; path = System/Library/Frameworks/Intents.framework; sourceTree = SDKROOT; };
		4E5DEE782997B843003A9FDD /* Playaz4Playaz-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Playaz4Playaz-Bridging-Header.h"; sourceTree = "<group>"; };
		4E5DEE792997B844003A9FDD /* File.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = File.swift; sourceTree = "<group>"; };
		4E62E5BB28DE214500E427A7 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		4EB06F6828EEC9780022984E /* Playaz4Playaz.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = Playaz4Playaz.entitlements; path = Playaz4Playaz/Playaz4Playaz.entitlements; sourceTree = "<group>"; };
		4EB06F6D28EEE7DF0022984E /* Launch Screen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = "Launch Screen.storyboard"; sourceTree = "<group>"; };
		751831B28C7979C76AB997EA /* Pods-Playaz4Playaz-Playaz4PlayazTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Playaz4Playaz-Playaz4PlayazTests.release.xcconfig"; path = "Target Support Files/Pods-Playaz4Playaz-Playaz4PlayazTests/Pods-Playaz4Playaz-Playaz4PlayazTests.release.xcconfig"; sourceTree = "<group>"; };
		7B2E1131B2614475AF3ED423 /* CeraPro-Thin.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "CeraPro-Thin.otf"; path = "../src/assets/fonts/CeraPro-Thin.otf"; sourceTree = "<group>"; };
		94D1303621F88D9585E11856 /* Pods-Playaz4Playaz.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Playaz4Playaz.debug.xcconfig"; path = "Target Support Files/Pods-Playaz4Playaz/Pods-Playaz4Playaz.debug.xcconfig"; sourceTree = "<group>"; };
		B7376B65F32BD3AF2471A44C /* libPods-Playaz4Playaz-Playaz4PlayazTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-Playaz4Playaz-Playaz4PlayazTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		DCE89B45A94646C5BDE73009 /* CeraPro-Regular.otf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "CeraPro-Regular.otf"; path = "../src/assets/fonts/CeraPro-Regular.otf"; sourceTree = "<group>"; };
		E077EE5ECAB884B0A2798632 /* Pods-Playaz4Playaz.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Playaz4Playaz.release.xcconfig"; path = "Target Support Files/Pods-Playaz4Playaz/Pods-Playaz4Playaz.release.xcconfig"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				5F819746192F751CFE4D8087 /* libPods-Playaz4Playaz-Playaz4PlayazTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				4E340F2A28ABC86D00373588 /* Intents.framework in Frameworks */,
				4E340F2828ABC86500373588 /* CallKit.framework in Frameworks */,
				255521275FEACB44A0188BA7 /* libPods-Playaz4Playaz.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* Playaz4PlayazTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* Playaz4PlayazTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = Playaz4PlayazTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* Playaz4Playaz */ = {
			isa = PBXGroup;
			children = (
				4EB06F6928EEE73D0022984E /* images.xcassets */,
				4EB06F6828EEC9780022984E /* Playaz4Playaz.entitlements */,
				4E62E5BB28DE214500E427A7 /* GoogleService-Info.plist */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				13B07FB71A68108700A75B9A /* main.m */,
			);
			name = Playaz4Playaz;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				4E340F2928ABC86D00373588 /* Intents.framework */,
				4E340F2728ABC86500373588 /* CallKit.framework */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				449AFD5019F07916E70925D5 /* libPods-Playaz4Playaz.a */,
				B7376B65F32BD3AF2471A44C /* libPods-Playaz4Playaz-Playaz4PlayazTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		4EB06F6928EEE73D0022984E /* images.xcassets */ = {
			isa = PBXGroup;
			children = (
				4EB06F6D28EEE7DF0022984E /* Launch Screen.storyboard */,
			);
			name = images.xcassets;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				4E5DEE792997B844003A9FDD /* File.swift */,
				13B07FAE1A68108700A75B9A /* Playaz4Playaz */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* Playaz4PlayazTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				C003C86FD21948929AC39CE6 /* Resources */,
				4E5DEE782997B843003A9FDD /* Playaz4Playaz-Bridging-Header.h */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* P4P.app */,
				00E356EE1AD99517003FC87E /* Playaz4PlayazTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				94D1303621F88D9585E11856 /* Pods-Playaz4Playaz.debug.xcconfig */,
				E077EE5ECAB884B0A2798632 /* Pods-Playaz4Playaz.release.xcconfig */,
				0EEA5A825BD30449D361F636 /* Pods-Playaz4Playaz-Playaz4PlayazTests.debug.xcconfig */,
				751831B28C7979C76AB997EA /* Pods-Playaz4Playaz-Playaz4PlayazTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		C003C86FD21948929AC39CE6 /* Resources */ = {
			isa = PBXGroup;
			children = (
				31DB88D5319044BB84824014 /* CeraPro-Bold.otf */,
				1F4B165241674A64AEFD8F4C /* CeraPro-Medium.otf */,
				DCE89B45A94646C5BDE73009 /* CeraPro-Regular.otf */,
				7B2E1131B2614475AF3ED423 /* CeraPro-Thin.otf */,
				1D832E11981D4F05AD12120C /* CeraStencilPRO-Bold.otf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* Playaz4PlayazTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "Playaz4PlayazTests" */;
			buildPhases = (
				5DD0E0BAE500143A9C1848F8 /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				D3AF5C7A4B489657B91AED32 /* [CP] Embed Pods Frameworks */,
				FC79549CA91CEF60AFC8658E /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = Playaz4PlayazTests;
			productName = Playaz4PlayazTests;
			productReference = 00E356EE1AD99517003FC87E /* Playaz4PlayazTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* Playaz4Playaz */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "Playaz4Playaz" */;
			buildPhases = (
				AF938695E9F7E1A5D89691BC /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				CFFAC903881A34CC9BA86CD4 /* [CP] Embed Pods Frameworks */,
				3CDF951DCF1DD955D87D5052 /* [CP] Copy Pods Resources */,
				7AC27C6B71BDDD33EC71BA24 /* [CP-User] [RNFB] Core Configuration */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Playaz4Playaz;
			productName = Playaz4Playaz;
			productReference = 13B07F961A680F5B00A75B9A /* P4P.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1520;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1420;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "Playaz4Playaz" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* Playaz4Playaz */,
				00E356ED1AD99517003FC87E /* Playaz4PlayazTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				4EB06F6F28EEE7DF0022984E /* Launch Screen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				4E62E5BD28DE215400E427A7 /* GoogleService-Info.plist in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				4EB06F6E28EEE7DF0022984E /* Launch Screen.storyboard in Resources */,
				EB2B60DF53344576A35218FC /* CeraPro-Bold.otf in Resources */,
				D1F4232415D24BEE9C5A2797 /* CeraPro-Medium.otf in Resources */,
				BF1603C70F4A460CB0CF6194 /* CeraPro-Regular.otf in Resources */,
				E3FD591E4B804A6385C72C17 /* CeraPro-Thin.otf in Resources */,
				AE577CAE1CC2496787CF97DA /* CeraStencilPRO-Bold.otf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nexport NODE_BINARY=node\n../node_modules/react-native/scripts/react-native-xcode.sh\n";
		};
		3CDF951DCF1DD955D87D5052 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Playaz4Playaz/Pods-Playaz4Playaz-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Playaz4Playaz/Pods-Playaz4Playaz-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Playaz4Playaz/Pods-Playaz4Playaz-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		5DD0E0BAE500143A9C1848F8 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Playaz4Playaz-Playaz4PlayazTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		7AC27C6B71BDDD33EC71BA24 /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		AF938695E9F7E1A5D89691BC /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Playaz4Playaz-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		CFFAC903881A34CC9BA86CD4 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Playaz4Playaz/Pods-Playaz4Playaz-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Playaz4Playaz/Pods-Playaz4Playaz-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Playaz4Playaz/Pods-Playaz4Playaz-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		D3AF5C7A4B489657B91AED32 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Playaz4Playaz-Playaz4PlayazTests/Pods-Playaz4Playaz-Playaz4PlayazTests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Playaz4Playaz-Playaz4PlayazTests/Pods-Playaz4Playaz-Playaz4PlayazTests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Playaz4Playaz-Playaz4PlayazTests/Pods-Playaz4Playaz-Playaz4PlayazTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FC79549CA91CEF60AFC8658E /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Playaz4Playaz-Playaz4PlayazTests/Pods-Playaz4Playaz-Playaz4PlayazTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Playaz4Playaz-Playaz4PlayazTests/Pods-Playaz4Playaz-Playaz4PlayazTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Playaz4Playaz-Playaz4PlayazTests/Pods-Playaz4Playaz-Playaz4PlayazTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				00E356F31AD99517003FC87E /* Playaz4PlayazTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				4E5DEE7A2997B844003A9FDD /* File.swift in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* Playaz4Playaz */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0EEA5A825BD30449D361F636 /* Pods-Playaz4Playaz-Playaz4PlayazTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "-";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = Playaz4PlayazTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Playaz4Playaz.app/Playaz4Playaz";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 751831B28C7979C76AB997EA /* Pods-Playaz4Playaz-Playaz4PlayazTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "-";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = Playaz4PlayazTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Playaz4Playaz.app/Playaz4Playaz";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 94D1303621F88D9585E11856 /* Pods-Playaz4Playaz.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Playaz4Playaz/Playaz4Playaz.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 3.0.19;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 4D6PSRZ734;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/CocoaAsyncSocket\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Fmt\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-PeerTalk\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-RSocket\"",
					"\"${PODS_ROOT}/Headers/Public/FlipperKit\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNCAsyncStorage\"",
					"\"${PODS_ROOT}/Headers/Public/RNCallKeep\"",
					"\"${PODS_ROOT}/Headers/Public/RNDateTimePicker\"",
					"\"${PODS_ROOT}/Headers/Public/RNDeviceInfo\"",
					"\"${PODS_ROOT}/Headers/Public/RNFS\"",
					"\"${PODS_ROOT}/Headers/Public/RNGestureHandler\"",
					"\"${PODS_ROOT}/Headers/Public/RNImageCropPicker\"",
					"\"${PODS_ROOT}/Headers/Public/RNReanimated\"",
					"\"${PODS_ROOT}/Headers/Public/RNSVG\"",
					"\"${PODS_ROOT}/Headers/Public/RNScreens\"",
					"\"${PODS_ROOT}/Headers/Public/RNTextSize\"",
					"\"${PODS_ROOT}/Headers/Public/React-Codegen\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeIncallManager\"",
					"\"${PODS_ROOT}/Headers/Public/SocketRocket\"",
					"\"${PODS_ROOT}/Headers/Public/TOCropViewController\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/YogaKit\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/libevent\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-cameraroll\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-image-crop-tools\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-image-resizer\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-pager-view\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-safe-area-context\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-splash-screen\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-video\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webrtc\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost-for-react-native\"",
					"\"$(PODS_ROOT)/glog\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/React-hermes\"",
					"\"${PODS_ROOT}/Headers/Public/hermes-engine\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_TARGET_SRCROOT)/include/\"",
					"$(SRCROOT)/../node_modules/react-native-callkeep/ios/RNCallKeep",
				);
				INFOPLIST_FILE = Playaz4Playaz/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.9;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = app.playaz4playaz.ios;
				PRODUCT_NAME = P4P;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match Development app.playaz4playaz.ios";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "Playaz4Playaz-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E077EE5ECAB884B0A2798632 /* Pods-Playaz4Playaz.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Playaz4Playaz/Playaz4Playaz.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 3.0.19;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 4D6PSRZ734;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = arm64;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/CocoaAsyncSocket\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Fmt\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-PeerTalk\"",
					"\"${PODS_ROOT}/Headers/Public/Flipper-RSocket\"",
					"\"${PODS_ROOT}/Headers/Public/FlipperKit\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNCAsyncStorage\"",
					"\"${PODS_ROOT}/Headers/Public/RNCallKeep\"",
					"\"${PODS_ROOT}/Headers/Public/RNDateTimePicker\"",
					"\"${PODS_ROOT}/Headers/Public/RNDeviceInfo\"",
					"\"${PODS_ROOT}/Headers/Public/RNFS\"",
					"\"${PODS_ROOT}/Headers/Public/RNGestureHandler\"",
					"\"${PODS_ROOT}/Headers/Public/RNImageCropPicker\"",
					"\"${PODS_ROOT}/Headers/Public/RNReanimated\"",
					"\"${PODS_ROOT}/Headers/Public/RNSVG\"",
					"\"${PODS_ROOT}/Headers/Public/RNScreens\"",
					"\"${PODS_ROOT}/Headers/Public/RNTextSize\"",
					"\"${PODS_ROOT}/Headers/Public/React-Codegen\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeIncallManager\"",
					"\"${PODS_ROOT}/Headers/Public/SocketRocket\"",
					"\"${PODS_ROOT}/Headers/Public/TOCropViewController\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/YogaKit\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/libevent\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-cameraroll\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-image-crop-tools\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-image-resizer\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-pager-view\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-safe-area-context\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-splash-screen\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-video\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webrtc\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost-for-react-native\"",
					"\"$(PODS_ROOT)/glog\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/React-hermes\"",
					"\"${PODS_ROOT}/Headers/Public/hermes-engine\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_TARGET_SRCROOT)/include/\"",
					"$(SRCROOT)/../node_modules/react-native-callkeep/ios/RNCallKeep",
				);
				INFOPLIST_FILE = Playaz4Playaz/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 2.9;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = app.playaz4playaz.ios;
				PRODUCT_NAME = P4P;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match Appstore app.playaz4playaz.ios";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "Playaz4Playaz-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "Playaz4PlayazTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "Playaz4Playaz" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "Playaz4Playaz" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
