//
//  ReactViewController.m
//  Playaz4Playaz
//
//  Created by <PERSON> on 10/8/25.
//

#import "ReactViewController.h"
#import <React/RCTBundleURLProvider.h>
#import <RCTReactNativeFactory.h>
#import <RCTDefaultReactNativeFactoryDelegate.h>
#import <RCTAppDependencyProvider.h>


@interface ReactViewController ()

@end

@interface ReactNativeFactoryDelegate: RCTDefaultReactNativeFactoryDelegate
@end

@implementation ReactViewController {
  RCTReactNativeFactory *_factory;
  id<RCTReactNativeFactoryDelegate> _factoryDelegate;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
      _factoryDelegate = [ReactNativeFactoryDelegate new];
      _factoryDelegate.dependencyProvider = [RCTAppDependencyProvider new];
      _factory = [[RCTReactNativeFactory alloc] initWithDelegate:_factoryDelegate];
      self.view = [_factory.rootViewFactory viewWithModuleName:@"HelloWorld"];
}
@end

@implementation ReactNativeFactoryDelegate

- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge
{
  return [self bundleURL];
}

- (NSURL *)bundleURL
{
#if DEBUG
  return [RCTBundleURLProvider.sharedSettings jsBundleURLForBundleRoot:@"index"];
#else
  return [NSBundle.mainBundle URLForResource:@"main" withExtension:@"jsbundle"];
#endif
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
