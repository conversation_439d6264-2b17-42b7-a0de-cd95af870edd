diff --git a/node_modules/react-native-text-size/android/build.gradle b/node_modules/react-native-text-size/android/build.gradle
index 2617bd7..0e4943f 100644
--- a/node_modules/react-native-text-size/android/build.gradle
+++ b/node_modules/react-native-text-size/android/build.gradle
@@ -9,8 +9,8 @@ def safeExtGet(prop, fallback) {
 }
 
 def _buildToolsVersion  = safeExtGet('buildToolsVersion', '28.0.3')
-def _compileSdkVersion  = safeExtGet('compileSdkVersion', 28)
-def _targetSdkVersion   = safeExtGet('targetSdkVersion', 28)
+def _compileSdkVersion  = safeExtGet('compileSdkVersion', 35)
+def _targetSdkVersion   = safeExtGet('targetSdkVersion', 35)
 def _minSdkVersion      = safeExtGet('minSdkVersion', 16)
 
 buildscript {
diff --git a/node_modules/react-native-text-size/android/build/.transforms/08f1b881935d81ece3ca320c53cfc932/results.bin b/node_modules/react-native-text-size/android/build/.transforms/08f1b881935d81ece3ca320c53cfc932/results.bin
new file mode 100644
index 0000000..6e09b8c
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/.transforms/08f1b881935d81ece3ca320c53cfc932/results.bin
@@ -0,0 +1 @@
+o/out
diff --git a/node_modules/react-native-text-size/android/build/.transforms/08f1b881935d81ece3ca320c53cfc932/transformed/out/AndroidManifest.xml b/node_modules/react-native-text-size/android/build/.transforms/08f1b881935d81ece3ca320c53cfc932/transformed/out/AndroidManifest.xml
new file mode 100644
index 0000000..f9ee227
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/.transforms/08f1b881935d81ece3ca320c53cfc932/transformed/out/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.github.amarcruz.rntextsize" >
+
+    <uses-sdk android:minSdkVersion="16" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-text-size/android/build/.transforms/08f1b881935d81ece3ca320c53cfc932/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties b/node_modules/react-native-text-size/android/build/.transforms/08f1b881935d81ece3ca320c53cfc932/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
new file mode 100644
index 0000000..776557e
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/.transforms/08f1b881935d81ece3ca320c53cfc932/transformed/out/META-INF/com/android/build/gradle/aar-metadata.properties
@@ -0,0 +1,5 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/react-native-text-size/android/build/.transforms/08f1b881935d81ece3ca320c53cfc932/transformed/out/R.txt b/node_modules/react-native-text-size/android/build/.transforms/08f1b881935d81ece3ca320c53cfc932/transformed/out/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/.transforms/08f1b881935d81ece3ca320c53cfc932/transformed/out/jars/classes.jar b/node_modules/react-native-text-size/android/build/.transforms/08f1b881935d81ece3ca320c53cfc932/transformed/out/jars/classes.jar
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/.transforms/08f1b881935d81ece3ca320c53cfc932/transformed/out/jars/libs/R.jar b/node_modules/react-native-text-size/android/build/.transforms/08f1b881935d81ece3ca320c53cfc932/transformed/out/jars/libs/R.jar
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/.transforms/2ea3ff49db658506bd011df5ed655c0d/results.bin b/node_modules/react-native-text-size/android/build/.transforms/2ea3ff49db658506bd011df5ed655c0d/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/.transforms/2ea3ff49db658506bd011df5ed655c0d/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/react-native-text-size/android/build/.transforms/2ea3ff49db658506bd011df5ed655c0d/transformed/classes/classes_dex/classes.dex b/node_modules/react-native-text-size/android/build/.transforms/2ea3ff49db658506bd011df5ed655c0d/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/.transforms/9b6baf860afd8271473cff72335cba0c/results.bin b/node_modules/react-native-text-size/android/build/.transforms/9b6baf860afd8271473cff72335cba0c/results.bin
new file mode 100644
index 0000000..e3f0ff0
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/.transforms/9b6baf860afd8271473cff72335cba0c/results.bin
@@ -0,0 +1 @@
+i/classes_global-synthetics
diff --git a/node_modules/react-native-text-size/android/build/.transforms/b06446bee903483f8a85984837f6ef26/results.bin b/node_modules/react-native-text-size/android/build/.transforms/b06446bee903483f8a85984837f6ef26/results.bin
new file mode 100644
index 0000000..d945149
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/.transforms/b06446bee903483f8a85984837f6ef26/results.bin
@@ -0,0 +1 @@
+i/release_dex
diff --git a/node_modules/react-native-text-size/android/build/.transforms/b8f82fa463635fc005d5fb5210264621/results.bin b/node_modules/react-native-text-size/android/build/.transforms/b8f82fa463635fc005d5fb5210264621/results.bin
new file mode 100644
index 0000000..1ed65e0
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/.transforms/b8f82fa463635fc005d5fb5210264621/results.bin
@@ -0,0 +1 @@
+i/
diff --git a/node_modules/react-native-text-size/android/build/.transforms/cf80e63a4fe3a48df61a855701467036/results.bin b/node_modules/react-native-text-size/android/build/.transforms/cf80e63a4fe3a48df61a855701467036/results.bin
new file mode 100644
index 0000000..1ed65e0
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/.transforms/cf80e63a4fe3a48df61a855701467036/results.bin
@@ -0,0 +1 @@
+i/
diff --git a/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/results.bin b/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/results.bin
new file mode 100644
index 0000000..9e53e72
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/results.bin
@@ -0,0 +1 @@
+o/release
diff --git a/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/transformed/release/desugar_graph.bin b/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/transformed/release/desugar_graph.bin
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/transformed/release/release_dex/com/github/amarcruz/rntextsize/BuildConfig.dex b/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/transformed/release/release_dex/com/github/amarcruz/rntextsize/BuildConfig.dex
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/transformed/release/release_dex/com/github/amarcruz/rntextsize/RNTextSizeConf.dex b/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/transformed/release/release_dex/com/github/amarcruz/rntextsize/RNTextSizeConf.dex
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/transformed/release/release_dex/com/github/amarcruz/rntextsize/RNTextSizeModule.dex b/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/transformed/release/release_dex/com/github/amarcruz/rntextsize/RNTextSizeModule.dex
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/transformed/release/release_dex/com/github/amarcruz/rntextsize/RNTextSizePackage.dex b/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/transformed/release/release_dex/com/github/amarcruz/rntextsize/RNTextSizePackage.dex
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/transformed/release/release_dex/com/github/amarcruz/rntextsize/RNTextSizeSpannedText$CustomLetterSpacingSpan.dex b/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/transformed/release/release_dex/com/github/amarcruz/rntextsize/RNTextSizeSpannedText$CustomLetterSpacingSpan.dex
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/transformed/release/release_dex/com/github/amarcruz/rntextsize/RNTextSizeSpannedText$CustomStyleSpan.dex b/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/transformed/release/release_dex/com/github/amarcruz/rntextsize/RNTextSizeSpannedText$CustomStyleSpan.dex
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/transformed/release/release_dex/com/github/amarcruz/rntextsize/RNTextSizeSpannedText.dex b/node_modules/react-native-text-size/android/build/.transforms/ea31d60b99541a17379292356bd40140/transformed/release/release_dex/com/github/amarcruz/rntextsize/RNTextSizeSpannedText.dex
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/generated/source/buildConfig/release/com/github/amarcruz/rntextsize/BuildConfig.java b/node_modules/react-native-text-size/android/build/generated/source/buildConfig/release/com/github/amarcruz/rntextsize/BuildConfig.java
new file mode 100644
index 0000000..854dec0
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/generated/source/buildConfig/release/com/github/amarcruz/rntextsize/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.github.amarcruz.rntextsize;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = false;
+  public static final String LIBRARY_PACKAGE_NAME = "com.github.amarcruz.rntextsize";
+  public static final String BUILD_TYPE = "release";
+}
diff --git a/node_modules/react-native-text-size/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml b/node_modules/react-native-text-size/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..f9ee227
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.github.amarcruz.rntextsize" >
+
+    <uses-sdk android:minSdkVersion="16" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-text-size/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json b/node_modules/react-native-text-size/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
new file mode 100644
index 0000000..0dbde90
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/aapt_friendly_merged_manifests/release/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.github.amarcruz.rntextsize",
+  "variantName": "release",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-text-size/android/build/intermediates/aar_main_jar/release/classes.jar b/node_modules/react-native-text-size/android/build/intermediates/aar_main_jar/release/classes.jar
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/aar_metadata/release/aar-metadata.properties b/node_modules/react-native-text-size/android/build/intermediates/aar_metadata/release/aar-metadata.properties
new file mode 100644
index 0000000..776557e
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/aar_metadata/release/aar-metadata.properties
@@ -0,0 +1,5 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/react-native-text-size/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json b/node_modules/react-native-text-size/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/annotation_processor_list/release/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/react-native-text-size/android/build/intermediates/annotations_typedef_file/release/typedefs.txt b/node_modules/react-native-text-size/android/build/intermediates/annotations_typedef_file/release/typedefs.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/compile_library_classes_jar/release/classes.jar b/node_modules/react-native-text-size/android/build/intermediates/compile_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/compile_r_class_jar/release/R.jar b/node_modules/react-native-text-size/android/build/intermediates/compile_r_class_jar/release/R.jar
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/compile_symbol_list/release/R.txt b/node_modules/react-native-text-size/android/build/intermediates/compile_symbol_list/release/R.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/full_jar/release/full.jar b/node_modules/react-native-text-size/android/build/intermediates/full_jar/release/full.jar
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml b/node_modules/react-native-text-size/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
new file mode 100644
index 0000000..039c82a
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/incremental/mergeReleaseJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/jniLibs"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/release/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-text-size/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml b/node_modules/react-native-text-size/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
new file mode 100644
index 0000000..d694db9
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/incremental/mergeReleaseShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/shaders"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/release/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-text-size/android/build/intermediates/incremental/packageReleaseAssets/merger.xml b/node_modules/react-native-text-size/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
new file mode 100644
index 0000000..e85254f
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/incremental/packageReleaseAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/assets"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/release/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/build/intermediates/shader_assets/release/out"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-text-size/android/build/intermediates/incremental/release-mergeJavaRes/merge-state b/node_modules/react-native-text-size/android/build/intermediates/incremental/release-mergeJavaRes/merge-state
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/incremental/release/mergeReleaseResources/compile-file-map.properties b/node_modules/react-native-text-size/android/build/intermediates/incremental/release/mergeReleaseResources/compile-file-map.properties
new file mode 100644
index 0000000..a071b16
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/incremental/release/mergeReleaseResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Wed Apr 10 08:48:51 PST 2024
diff --git a/node_modules/react-native-text-size/android/build/intermediates/incremental/release/mergeReleaseResources/merger.xml b/node_modules/react-native-text-size/android/build/intermediates/incremental/release/mergeReleaseResources/merger.xml
new file mode 100644
index 0000000..2343fe8
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/incremental/release/mergeReleaseResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/build/generated/res/resValues/release"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-text-size/android/build/intermediates/incremental/release/packageReleaseResources/compile-file-map.properties b/node_modules/react-native-text-size/android/build/intermediates/incremental/release/packageReleaseResources/compile-file-map.properties
new file mode 100644
index 0000000..80d5d2d
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/incremental/release/packageReleaseResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Wed Apr 10 08:47:46 PST 2024
diff --git a/node_modules/react-native-text-size/android/build/intermediates/incremental/release/packageReleaseResources/merger.xml b/node_modules/react-native-text-size/android/build/intermediates/incremental/release/packageReleaseResources/merger.xml
new file mode 100644
index 0000000..2343fe8
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/incremental/release/packageReleaseResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/build/generated/res/resValues/release"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-text-size/android/build/intermediates/javac/release/classes/com/github/amarcruz/rntextsize/BuildConfig.class b/node_modules/react-native-text-size/android/build/intermediates/javac/release/classes/com/github/amarcruz/rntextsize/BuildConfig.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/javac/release/classes/com/github/amarcruz/rntextsize/RNTextSizeConf.class b/node_modules/react-native-text-size/android/build/intermediates/javac/release/classes/com/github/amarcruz/rntextsize/RNTextSizeConf.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/javac/release/classes/com/github/amarcruz/rntextsize/RNTextSizeModule.class b/node_modules/react-native-text-size/android/build/intermediates/javac/release/classes/com/github/amarcruz/rntextsize/RNTextSizeModule.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/javac/release/classes/com/github/amarcruz/rntextsize/RNTextSizePackage.class b/node_modules/react-native-text-size/android/build/intermediates/javac/release/classes/com/github/amarcruz/rntextsize/RNTextSizePackage.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/javac/release/classes/com/github/amarcruz/rntextsize/RNTextSizeSpannedText$CustomLetterSpacingSpan.class b/node_modules/react-native-text-size/android/build/intermediates/javac/release/classes/com/github/amarcruz/rntextsize/RNTextSizeSpannedText$CustomLetterSpacingSpan.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/javac/release/classes/com/github/amarcruz/rntextsize/RNTextSizeSpannedText$CustomStyleSpan.class b/node_modules/react-native-text-size/android/build/intermediates/javac/release/classes/com/github/amarcruz/rntextsize/RNTextSizeSpannedText$CustomStyleSpan.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/javac/release/classes/com/github/amarcruz/rntextsize/RNTextSizeSpannedText.class b/node_modules/react-native-text-size/android/build/intermediates/javac/release/classes/com/github/amarcruz/rntextsize/RNTextSizeSpannedText.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties b/node_modules/react-native-text-size/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties
new file mode 100644
index 0000000..0469975
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/lint_model_metadata/release/lint-model-metadata.properties
@@ -0,0 +1,2 @@
+mavenArtifactId=react-native-text-size
+mavenGroupId=Playaz4Playaz
\ No newline at end of file
diff --git a/node_modules/react-native-text-size/android/build/intermediates/local_aar_for_lint/release/out.aar b/node_modules/react-native-text-size/android/build/intermediates/local_aar_for_lint/release/out.aar
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/local_only_symbol_list/release/R-def.txt b/node_modules/react-native-text-size/android/build/intermediates/local_only_symbol_list/release/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/local_only_symbol_list/release/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/react-native-text-size/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt b/node_modules/react-native-text-size/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
new file mode 100644
index 0000000..d8346a4
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/manifest_merge_blame_file/release/manifest-merger-blame-release-report.txt
@@ -0,0 +1,7 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.github.amarcruz.rntextsize" >
+4
+5    <uses-sdk android:minSdkVersion="16" />
+6
+7</manifest>
diff --git a/node_modules/react-native-text-size/android/build/intermediates/merged_java_res/release/feature-react-native-text-size.jar b/node_modules/react-native-text-size/android/build/intermediates/merged_java_res/release/feature-react-native-text-size.jar
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/merged_manifest/release/AndroidManifest.xml b/node_modules/react-native-text-size/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
new file mode 100644
index 0000000..f9ee227
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/merged_manifest/release/AndroidManifest.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.github.amarcruz.rntextsize" >
+
+    <uses-sdk android:minSdkVersion="16" />
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-text-size/android/build/intermediates/navigation_json/release/navigation.json b/node_modules/react-native-text-size/android/build/intermediates/navigation_json/release/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/navigation_json/release/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/react-native-text-size/android/build/intermediates/runtime_library_classes_dir/release/com/github/amarcruz/rntextsize/BuildConfig.class b/node_modules/react-native-text-size/android/build/intermediates/runtime_library_classes_dir/release/com/github/amarcruz/rntextsize/BuildConfig.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/runtime_library_classes_dir/release/com/github/amarcruz/rntextsize/RNTextSizeConf.class b/node_modules/react-native-text-size/android/build/intermediates/runtime_library_classes_dir/release/com/github/amarcruz/rntextsize/RNTextSizeConf.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/runtime_library_classes_dir/release/com/github/amarcruz/rntextsize/RNTextSizeModule.class b/node_modules/react-native-text-size/android/build/intermediates/runtime_library_classes_dir/release/com/github/amarcruz/rntextsize/RNTextSizeModule.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/runtime_library_classes_dir/release/com/github/amarcruz/rntextsize/RNTextSizePackage.class b/node_modules/react-native-text-size/android/build/intermediates/runtime_library_classes_dir/release/com/github/amarcruz/rntextsize/RNTextSizePackage.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/runtime_library_classes_dir/release/com/github/amarcruz/rntextsize/RNTextSizeSpannedText$CustomLetterSpacingSpan.class b/node_modules/react-native-text-size/android/build/intermediates/runtime_library_classes_dir/release/com/github/amarcruz/rntextsize/RNTextSizeSpannedText$CustomLetterSpacingSpan.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/runtime_library_classes_dir/release/com/github/amarcruz/rntextsize/RNTextSizeSpannedText$CustomStyleSpan.class b/node_modules/react-native-text-size/android/build/intermediates/runtime_library_classes_dir/release/com/github/amarcruz/rntextsize/RNTextSizeSpannedText$CustomStyleSpan.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/runtime_library_classes_dir/release/com/github/amarcruz/rntextsize/RNTextSizeSpannedText.class b/node_modules/react-native-text-size/android/build/intermediates/runtime_library_classes_dir/release/com/github/amarcruz/rntextsize/RNTextSizeSpannedText.class
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/runtime_library_classes_jar/release/classes.jar b/node_modules/react-native-text-size/android/build/intermediates/runtime_library_classes_jar/release/classes.jar
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/intermediates/source_set_path_map/release/file-map.txt b/node_modules/react-native-text-size/android/build/intermediates/source_set_path_map/release/file-map.txt
new file mode 100644
index 0000000..4992151
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/source_set_path_map/release/file-map.txt
@@ -0,0 +1,7 @@
+com.github.amarcruz.rntextsize.react-native-text-size-pngs-0 /Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/build/generated/res/pngs/release
+com.github.amarcruz.rntextsize.react-native-text-size-resValues-1 /Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/build/generated/res/resValues/release
+com.github.amarcruz.rntextsize.react-native-text-size-mergeReleaseResources-2 /Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/build/intermediates/incremental/release/mergeReleaseResources/merged.dir
+com.github.amarcruz.rntextsize.react-native-text-size-mergeReleaseResources-3 /Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/build/intermediates/incremental/release/mergeReleaseResources/stripped.dir
+com.github.amarcruz.rntextsize.react-native-text-size-merged_res-4 /Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/build/intermediates/merged_res/release
+com.github.amarcruz.rntextsize.react-native-text-size-main-5 /Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/res
+com.github.amarcruz.rntextsize.react-native-text-size-release-6 /Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/release/res
diff --git a/node_modules/react-native-text-size/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt b/node_modules/react-native-text-size/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
new file mode 100644
index 0000000..be39d13
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/intermediates/symbol_list_with_package_name/release/package-aware-r.txt
@@ -0,0 +1 @@
+com.github.amarcruz.rntextsize
diff --git a/node_modules/react-native-text-size/android/build/outputs/aar/react-native-text-size-release.aar b/node_modules/react-native-text-size/android/build/outputs/aar/react-native-text-size-release.aar
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-text-size/android/build/outputs/logs/manifest-merger-release-report.txt b/node_modules/react-native-text-size/android/build/outputs/logs/manifest-merger-release-report.txt
new file mode 100644
index 0000000..f4537f2
--- /dev/null
+++ b/node_modules/react-native-text-size/android/build/outputs/logs/manifest-merger-release-report.txt
@@ -0,0 +1,17 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/AndroidManifest.xml:2:1-4:12
+INJECTED from /Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/AndroidManifest.xml:2:1-4:12
+	package
+		ADDED from /Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/AndroidManifest.xml:3:11-51
+		INJECTED from /Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/AndroidManifest.xml:2:11-69
+uses-sdk
+INJECTED from /Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/P4P/p4pv2-tooltwist/mobile/node_modules/react-native-text-size/android/src/main/AndroidManifest.xml
diff --git a/node_modules/react-native-text-size/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin b/node_modules/react-native-text-size/android/build/tmp/compileReleaseJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..e69de29
