import {
  LoginManager,
  AccessToken,
  GraphRequest,
  GraphRequestManager
} from 'react-native-fbsdk-next'
import analytics from '@react-native-firebase/analytics'
import { Api } from 'lib'
import { store } from 'store'
import { storage } from 'core'

async function loginWithFb() {

  const result = await LoginManager.logInWithPermissions(['public_profile', 'email'])

  if (result.isCancelled) return;

  const data = await AccessToken.getCurrentAccessToken();
  if (!data) return

  const infoRequest = new GraphRequest(
    // add data fields we want from FB here
    '/me',
    {
      parameters: {
        fields: { string: 'id, email, name, picture.type(large)' }
      }
    },
    async (err, fbData) => {
      try {
        const { email, id, name, picture } = fbData

        const res = await Api.post(
          '/users/fbLogin',
          { email, id, name, avatar: picture.data.url },
          'navigator'
        )
        if (!res) return

        await storage.setItem('token', res.token)

        res.created
          ? analytics().logSignUp({ method: 'google' })
          : analytics().logLogin({ method: 'google' })

        store.dispatch({
          type: 'SET_USER',
          payload: res.user
        })
      } catch (e) {
        console.log('error', e)
      }
    })

  new GraphRequestManager().addRequest(infoRequest).start();

}

export default loginWithFb
