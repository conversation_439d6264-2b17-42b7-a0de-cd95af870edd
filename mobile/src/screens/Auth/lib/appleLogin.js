import { appleAuth } from '@invertase/react-native-apple-authentication'
import analytics from '@react-native-firebase/analytics'
import { Api } from 'lib'
import { store } from 'store'
import { storage } from 'core'

async function appleLogin() {
  const appleAuthRequestResponse = await appleAuth
    .performRequest({
      requestedOperation: appleAuth.Operation.LOGIN,
      requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL]
    })
    .then(res => res)
    .catch(err => false)

  if(!appleAuthRequestResponse) return

  const {
    user,
    email,
    fullName: { givenName, familyName }
  } = appleAuthRequestResponse

  store.dispatch({ type: 'LOADING_BUTTON', payload: 'navigatior' })

  // NO USER
  if(!appleAuthRequestResponse.user) return store.dispatch({ type: 'LOADING_BUTTON'})

  const credentialState = await appleAuth.getCredentialStateForUser(appleAuthRequestResponse.user)
  store.dispatch({ type: 'LOADING_BUTTON'})

  if(credentialState !== appleAuth.State.AUTHORIZED) return

  const name = `${givenName || ''} ${familyName || ''}`.trim()

  const res = await Api.post(
    '/users/appleLogin',
    { id: user, name, email },
    'navigator'
  )
  if(!res) return

  await storage.setItem('token', res.token)

  res.created 
    ? analytics().logSignUp({ method: 'google' })
    : analytics().logLogin({ method: 'google' })

  store.dispatch({
    type: 'SET_USER',
    payload: res.user
  })
}

export default appleLogin
