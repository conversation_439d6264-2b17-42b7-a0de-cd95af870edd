import { GoogleSignin } from '@react-native-google-signin/google-signin'
import analytics from '@react-native-firebase/analytics'
import { Api } from 'lib'
import { store } from 'store'
import { storage } from 'core'

async function ggLogin() {
  GoogleSignin.configure()

  try {
    await GoogleSignin.hasPlayServices()
    const info = await GoogleSignin.signIn()

    const { email, id, name, photo } = info.data.user

    const res = await Api.post(
      '/users/ggLogin',
      { email, id, name, avatar: photo },
      'navigator'
    )

    if(!res) return

    await storage.setItem('token', res.token)
    
    res.created 
      ? analytics().logSignUp({ method: 'google' })
      : analytics().logLogin({ method: 'google' })

    store.dispatch({
      type: 'SET_USER',
      payload: res.user
    })
  } catch (error) {
    error?.response?.data && alert(error.response.data)
  }
}

export default ggLogin
