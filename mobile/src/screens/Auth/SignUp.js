import React, { useState, useRef, useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { Dimensions, TouchableOpacity } from 'react-native'
import { useNavigation, useRoute } from '@react-navigation/native'
import analytics from '@react-native-firebase/analytics'
import { validateEmail, Api } from 'lib'
import { storage, isNewIphone } from 'core'
import { Text, Col, Button, Input, Clickable, Icon } from 'components'
import { AuthContainer, PrivacyAndTerms } from './components'
import { fbLogin, ggLogin, appleLogin } from './lib'

const SignUp = ({  }) => {
  const [name, setName] = useState(''),
    [email, setEmail] = useState(''),
    [emailErr, setEmailErr] = useState(null),
    [password, setPassword] = useState(''),
    nameRef = useRef(null),
    passwordRef = useRef(null),
    scrollRef = useRef(null),
    { language } = useSelector(state => state.appState),
    buttonDisabled = password.length < 8 || name.length < 1 || !validateEmail(email),
    dispatch = useDispatch(),
    navigation = useNavigation(),
    deviceHt = Dimensions.get('screen').height,
    scrollable = deviceHt <= 820,
    { t } = useTranslation(),
    params = useRoute().params

  // SCROLL TO TOP ON KEYBOARD OPEN,
  // USEFULL ESPECIALLY FOR SMALLER DEVICES
  function handleFocus() {
    setTimeout(() => {
      scrollRef.current.scrollTo({ x: 0, y: isNewIphone() ? 180 : 194 })
    }, 100)
  }

  async function signUp() {
    const res = await Api.post(
      '/users/signUp',
      { name, email, password, code: params?.code, lang: language },
      'signUp'
    )
    if(!res) return

    if(res.err && res.input === 'email') {
      return setEmailErr(res.err)
    }

    storage.setItem('token', res.token)

    analytics().logSignUp({ method: 'email' })

    dispatch({
      type: 'SET_USER',
      payload: res.user
    })
  }

  function handleEmailChange(val) {
    setEmail(val)
    setEmailErr(null)
  }

  const featureUnavailable = useCallback(() => {
    dispatch({
      type: 'SHOW_COMING_SOON',
      payload: { 
        comingSoonDetails: {
            title: 'Facebook',
            visible: true,
        }
      },
    })
  })

  const handleGoBack = () => {
    navigation.goBack();
    dispatch({
      type: 'UPDATE_LINK_ACTION',
      payload: '',
    });
  }

  return (
    <AuthContainer scrollRef={scrollRef}>
      <Col between>
        <Col>

          <TouchableOpacity style={{ marginLeft: 6, marginTop: 16, padding: 16, paddingLeft: 0 }} onPress={handleGoBack}>
            <Icon type={'arrowLeft'} dimensions={16} col={'#000'} />
          </TouchableOpacity>

          <Text h2 lineHt="36px" marg="12px 0 0" align="center">
            {t('common.signUp')}
          </Text>

          <Input
            value={email}
            error={emailErr}
            autoCapitalize='none'
            autoComplete='off'
            autoCorrect={false}
            onSubmitEditing={() => nameRef.current.focus()}
            placeholder={t('common.email')}
            onChangeText={handleEmailChange}
            hasRemove
            marg='24px 0 0'
            keyboardType='email-address'
            onFocus={handleFocus}
          />

          <Input
            value={name}
            inputRef={nameRef}
            onSubmitEditing={() => passwordRef.current.focus()}
            placeholder={t('common.fullName')}
            onChangeText={(val) => setName(val)}
            hasRemove
            marg='28px 0 0'
            onFocus={handleFocus}
          />

          <Input
            value={password}
            error={!!password.length && password.length < 8 && t('auth.mustContain8Characters')}
            autoComplete='off'
            autoCorrect={false}
            inputRef={passwordRef}
            placeholder={t('common.password')}
            onChangeText={(val) => setPassword(val)}
            password
            marg='28px 0 0'
            onFocus={handleFocus}
          />

          <Button
            disabled={buttonDisabled}
            onClick={signUp}
            action='signUp'
            big
            text={t('common.signUp')}
            marg='28px 0 0'
          />

          <Text b1 marg='16px 0 0' align='center' lineHt='22px'>
            {t('common.or')}
          </Text>

          <Button
            facebook
            big
            text={t('auth.signUpFacebook')}
            marg='16px 0 0'
            onClick={featureUnavailable}
          />

          <Button
            google
            big
            text={t('auth.signUpGoogle')}
            marg='16px 0 0'
            onClick={ggLogin}
          />

          {Platform.OS === 'ios' && (
            <Button
              apple
              marg='16px 0 0'
              text={t('auth.signUpApple')}
              onClick={appleLogin}
            />
          )}

          <Col endAll pad={scrollable && '20px 0 10px'}>
            <PrivacyAndTerms />

            <Clickable onClick={() => navigation.navigate('Login')}>
              <Text align='center'>
                {t('auth.alreadyHaveAccount')} <Text bold>{t('common.login')}</Text>
              </Text>
            </Clickable>
          </Col>
        </Col>
      </Col>
    </AuthContainer>
  )
}

export default SignUp
