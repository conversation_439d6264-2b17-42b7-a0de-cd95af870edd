import React, { useState, useRef, useCallback, useMemo, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { Dimensions, Platform, StyleSheet, View } from 'react-native'
import { useNavigation, useRoute } from '@react-navigation/native'
import analytics from '@react-native-firebase/analytics'
import { validateEmail, Api, theme } from 'lib'
import { storage, isNewIphone } from 'core'
import { Text, Col, Button, Input, Clickable, Row, Divider, Modal, Icon } from 'components'
import { PrivacyAndTerms } from './components'
import { fbLogin, ggLogin, appleLogin } from './lib'
import CTATopSection from './components/CTATopSection'
import styled from 'styled-components'

const Logo = styled.Image`
  height: 32px;
  width: 164px;
`

const MEDIA_SHARE = ['post', 'shoutout', 'view'];

const SignUp = () => {
    const [name, setName] = useState(''),
        [email, setEmail] = useState(''),
        [emailErr, setEmailErr] = useState(null),
        [password, setPassword] = useState(''),
        nameRef = useRef(null),
        passwordRef = useRef(null),
        { language } = useSelector(state => state.appState),
        buttonDisabled = password.length < 8 || name.length < 1 || !validateEmail(email),
        dispatch = useDispatch(),
        navigation = useNavigation(),
        { t } = useTranslation(),
        params = useRoute().params
    const desiredHt = Dimensions.get('screen').height - 194
    const hasNotch = isNewIphone()
    const [showSignUpModal, setShowSignUpModal] = useState(false)
    const isMediaShare = MEDIA_SHARE.includes(params?.ctaAction?.toLowerCase());
   

    async function signUp() {
        const res = await Api.post(
            '/users/signUp',
            { name, email, password, code: params?.code, lang: language },
            'signUp'
        )
        if (!res) return

        if (res.err && res.input === 'email') {
            return setEmailErr(res.err)
        }

        storage.setItem('token', res.token)

        // analytics().logSignUp({ method: 'email' })

        const signUpSource = isMediaShare ? 'sign_up_from_media_share' : 'sign_up_from_cta_share';
        await analytics().logEvent(signUpSource, {
            type: params?.ctaAction,
        });


        dispatch({
            type: 'SET_USER',
            payload: res.user
        })
    }

    function handleEmailChange(val) {
        setEmail(val)
        setEmailErr(null)
    }

    const featureUnavailable = useCallback(() => {
        dispatch({
            type: 'SHOW_COMING_SOON',
            payload: {
                comingSoonDetails: {
                    title: 'Facebook',
                    visible: true,
                }
            },
        })
    })

    const signUpModal = () => {
        return (
            <Modal
                visible={showSignUpModal}
                transparent={true}
                closeModal={() => setShowSignUpModal(false)}
                avoidKeyboard
            >
                <Col noFlex pad={hasNotch ? '0px 25px 0px 25px' : '0px 25px 25px 25px'}>
                    <Text
                        h3
                        lineHt='36px'
                        marg='24px 0 0'
                        align='center'
                    >
                        {t('common.signUp')}
                    </Text>

                    <Input
                        value={email}
                        error={emailErr}
                        autoCapitalize='none'
                        autoComplete='off'
                        autoCorrect={false}
                        onSubmitEditing={() => nameRef.current.focus()}
                        placeholder={t('common.email')}
                        onChangeText={handleEmailChange}
                        hasRemove
                        marg='24px 0 0'
                        keyboardType='email-address'
                    />

                    <Input
                        value={name}
                        inputRef={nameRef}
                        onSubmitEditing={() => passwordRef.current.focus()}
                        placeholder={t('common.fullName')}
                        onChangeText={(val) => setName(val)}
                        hasRemove
                        marg='28px 0 0'
                    />

                    <Input
                        value={password}
                        error={!!password.length && password.length < 8 && t('auth.mustContain8Characters')}
                        autoComplete='off'
                        autoCorrect={false}
                        inputRef={passwordRef}
                        placeholder={t('common.password')}
                        onChangeText={(val) => setPassword(val)}
                        password
                        marg='28px 0 0'
                    />

                    <Button
                        disabled={buttonDisabled}
                        onClick={signUp}
                        action='signUp'
                        big
                        text={t('common.signUp')}
                        marg='28px 0 0'
                    />
                </Col>
            </Modal>

        )

    }

    return (

        <Col bg={theme.GREY_10}>
            <CTATopSection params={params} />

            <Col
                bg='#fff'
                noFlex
                minHt={`${desiredHt}px`}
            >
                <Col marg={'25px 0 0 0'}>
                    <Col pad={'0px 25px 0px 25px'}>
                        <Button
                            facebook
                            big
                            text={t('auth.signUpFacebook')}
                            marg='16px 0 0'
                            onClick={featureUnavailable}
                        />

                        <Button
                            google
                            big
                            text={t('auth.signUpGoogle')}
                            marg='16px 0 0'
                            onClick={ggLogin}
                        />

                        {Platform.OS === 'ios' && (
                            <Button
                                apple
                                marg='16px 0 0'
                                text={t('auth.signUpApple')}
                                onClick={appleLogin}
                            />
                        )}
                        <Row noFlex marg={'20px 0px 20px 0px'} centerAll >
                            <Divider style={{ flex: 1 }} />
                            <Text marg={'0px 10px 0px 10px'}>{t('common.or')}</Text>
                            <Divider style={{ flex: 1 }} />
                        </Row>

                        <Button
                            onClick={() => setShowSignUpModal(true)}
                            action='signUp'
                            big
                            text={t('common.signUp')}

                            bg={theme.SECONDARY}
                        />
                        <Col marg={'20px 0 0 0'}>
                            <PrivacyAndTerms />

                            <Clickable onClick={() => navigation.navigate('CTALogin', { ...params })}>
                                <Text align='center'>
                                    {t('auth.alreadyHaveAccount')} <Text bold>{t('common.login')}</Text>
                                </Text>
                            </Clickable>




                            <View style={styles.logoContainer}>
                                <Logo
                                    source={require('assets/branding/logo-black.png')}
                                    resizeMode='contain'
                                />
                            </View>

                        </Col>
                    </Col>

                </Col>

            </Col>

            {showSignUpModal && signUpModal()}
        </Col >

    )
}


export default SignUp

const styles = StyleSheet.create({
    logoContainer: {
        justifyContent: 'center',
        alignContent: 'center',
        alignItems: 'center',
        marginTop: 25
    }
})