import React, { useState } from 'react'
import { theme } from 'lib'
import { Col, Row, Icon, Text } from 'components'

const LoginAttempts = ({ attempts }) => {
  if(!attempts) return null

  return (
    <Col
      ht='40px'
      noFlex
      bg={theme.SECONDARY_10}
      marg='8px 0 0'
    >
      <Row pad='8px'>
        <Icon
          dimensions={22}
          type='warning'
          marg='0 9px 0 0'
          col={theme.SECONDARY}
        />

        <Text col={theme.SECONDARY}>
          {3 - attempts} attempts left to lock your account
        </Text>
      </Row>
    </Col>
  )
}

export default LoginAttempts
