import React, { useState } from 'react'
import { ScrollView, Dimensions, KeyboardAvoidingView, Platform } from 'react-native'
import { theme } from 'lib'
import { Text, Col, Button, Input } from 'components'
import { isNewIphone } from 'core'

import { TopSection } from './'

const SignUp = ({ children, scrollRef }) => {
  const hasNotch = isNewIphone()
  const isAndroid = Platform.OS === 'android'
  const desiredHt = Dimensions.get('screen').height - 194

  return (
    <Col bg={theme.GREY_10}>
      <TopSection />

      <KeyboardAvoidingView
        behavior={isAndroid ? 'height' : 'padding'}
        bounces={false}
      >
        <ScrollView
          ref={scrollRef}
          contentContainerStyle={{ backgroundColor: 'transparent' }}
          keyboardShouldPersistTaps='handled'
        >
          <Col noFlex ht='194px' bg='transparent' />

          <Col
            bg='#fff'
            noFlex
            pad={hasNotch ? '0 16px 30px' : '0 16px 8px'}
            minHt={`${desiredHt}px`}
          >
            {children}
          </Col>
        </ScrollView>
      </KeyboardAvoidingView>
    </Col>
  )
}

export default SignUp
