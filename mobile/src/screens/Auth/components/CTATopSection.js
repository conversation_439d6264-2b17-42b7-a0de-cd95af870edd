import React, { useState, useMemo } from 'react'
import { View, StyleSheet, TouchableOpacity } from 'react-native'
import styled from 'styled-components'
import { ScrollView } from 'react-native'
import { theme } from 'lib'
import { Text, Icon } from 'components'
import LinearGradient from 'react-native-linear-gradient'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { useNavigation } from '@react-navigation/native'


const Image = styled.ImageBackground`
background-color: ${props => props.noAvatar ? theme.TALE : '#fff'};
border-radius: 100px;
margin:  4px;
height: 100px;
width:  100px;
overflow: hidden;
align-items: center;
justify-content: center;
`

const CTATopSection = ({ params }) => {

    const { avatar, username, name, type } = params.ctaUserData || params
    const headerHeight = useSafeAreaInsets().top + 20
    const navigation = useNavigation()

    const isAthlete = type === 'athlete'
    const isTeam = type === 'team'

    const noAvatar = !avatar

    function getColor() {
        if (isTeam) return theme.DARK_PURPLE
        if (isAthlete) return theme.SECONDARY
        return theme.DARK_BLUE
    }


    function getText() {
        const splitted = name?.split(' '),
            initials = splitted && `${splitted[0] && splitted[0][0].toUpperCase()}${splitted[1] && splitted[1][0].toUpperCase()}`.replace('undefined', '')

        return (
            <Text h1>{initials}</Text>
        )
    }

    const styles = useMemo(() => ({
        gradientBackground: {
            paddingTop: headerHeight,
            height: 270
        },
        nameAvatarRow: {
            flexDirection: 'row',
            width: '100%',
            padding: 10
        },
        nameRow: {
            flexDirection: 'row',
            alignItems: 'flex-start',
            flex: 1
        },
        avatar: {
            borderRadius: 100,
            height: 120,
            width: 120,
        },
        contentContainer: {
            alignItems: 'center',
            justifyContent: 'center',
        }

    }), [headerHeight])

    const handleGoBack = () => {
        navigation.goBack();
        dispatch({
            type: 'UPDATE_LINK_ACTION',
            payload: '',
        });
    }


    return (
        <LinearGradient
            colors={[theme.PRIMARY_BLACK, getColor()]}
            start={{ x: 0, y: 0.5 }}
            end={{ x: 0, y: 0 }}
            style={styles.gradientBackground}>
            <TouchableOpacity style={{ position: 'absolute', top: headerHeight, left: 10, padding: 10, zIndex: 1 }} onPress={handleGoBack}>
                <Icon type={'arrowLeft'} dimensions={16} col={'#FFF'} />
            </TouchableOpacity>
            <View style={styles.contentContainer}>
                <Image
                    resizeMode='cover'
                    source={{ uri: avatar || null }}
                    noAvatar={noAvatar}
                    key={avatar}
                >
                    {noAvatar && getText()}
                </Image>

                <Text h3 col={'#fff'}>{name}</Text>
                <Text h7 col={'#fff'}>@{username}</Text>

            </View>

        </LinearGradient>
    )
}

export default CTATopSection
