import React, { useState } from 'react'
import { View } from 'react-native'
import styled from 'styled-components'
import { ScrollView } from 'react-native'
import { theme } from 'lib'
import { Text } from 'components'

const Container = styled.View`
  height: 194px;
  width: 100%;
  background-color: ${theme.GREY_10};
  position: absolute;
`

const IconBg = styled.Image`
  height: 121px;
  width: 121px;
  align-self: center;
  top: 133px;
  position: absolute;
`

const TopSection = ({ sectionRef }) => {

  return (
    <Container>
      <View>
        <IconBg
          source={require('assets/branding/logo-icon-grey-20.png')}
          resizeMode='contain'
        />
      </View>
    </Container>
  )
}

export default TopSection
