import React, { useState } from 'react'
import { PrivacyPolicy, TermsAndConditions, Row, Text, Clickable } from 'components'

/*
  TODO!: Should review handling localization here,
  since we have clickable texts
*/

const PrivacyAndTerms = ({  }) => {
  const [privacyVisible, setPrivacyVisible] = useState(false),
    [termsVisible, setTermsVisible] = useState(false)

  function handlePrivacyVisible() {
    setPrivacyVisible(!privacyVisible)
  }

  function handleTermsVisible() {
    setTermsVisible(!termsVisible)
  }

  return (
    <React.Fragment>
      <Row wrap noFlex marg='0 0 36px' centerAll>
        <Text>
          By registering or logging in, you agree to our
        </Text>

        <Clickable
          hitSlop={10}
          onClick={handleTermsVisible}
        >
          <Text bold> Terms and Conditions</Text>
        </Clickable>

        <Text> and </Text>

        <Clickable
          onClick={handlePrivacyVisible}
          hitSlop={10}
        >
          <Text bold>Privacy Policy</Text>
        </Clickable>
      </Row>

      <PrivacyPolicy
        visible={privacyVisible}
        closeModal={handlePrivacyVisible}
      />

      <TermsAndConditions
        visible={termsVisible}
        closeModal={handleTermsVisible}
      />
    </React.Fragment>
  )
}

export default PrivacyAndTerms
