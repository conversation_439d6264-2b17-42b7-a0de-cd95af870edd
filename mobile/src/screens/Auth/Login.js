import React, { useState, useRef, useCallback } from 'react'
import { useDispatch } from 'react-redux'
import { Dimensions, Platform, TouchableOpacity } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import analytics from '@react-native-firebase/analytics'
import { validateEmail, Api, theme } from 'lib'
import { storage, isNewIphone } from 'core'
import { Text, Col, Button, Input, Clickable, Icon } from 'components'
import { AuthContainer, LoginAttempts, PrivacyAndTerms } from './components'
import { useTranslation } from 'react-i18next'
import { fbLogin, ggLogin, appleLogin } from './lib'

const Login = ({  }) => {
  const [email, setEmail] = useState(''),
    [password, setPassword] = useState(''),
    [attempts, setAttempts] = useState(0),
    [passErr, setPassErr] = useState(null),
    [err, setErr] = useState(null),
    passwordRef = useRef(null),
    scrollRef = useRef(null),
    buttonDisabled = !password.length || !validateEmail(email),
    dispatch = useDispatch(),
    navigation = useNavigation(),
    deviceHt = Dimensions.get('screen').height,
    scrollable = deviceHt <= 820,
    { t } = useTranslation()
    
  /*
    Scroll to top when keyboard opens,
    usefull especially for smaller devices
  */
  function handleFocus() {
    setTimeout(() => {
      scrollRef.current?.scrollTo({ x: 0, y: isNewIphone() ? 180 : 194 })
    }, 100)
  }

  async function login() {
    const res = await Api.post(
      '/users/login',
      { email, password },
      'login'
    )

    if(!res) return

    if(res.pwErr) {
      setPassErr(res.pwErr)
      setAttempts(res.loginAttempts)

      return
    }

    if(res.locked) {
      setAttempts(3)
      navigation.navigate('AccountLocked')

      return
    }

    if(res.err) return setErr(res.err)

    if(res.migratedUser) {
      return navigation.navigate('MigratedUser', { email })
    }

    await storage.setItem('token', res.token)

    analytics().logLogin({ method: 'email' })

    dispatch({
      type: 'SET_USER',
      payload: res.user
    })
  }

  const featureUnavailable = useCallback(() => {
    dispatch({
      type: 'SHOW_COMING_SOON',
      payload: { 
        comingSoonDetails: {
            title: 'Facebook',
            visible: true,
        }
      },
    })
  })

  const handleGoBack = () => {
    navigation.goBack();
    dispatch({
      type: 'UPDATE_LINK_ACTION',
      payload: '',
    });
  }

  return (
    <AuthContainer scrollRef={scrollRef}>
      <Col between>

        <TouchableOpacity style={{ marginLeft: 6, marginTop: 16, padding: 16, paddingLeft: 0 }} onPress={handleGoBack}>
          <Icon type={'arrowLeft'} dimensions={16} col={'#000'} />
        </TouchableOpacity>

        <Col>
          <Text
            h2
            lineHt='36px'
            marg='12px 0 0'
            align='center'
          >
            {t('common.login')}
          </Text>

          {err && (
            <Text c1 bold col={theme.SECONDARY} align='center' marg='16px 0 0'>
              {err}
            </Text>
          )}

          <Input
            value={email}
            autoCapitalize='none'
            autoComplete='off'
            autoCorrect={false}
            onSubmitEditing={() => passwordRef.current.focus()}
            placeholder={t('common.email')}
            onChangeText={(val) => setEmail(val)}
            hasRemove
            marg='50px 0 0'
            keyboardType='email-address'
            onFocus={handleFocus}
          />

          <Input
            autoComplete='off'
            autoCorrect={false}
            inputRef={passwordRef}
            value={password}
            error={passErr}
            placeholder={t('common.password')}
            onChangeText={(val) => setPassword(val)}
            password
            marg='28px 0 0'
            onFocus={handleFocus}
          />

          <LoginAttempts attempts={attempts} />

          <Button
            disabled={buttonDisabled}
            onClick={login}
            action='login'
            big
            text={t('common.login')}
            marg='28px 0 0'
          />

          <Clickable
            onClick={() => navigation.navigate('ForgotPassword', { email })}
          >
            <Text
              bold
              align='center'
              lineHt='22px'
              marg='24px 0 0'
            >
              {t('auth.forgottenPassword')}
            </Text>
          </Clickable>

          <Text b1 marg='16px 0 0' align='center' lineHt='22px'>
            {t('common.or')}
          </Text>

          <Button
            onClick={featureUnavailable}
            facebook
            big
            marg='16px 0 0'
            text={t('auth.logInFacebook')}
          />

          <Button
            google
            big
            marg='16px 0 0'
            text={t('auth.logInGoogle')}
            onClick={ggLogin}
          />

          {Platform.OS === 'ios' && (
            <Button
              apple
              marg='16px 0 0'
              text={t('auth.logInApple')}
              onClick={appleLogin}
            />
          )}

          <Col endAll pad={scrollable && '20px 0 10px'}>
            <Col marg='24px 0 0'>
              <PrivacyAndTerms />
            </Col>

            <Clickable onClick={() => navigation.navigate('SignUp')}>
              <Text align='center'>
                {t('auth.dontHaveAccount')} <Text bold>{t('common.signUp')}</Text>
              </Text>
            </Clickable>
          </Col>
        </Col>
      </Col>
    </AuthContainer>
  )
}

export default Login
