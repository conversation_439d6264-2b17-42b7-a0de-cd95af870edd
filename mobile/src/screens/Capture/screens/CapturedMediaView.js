import React, { useState, useEffect } from 'react'
import { Dimensions, Platform } from 'react-native'
import { useTranslation } from 'react-i18next'
import { useRoute, useNavigationState } from '@react-navigation/native'
import mime from 'mime'
import { mediaCompressor } from 'core'
import { Header, Col, Row, Text, Clickable, Icon, Spinner } from 'components'
import { Video, Image } from '../components'

const CapturedMediaView = ({ navigation }) => {
  const [loading, setLoading] = useState(false),
    [screenFocused, setScreenFocused] = useState(false),
    deviceWid = Dimensions.get('window').width,
    { type, captured, entityParams, notAddScreen } = useRoute()?.params || {},
    routes = useNavigationState(state => state.routes),
    { t } = useTranslation(),
    isVideo = captured?.path,
    isAndroid = Platform.OS === 'android',
    aspectRatio = type === 'post' ? 3 / 4 : 9 / 16

  useEffect(() => {
    const focusListener = navigation.addListener('focus', () => {
      setScreenFocused(true)
    })

    const blurListener = navigation.addListener('blur', () => {
      setScreenFocused(false)
    })

    return focusListener, blurListener
  }, [])

  async function handleNavigation(navigateScreen){
    if (isVideo) {
      navigation.navigate(navigateScreen, {
        screen: 'Post',
        cameraMedia: {
          isVideo: true,
          uri: captured.path,
          realPath: isAndroid ? `file://${captured.path}` : captured.path,
          index: 0
        },
        merge: true
      })
    } else {
      const imgPath = await mediaCompressor.getPath(captured, 'image'),
        compressed = await mediaCompressor.image(imgPath, 1080, 1080, 0.7)

      navigation.navigate(navigateScreen, {
        screen: 'Post',
        cameraMedia: {
          uri: compressed,
          type: mime.getType(compressed),
          index: 0
        },
        merge: true
      })
    }
  }

  async function handleNext() {
    setLoading(true)

    switch (type) {
      case 'post':
        handleNavigation('Create')
        break
      case 'startChat':
        handleNavigation('StartChat')
        break
      case 'shoutout':
        const screen = !notAddScreen ? 'CreateMatchDayShoutout' : 'AddShoutout'

        if (isVideo) {
          navigation.navigate(screen, {
            cameraMedia: {
              isVideo: true,
              uri: captured.path,
              realPath: isAndroid ? `file://${captured.path}` : captured.path,
              index: 0
            },
            merge: true
          })
        } else {
          const imgPath = await mediaCompressor.getPath(captured, 'image'),
            compressed = await mediaCompressor.image(imgPath, 1080, 1080, 0.7)

          navigation.navigate(screen, {
            cameraMedia: {
              uri: compressed,
              type: mime.getType(compressed),
              index: 0
            },
            merge: true
          })
        }
        break
      default:
    }

    setLoading(false)
  }

  return (
    <Col bg='#000'>
      {loading && <Spinner fullScreen />}

      <Header
        transparent
        lightContent
      >
        <Col centerAll>
          <Row wid='100%' between centerAll bg='rgba(0, 0, 0, 0.5)'>
            <Clickable
              wid='33%'
              pad='0 16px'
              onClick={() => navigation.goBack()}
            >
              <Icon type='arrowLeft' dimensions={16} col='#fff' />
            </Clickable>

            <Col centerAll wid='33%'>
              <Text h5 center col='#fff'>
                {t('common.addNew')}
              </Text>
            </Col>


            <Row wid='33%'>
              <Col></Col>
              <Clickable
                pad='0 16px'
                onClick={handleNext}
              >
                <Text bold col='#fff'>
                  {t('common.next')}
                </Text>
              </Clickable>
            </Row>
          </Row>
        </Col>
      </Header>

      <Col centerAll>
        {isVideo ? (
          <Video
            source={captured.path}
            deviceWid={deviceWid}
            aspectRatio={aspectRatio}
            focused={screenFocused}
          />
        ) : (
          <Image
            source={captured}
            deviceWid={deviceWid}
            aspectRatio={aspectRatio}
          />
        )}
      </Col>
    </Col>
  )
}

export default CapturedMediaView
