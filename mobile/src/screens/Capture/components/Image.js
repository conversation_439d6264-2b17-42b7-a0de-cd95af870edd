import React, { useState } from 'react'
import { getTopPadding } from 'core'
import { Image } from 'react-native'

const CapturedImage = ({ source, deviceWid, aspectRatio }) => {

  return (
    <Image
      source={{ uri: `file://${source}` }}
      style={{
        height: undefined,
        width: deviceWid,
        aspectRatio: aspectRatio || 9/16,
        marginTop: getTopPadding(true),
        position: 'absolute'
      }}
    />
  )
}

export default CapturedImage
