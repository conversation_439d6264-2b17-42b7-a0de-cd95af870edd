import React, { useState } from 'react'
import { Pressable } from 'react-native'
import { getTopPadding } from 'core'
import Video from 'react-native-video'

const CapturedVideo = ({ source, deviceWid, aspectRatio, focused }) => {
  
  return (
    <Video
      resizeMode='cover'
      source={{ uri: source }}
      style={{
        height: undefined,
        width: deviceWid,
        aspectRatio: aspectRatio || 9/16,
        marginTop: getTopPadding(true),
        position: 'absolute',
      }}
      paused={!focused}
      muted={false}
      repeat
    />
  )
}

export default CapturedVideo
