import React, { useState } from 'react'
import styled from 'styled-components'
import { useDispatch } from 'react-redux'
import { theme } from 'lib'
import { addMinutes, addHours, addDays, addWeeks, addMonths, addYears } from 'date-fns'
import { Icon, Clickable, Col, Row, Modal, Text } from 'components'
import { SettingObject } from './'
import { useTranslation } from 'react-i18next'

const Seperator = styled.View`
  width: 100%;
  height: 1px;
  background-color: ${theme.GREY_20};
  margin: 4px 0;
`

const MuteOptions = ({ visible, closeModal, updateChatSettings }) => {
  const dispatch = useDispatch(),
    { t } = useTranslation()

  return (
    <React.Fragment>
      <Modal visible={visible} closeModal={closeModal} propagateSwipe={true}>
        <Col noFlex pad='0 16px 15px'>
          <SettingObject
            icon='arrowLeft'
            text={t('common.back')}
            dimensions={16}
            onClick={closeModal}
          />

          <Seperator />

          <SettingObject
            icon='musicOff'
            text={t('chat.disableSound')}
          />

          <Text b2 col={theme.GREY_60} marg='8px 0 0'>
            {t('chat.muteNotificationsFor')}
          </Text>

          <SettingObject
            text={t('chat.thirtyMinutes')}
            onClick={() => updateChatSettings('mutedUntil', addMinutes(new Date, 30))}
          />

          <SettingObject
            text={t('chat.oneHour')}
            onClick={() => updateChatSettings('mutedUntil', addHours(new Date, 1))}
          />

          <SettingObject
            text={t('chat.oneDay')}
            onClick={() => updateChatSettings('mutedUntil', addDays(new Date, 1))}
          />

          <SettingObject
            text={t('chat.oneWeek')}
            onClick={() => updateChatSettings('mutedUntil', addWeeks(new Date, 1))}
          />

          <SettingObject
            text={t('chat.oneMonth')}
            onClick={() => updateChatSettings('mutedUntil', addMonths(new Date, 1))}
          />

          <Seperator />

          <SettingObject
            icon='mute'
            text={t('chat.muteForever')}
            onClick={() => updateChatSettings('mutedUntil', addYears(new Date, 50))}
          />
        </Col>
      </Modal>
    </React.Fragment>
  )
}

export default MuteOptions
