import React from 'react'
import styled from 'styled-components'
import { theme, normalizeDate } from 'lib'
import { Col, Row, Text, Spinner } from 'components'
import { ImageMsg } from './'

const Container = styled.View`
  flex-direction: row;
  ${props => props.isSender && `flex-direction: row-reverse;`};
`

const ImageBg = styled.ImageBackground`
  margin: 0 0 17px;
  width: 244px;
  height: 244px;
`

const Message = ({ item, user }) => {
  if(!item.length) return null

  const isSender = (item[0].sender === user._id)

  return (
    <Container isSender={isSender}>
      <Col
        endHorizontal={isSender}
        startAll={!isSender}
        noFlex
        marg='8px 0 0'
      >
        {item.map((x, i) => {
          const lastItem = i === 0,
            firstItem = i === item.length - 1

          return (
            <Row
              key={i}
              minWid='140px'
              pad={x.file ? '8px 8px 10px' : '10px 16px'}
              marg='8px 0 0'
              bg={isSender ? theme.FOG : theme.GREY_10}
              noFlex
              individualRadius={isSender
                ? [
                  '22px',
                  lastItem ? '22px' : '0',
                  '0',
                  '22px'
                ] : [
                  '0',
                  '22px',
                  '22px',
                  firstItem ? '22px' : '0',
                ]
              }
            >
              {x.file ? (
                <ImageMsg file={x.file} isSender={isSender}/>
              ) : (
                <Text b1 med marg='0 0 10px'>
                  {x.body}
                </Text>
              )}

              <Row absolute noFlex rightDistance={x.file ? '8px' : '16px'} bottomDistance='4px'>
                <Text b2 med col={theme.GREY_70}>
                  {normalizeDate(x.createdAt, 'Hour')}
                </Text>
              </Row>
            </Row>
          )
        })}
      </Col>
    </Container>
  )
}

export default Message
