import React, { useState } from 'react'
import { Icon, Clickable, Col, Row, Text } from 'components'
import { theme } from 'lib'
import styled from 'styled-components'


const SettingObject = ({ icon, text, onClick, arrow }) => {

  return (
    <Col marg='4px 0 0' noFlex>
      <Clickable marg='4px 0 0' onClick={onClick}>
        <Row noFlex ht='48px' center wid='100%'>
          <Row center>
            <Icon type={icon} dimensions={20} col={theme.GREY_60}/>
            <Text marg={`0 0 0 ${icon ? '10px' : '32px'}`} med>{text}</Text>
          </Row>

          {arrow &&
            <Row pad='8px 0 0' noFlex centerAll>
              <Icon type='right' dimensions={20} col={theme.GREY_60}/>
            </Row>
          }
        </Row>
      </Clickable>
    </Col>
  )
}

export default SettingObject
