import React, { useState } from 'react'
import { useDispatch } from 'react-redux'
import { Platform } from 'react-native'
import { Icon, Clickable, Col, Row, Modal, Text } from 'components'
import { useActionSheet } from '@expo/react-native-action-sheet'
import { useNavigation } from '@react-navigation/native'
import { theme, Api } from 'lib'
import styled from 'styled-components'
import { useTranslation } from 'react-i18next'

import { SettingObject, MuteOptions } from './'

const Seperator = styled.View`
  width: 100%;
  height: 1px;
  background-color: ${theme.GREY_20};
  margin: 4px 0;
`

const Settings = ({
  chat,
  user,
  searching,
  toggleSearch,
  otherUser,
  getHeaderObject
}) => {
  const [visible, setVisible] = useState(false),
    [muteVisible, setMuteVisible] = useState(false),
    chatSettings = chat.users.find(x => x.user._id === user._id),
    dispatch = useDispatch(),
    { showActionSheetWithOptions } = useActionSheet(),
    navigation = useNavigation(),
    isAndroid = Platform.OS === 'android',
    { t } = useTranslation(),
    isBlocked = user.blocked?.find(x => x === otherUser._id)

  function handleVisible() {
    setVisible(!visible)
  }

  function handleMuteModal() {
    muteVisible ? setMuteVisible(false) : setVisible(false)

    setTimeout(() => {
      muteVisible ? setVisible(true) : setMuteVisible(true)
    }, 400)
  }

  async function updateChatSettings(type, value) {
    const getMethod = () => {
      switch (type) {
        case 'hidden':
            return 'hide'

        case 'favorite':
            return 'favorite'

        case 'clearHistoryDate':
          return 'clearHistory'

        case 'deletedDate':
          return 'delete'

        case 'mutedUntil':
          return 'mute'
      }
    }

    const res = await Api.patch(
      `/chats/${getMethod()}`,
      { chatId: chat._id, value }
    )
    if(!res) return

    dispatch({
      type: 'UPDATE_CHAT_SETTINGS',
      payload: {
        chatId: chat._id,
        userId: user._id,
        type,
        value
      }
    })

    type === 'mutedUntil' ? handleMuteModal() : handleVisible()
    if(type === 'deletedDate') navigation.navigate('Messages')
  }

  function handleSearch() {
    toggleSearch()
    handleVisible()
  }

  function handleClearHistory() {
    if(isAndroid) handleVisible()

    showActionSheetWithOptions(
      {
        options: [t('chat.clearChatHistory'), t('common.cancel')],
        destructiveButtonIndex: [0],
        cancelButtonIndex: 1,
        title: t('chat.youCannotAction'),
        userInterfaceStyle: 'light'
      },
      (buttonIndex) => {
        switch (buttonIndex) {
          case 0:
            updateChatSettings('clearHistoryDate', new Date())
            break
        }
      }
    )
  }

  function handleDeleteChat() {
    if(isAndroid) handleVisible()

    showActionSheetWithOptions(
      {
        options: [t('common.delete'), t('common.cancel')],
        destructiveButtonIndex: [0],
        cancelButtonIndex: 1,
        title: `${t('chat.deleteConversationWith')} with ${getHeaderObject().name}? \n ${t('chat.youCannotAction')}`,
        userInterfaceStyle: 'light'
      },
      (buttonIndex) => {
        switch (buttonIndex) {
          case 0:
              updateChatSettings('deletedDate', new Date())
            break
        }
      }
    )
  }

  async function block() {
    Api.post('/users/toggleBlock', { userId: otherUser._id })

    dispatch({
      type: 'BLOCK',
      payload: otherUser._id
    })

    handleVisible()
  }


  return (
    <React.Fragment>
      <Clickable onClick={handleVisible} pad='16px'>
        <Icon type='menu' dimensions={16} />
      </Clickable>

      <MuteOptions
        visible={muteVisible}
        closeModal={handleMuteModal}
        updateChatSettings={updateChatSettings}
      />

      <Modal visible={visible} closeModal={handleVisible}>
        <Col noFlex pad='0 16px 15px'>
          <SettingObject
            icon='mute'
            text={t('chat.muteNotifications')}
            arrow
            onClick={handleMuteModal}
          />

          <Seperator />

          <SettingObject
            icon='starOutline'
            text={chatSettings?.favorite ? t('chat.removeFavorites') : t('chat.addFavorites')}
            onClick={() => {
              updateChatSettings(
                'favorite',
                !chatSettings.favorite
              )
            }}
          />

          <SettingObject
            icon='search'
            text={t('common.search')}
            onClick={handleSearch}
          />

          <SettingObject
            icon='hide'
            text={chatSettings?.hidden ? t('chat.unhideChat') : t('chat.hideChat')}
            onClick={() => {
              updateChatSettings(
                'hidden',
                !chatSettings.hidden
              )
            }}
          />

          <SettingObject
            icon='clean'
            text={t('chat.clearHistory')}
            onClick={handleClearHistory}
          />

          {!chat.group && (
            <SettingObject
              icon='block'
              text={!isBlocked ? t('common.block') : t('common.unblock')}
              onClick={block}
            />
          )}

          <SettingObject
            icon='delete'
            text={t('chat.deleteConversation')}
            onClick={handleDeleteChat}
          />
        </Col>
      </Modal>
    </React.Fragment>
  )
}

export default Settings
