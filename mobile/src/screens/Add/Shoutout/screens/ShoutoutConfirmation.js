import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { useDispatch, useSelector } from 'react-redux'
import { isNewIphone } from 'core'
import { Switch } from 'react-native'
import { useRoute } from '@react-navigation/native'
import { theme, Api, currencies } from 'lib'
import { useTranslation } from 'react-i18next'
import {
  Col,
  Row,
  Clickable,
  Text,
  SwipeButton,
  Header,
  Spinner,
  Icon
} from 'components'

import { submit } from '../lib'

const ImageBackground = styled.ImageBackground`
  width: 72px;
  height: 97px;
  align-items: center;
  justify-content: center;
  margin: 0 12px 0 0;
`

const ShoutoutConfirmation = ({ navigation }) => {
  const [accepted, setAccepted] = useState(false),
    [price, setPrice] = useState(false),
    { otherUser, entityId, type, comment, media, eventId } = useRoute().params,
    user = useSelector(state => state.user),
    dispatch = useDispatch(),
    { t } = useTranslation(),
    userCurrency = user.billing?.currency || 'usd',
    currency = currencies.find(x => userCurrency.toLowerCase() === x.value.toLowerCase())

  useEffect(() => {
    getPrice()
  }, [])

  useEffect(() => {
    if(!price || user?.billing?.cards?.length) return
    if(!price.price) return

    navigation.navigate('UpdatePaymentWarning')
  }, [price])

  async function getPrice() {
    const res = await Api.get(`/billing/getPricing/shoutout/${user.billing.currency || 'usd'}`)
    if(!res) return

    dispatch({
      type: 'UPDATE_BILLING',
      payload: res.billing
    })

    setPrice(res.pricing)
  }

  async function submitShoutout() {
    if(!accepted) {
      dispatch({
        type: 'HANDLE_RES',
        payload: { err: t('add.shouldAcceptTerms') }
      })

      return
    }

    submit({
      dispatch,
      navigation,
      media,
      comment,
      entityId,
      eventId
    })
  }

  return (
    <React.Fragment>
      <Header
        hasBack
        title={t('common.addShoutout')}
      />

      {!price && <Spinner fullScreen />}

      <Col
        bg='#fff'
        pad='12px 16px 0'
        between
      >
        <Col>
          <Row between center noFlex>
            <Text h2>{t('add.summary')}</Text>

            <Clickable onClick={() => navigation.goBack()}>
              <Text b2 bold>Edit</Text>
            </Clickable>
          </Row>

          <Col
            noFlex
            between
            ht='129px'
            pad='16px'
            marg='16px 0 0'
            bg={theme.GREY_10}
          >
            <Row>
              {media && (
                <ImageBackground
                  source={{ uri: media.uri }}
                  style={{ width: 72, height: 97 }}
                >
                  {media?.isVideo && (
                    <Col
                      noFlex
                      wid='32px'
                      ht='32px'
                      centerAll
                      hasRadius='32px'
                      bg='rgba(210, 216, 223, 0.7)'
                    >
                      <Icon type='playOutline' dimensions={14} />
                    </Col>)
                  }
                </ImageBackground>
              )}
              <Col>
                <Text b2><Text b2 bold>{otherUser.username}</Text> from <Text b2 bold>{user.username}</Text></Text>

                <Text marg='4px 0 0'>{comment}</Text>
              </Col>
            </Row>
          </Col>

          {price && (
            <Row between noFlex marg='27px 0 0'>
              <Text h2>{t('add.total')}</Text>

              <Text h2 bold col={theme.GREY_60}>{currency.symbol}<Text h2 bold>{price.price / 100}</Text></Text>
            </Row>
          )}

          <Row
            noFlex
            center
            between
            marg='23px 0 0'
          >
            <Switch
              value={accepted}
              onValueChange={setAccepted}
              ios_backgroundcolor={theme.GREEN}
            />

            <Col noFlex wid='290px'>
              <Text b2 col={theme.GREY_60}>
              {t('add.iAcceptTerms')}
              </Text>
            </Col>
          </Row>

          {/* !user.premiumPack && (
            <React.Fragment>
              <Divider marg='32px 0 24px' />

              <Col noFlex>
                <Row noFlex center>
                  <Col>
                    <Text marg='0 0 4px'>Save <Text bold>20%</Text> with 12 pack</Text>
                    <Text c1 med col={theme.GREY_60}>12 shoutouts for the price of 10</Text>
                  </Col>

                  <Text bold>$86,33</Text>
                </Row>

                <Button
                  bordered
                  big
                  marg='24px 0 0'
                  text='See pack details'
                />
              </Col>
            </React.Fragment>
          ) */}
        </Col>

        <Col noFlex pad={isNewIphone() ? '0 0 50px' : '0 0 16px'}>
          <SwipeButton
            title={t('add.swipeRightPurchase')}
            thumbColor={theme.GREEN}
            onClick={submitShoutout}
            disabled={price?.price !== 0 && !user.billing.cards?.length || !accepted}
          />
        </Col>
      </Col>
    </React.Fragment>
  )
}

export default ShoutoutConfirmation
