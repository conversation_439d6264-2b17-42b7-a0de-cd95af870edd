import React, { useState } from 'react'
import { ScrollView } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import { Text, Row, Col, Icon, Clickable, Dropdown } from 'components'
import { theme } from 'lib'

import { TopNavigator, SearchModal, CreateShoutout, Suggested } from '../components'

const Shoutout = ({ notAddScreen, route }) => {
  const { cameraMedia } = route.params,
    { t } = useTranslation(),
    [selected, setSelected] = useState(0)

  return (
    <Col pad='8px 0 0'>
      <ScrollView >
        {!notAddScreen && (
          <TopNavigator
            selected={selected}
            setSelected={setSelected}
          />
        )}

        {selected === 2
          ? (
            <Suggested />
          ) : (
            <CreateShoutout selected={selected} cameraMedia={cameraMedia} />
          )
        }
      </ScrollView>
    </Col>
  )
}

export default Shoutout
