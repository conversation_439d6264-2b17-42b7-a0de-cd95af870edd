import React, { useState, useEffect, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { FlatList } from 'react-native'
import { Api, theme } from 'lib'
import { Col, Row, Modal, Text, Input, Avatar, Clickable } from 'components'

const Item = ({ item, handleSelect }) => (
  <Clickable
    onClick={() => handleSelect(item)}
    marg='12px 0 0'
  >
    <Row noFlex>
      <Avatar user={item} />

      <Col marg='13px 0 13px 12px'>
        <Row center>
          <Text b1 med>{item?.name}</Text>
        </Row>

        {!!item.bio && (
          <Text
            col={theme.GREY_60}
            numberOfLines={1}
          >
            {item.bio}
          </Text>
        )}
      </Col>
    </Row>
  </Clickable>
)

let timer
const SearchModal = ({ visible, closeModal, type, handleSelect, excludeType }) => {
  const [data, setData] = useState([]),
    [listEndReached, setListEndReached] = useState(false),
    [text, setText] = useState(''),
    { t } = useTranslation(),
    listRef = useRef()

  useEffect(() => {
    if(!visible) return

    search(true)
  }, [visible, text])

  async function search(searchNew) {
    clearTimeout(timer)

    timer = setTimeout(async () => {
      setListEndReached(false)

      const res = await Api.post('/users/search', {
        text,
        type,
        excludeType,
        skip: !searchNew && data?.length || 0
      })
      if(!res) return
      if(!res?.length && !searchNew) return setListEndReached(true)

      if(searchNew) listRef.current.scrollToOffset({ offset: 0 })
      setData(searchNew ? res : [...data, ...res])
    }, 300)
  }

  function select(item) {
    handleSelect(item)
    closeModal()
  }

  return (
    <Modal
      fullScreen
      visible={visible}
      closeModal={closeModal}
      title={`Search users`}
      dropdown
    >
      <Col noFlex>
        <FlatList
          ListHeaderComponent={
            <Input
              search
              placeholder={`Search users...`}
              onChangeText={(e) => setText(e)}
            />
          }
          ListEmptyComponent={
            <Row marg='20px'>
              <Text b2 bold wid='100%' align='center'>
                {t('add.noUserFound')}
              </Text>
            </Row>
          }
          ref={listRef}
          data={data}
          onEndReached={!listEndReached && (() => search())}
          onEndReachedThreshold={1}
          keyExtractor={(item) => item._id}
          renderItem={(props) => <Item {...props} handleSelect={select}/>}
          contentContainerStyle={{ paddingHorizontal: 16 }}
        />
    </Col>
    </Modal>
  )
}

export default SearchModal
