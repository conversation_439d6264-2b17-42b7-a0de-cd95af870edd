import React, { useMemo } from 'react'
import { View } from 'react-native'
import { useTranslation } from 'react-i18next'
import { theme } from 'lib'
import { Clickable, Col, Icon, Text } from 'components'
import { isTablet } from 'react-native-device-info';

const TopNavigator = ({ selected, setSelected }) => {
  const { t } = useTranslation()
  const isTab = isTablet();

  const options = [{
    icon: 'profileBox',
    text: t('common.athletes')
  },
  {
    icon: 'game',
    text: t('common.game')
  },
  {
    icon: 'suggested',
    iconDimensions: 40,
    text: t('add.suggested')
  }]

  const styles = useMemo(() => ({
    tabContainer: {
      width: '100%',
      flexDirection: 'row',
      justifyContent: 'center',
      flexGrow: 1
    }, 
  }), [])

  return (
      <View style={styles.tabContainer}>
        {options.map((x, i) => !!x && (
          <Clickable
            key={i}
            onClick={() => setSelected(i)}
            marg={`0 ${i === options.length - 1 ? 16 : 48}px 0 0`}
          >
            <Col center>
              <Icon
                type={x.icon}
                dimensions={x.iconDimensions || 36}
                col={selected === i ? theme.SECONDARY : theme.GREY_30 }
              />
              <Text h2={isTab} h4={!isTab} marg='8px 0 0' col={selected === i ? '#000' : theme.GREY_60}>
                {x.text}
              </Text>
            </Col>
          </Clickable>
        ))}
      </View>
  )
}

export default TopNavigator