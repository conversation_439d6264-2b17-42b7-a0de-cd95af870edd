import React, { useState, useEffect } from 'react'
import { Api } from 'lib'
import { Text, Col, Row, Spinner } from 'components'
import { UserItem } from 'screens/Browse/components'
import { useTranslation } from 'react-i18next'
import { useNavigation } from '@react-navigation/native'


const Suggested = ({  }) => {
  const [suggestions, setSuggestions] = useState(null)
  const { t } = useTranslation()
  const navigation = useNavigation()

  useEffect(() => {
    getSuggestions()
  }, [])

  async function getSuggestions() {
    const res = await Api.post('/shoutouts/getSuggestions')
    if(!res) return

    setSuggestions(res)
  }

  function handleClick(user) {
    navigation.navigate('AddShoutout', { user })
  }

  return (
    <Col noFlex marg='42px 0 0'>
      {!suggestions ? (
        <Col noFlex centerAll>
          <Spinner />
        </Col>
      ) : (
        <Col noFlex pad='0 18px 32px'>
          <Text h3>{t('common.athletes')}</Text>
          {suggestions.athletes.map((x, i) => (
            <UserItem key={i} item={x} pad={'0'} onClick={() => handleClick(x)} />
          ))}

          <Text h3 marg='28px 0 0'>{t('common.teams')}</Text>
          {suggestions.teams.map((x, i) => (
            <UserItem key={i} item={x} pad={'0'} onClick={() => handleClick(x)} />
          ))}
        </Col>
      )} 
    </Col>
  )
}

export default Suggested