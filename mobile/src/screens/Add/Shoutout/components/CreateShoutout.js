import React, { useState, useEffect, useRef, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { useDispatch } from 'react-redux'
import { useNavigation } from '@react-navigation/native'
import styled from 'styled-components'
import { format, isToday } from 'date-fns'
import { theme, useIsMounted } from 'lib'
import { handlePermission } from 'core'
import { Col, Text, Dropdown, Input, Row, Clickable, Icon, Avatar } from 'components'

import { SearchModal } from './'
import { MediaPicker } from '../../components'
import { submit } from '../lib'
import { ScrollView, View, Platform, TouchableOpacity } from 'react-native'
import DeviceInfo from 'react-native-device-info'
import { useSafeAreaInsets } from 'react-native-safe-area-context'

let isTablet = DeviceInfo.isTablet()

const Image = styled.ImageBackground`
  height: 88px;
  width: 88px;
  margin: 0 8px 0 0;
`

const CreateShoutout = ({ notAddScreen, selected, paramsSelected, eventId, paramsEvent, cameraMedia }) => {
  const [comment, setComment] = useState(''),
    [media, setMedia] = useState(null),
    [country, setCountry] = useState(null),
    [city, setCity] = useState(null),
    [eventObject, setEventObject] = useState(paramsEvent),
    [user, setUser] = useState(paramsSelected),
    [searchModalOpen, setSearchModalOpen] = useState(false),
    [showMediaPicker, setShowMediaPicker] = useState(false),
    [permissionGiven, setPermissionGiven] = useState(false),
    navigation = useNavigation(),
    dispatch = useDispatch(),
    { t } = useTranslation(),
    mounted = useIsMounted(),
    eventUserType = eventObject?.away.type,
    inputRef = useRef(),
    scrollViewRef = useRef(),
    topPadding = Platform.OS === 'android' ? 50 : useSafeAreaInsets().top + 48

  useEffect(() => {
    if (notAddScreen) return
    setUser(null)
  }, [selected])

  useEffect(() => {
    if (!cameraMedia) return

    setMedia(cameraMedia)
  }, [cameraMedia])

  useEffect(() => {
    if (permissionGiven) {
      showPicker(permissionGiven)
    }
  }, [permissionGiven])

  const showPicker = (showPicker = false) => {
    if (permissionGiven) {
      setShowMediaPicker(showPicker)
    } else {
      getPermission()
    }
  }

  async function getPermission() {
    const perm = await handlePermission({
      checkedPermission: 'gallery',
      onBlocked: () => handleBlocked(t('permissions.gallery'))
    })

    if (!mounted.current) return
    setPermissionGiven(perm)
  }

  function handleBlocked(permission) {
    navigation.navigate('PermissionBlocked', { permission })
  }

  async function handleNext() {
    if (eventObject?._id || user.type === 'team') {
      return submit({
        dispatch,
        navigation,
        media,
        comment,
        entityId: user._id,
        eventId: eventObject?._id
      })
    }

    const data = {
      type: selected ? 'game' : 'athlete',
      comment,
      media,
      entityId: user._id,
      otherUser: user,
      eventId: eventObject?._id
    }

    navigation.navigate('ShoutoutConfirmation', data)
  }

  function handleSearchModal() {
    setSearchModalOpen(!searchModalOpen)
  }

  const styles = useMemo(() => ({
    photoContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderColor: 'rgba(0, 0, 0, 1)',
      borderRadius: 36,
      borderWidth: 2,
      paddingHorizontal: 10,
      paddingVertical: 2,
      height: 38
    },
    teamContainerStyle: {
      backgroundColor: '#000',
      paddingTop: 70,
      paddingHorizontal: 16,
      paddingBottom: 16,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: isTablet ? 'flex-end' : 'center',
      alignItems: 'center',
      marginHorizontal: 8,
      marginBottom: 10,
    },
    createPostContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: !user || !comment?.length ? theme.GREY_20 : 'black',
      borderRadius: 36,
      paddingHorizontal: 10,
      paddingVertical: 2,
      marginLeft: 5,
      height: 38
    }

  }), [user, comment, theme, isTablet])

  const photoVideoCommand = useMemo(() => (
    <TouchableOpacity
      style={[
        styles.photoContainer,
        !isTablet && { maxWidth: 200 }
      ]}
      onPress={() => {
        showPicker(!showMediaPicker)
      }}
    >
      <Icon type='addPhotos' dimensions={17} marg='0 0 0 5px' />
      <Text
        bold
        size='14px'
        marg='0 5px'
        numberOfLines={1}
        adjustsFontSizeToFit
      >{t('add.addPhotosAndVideos')}
      </Text>
    </TouchableOpacity>
  ), [isTablet, showMediaPicker, showPicker, t])

  const scrollToInput = () => {
    inputRef.current.measure((x, y, width, height, pageX, pageY) => {
      setTimeout(() => {
        scrollViewRef.current?.scrollTo({ y: pageY, animated: true })
      }, 100)
    })
  }

  const createShoutoutCommand = (
    <TouchableOpacity
      style={[
        styles.createPostContainer,
        !isTablet && { maxWidth: 140 }
      ]}
      onPress={handleNext}
      disabled={!user || !comment?.length}
    >
      <Text
        bold
        size='14px'
        marg='0 2px'
        numberOfLines={1}
        adjustsFontSizeToFit
        col='white'
      >{t('add.createShoutout')}</Text>
    </TouchableOpacity>
  )

  return (
    <>
      {!!eventObject && (
        <View style={isTablet ? styles.teamContainerStyle : { ...styles.teamContainerStyle, paddingTop: topPadding }}>
          <Col center noFlex>
            <Text col='#fff' b2>{eventObject?.league}</Text>
            <Text col='#fff' b2>{eventObject?.location}</Text>

            <Row noFlex between wid='100%' center>
              <Col noFlex center>
                <Avatar user={eventObject.away} />
                <Text c1 med col='#fff' wid='105px' align='center' marg='4px 0 0'>
                  @{eventObject.away.username}
                </Text>
              </Col>

              <Text col='#fff' b2 bold marg='0 0 35px'>
                {isToday(new Date(eventObject.date))
                  ? t('common.today')
                  : format(new Date(eventObject.date), 'MMM dd')
                },
                {
                  format(new Date(eventObject.date), ' hh:mm aa')
                }
              </Text>

              <Col noFlex center>
                <Avatar user={eventObject.home} />
                <Text c1 med col='#fff' wid='105px' align='center' marg='4px 0 0'>
                  @{eventObject.home.username}
                </Text>
              </Col>
            </Row>
          </Col>
        </View>
      )}
      <Col marg='0 12px 0 12px'>
        <ScrollView ref={scrollViewRef}>
          {!!paramsEvent ? 
            <Text h4 >{t('add.shoutoutTo')}</Text>
            :
            <Text h2 >{t('add.shoutoutTo')}</Text>
          }

          {paramsSelected ? (
            <Row noFlex center marg='16px 0 0'>
              <Avatar
                user={user}
              />

              <Col noFlex marg='0 0 0 12px'>
                <Text bold>
                  {user.name}
                </Text>
                <Text marg='2px 0 0' b2 col={theme.GREY_60}>
                  @{user.username}
                </Text>
              </Col>
            </Row>
          ) : (
            <Dropdown
              marg='16px 0 0'
              title={t(eventUserType
                ? `common.${eventUserType}`
                : selected
                  ? 'add.teamOrPlayer'
                  : 'common.player')
              }
              onClick={!eventObject && handleSearchModal}
              options={!!eventObject && [eventObject.away, eventObject.home]}
              handleSelect={setUser}
              selected={user}
            />
          )}

          

          <SearchModal
            visible={searchModalOpen}
            closeModal={handleSearchModal}
            type={!selected && 'athlete'}
            excludeType={selected && 'fan'}
            handleSelect={setUser}
          />

          <Row marg='32px 0 0' noFlex>
            {!!media && (
              <Image
                imageStyle={{ borderRadius: 16 }}
                source={{ uri: media.uri }}
              >
                <Clickable onClick={() => setMedia(null)}>
                  <Row
                    bg={theme.GREY_20}
                    opa='0.7'
                    noFlex
                    wid='32px'
                    ht='32px'
                    centerAll
                    absolute
                    hasRadius='16px'
                    rightDistance='8px'
                    topDistance='8px'
                  >
                    <Icon type='close' dimensions={14} />
                  </Row>
                </Clickable>
              </Image>
            )}

            <Row>
              <Input
                inputRef={inputRef}
                placeholder={t('add.writeCaption')}
                value={comment}
                onChangeText={(e) => setComment(e)}
                multiline
                numberOfLines={5}
                ht='250px'
                handleFocus={scrollToInput}
              />
            </Row>
          </Row>
        </ScrollView>


        <MediaPicker
          entityType='shoutout'
          noSelectMultiple
          media={media ? [media] : []}
          visible={showMediaPicker}
          closeModal={() => showPicker(false)}
          notAddScreen
          setMedia={(opt) => setMedia(opt?.length && opt[0])}
        />

        {/*
            !!selected && (
              <Col marg='32px 0 0' noFlex>
                <Text h2>
                  {t('add.whereItHappens')}
                </Text>

                <Dropdown
                  marg='16px 0 0'
                  options={nations}
                  selected={country}
                  handleSelect={setCountry}
                  title={t('common.country')}
                />

                <Dropdown
                  marg='16px 0 0'
                  title={t('common.city')}
                  selected={city}
                  handleSelect={setCity}
                />
              </Col>
            )
          */}
        <View style={{ height: 5 }}></View>
        <View style={styles.buttonContainer}>
          {photoVideoCommand}
          {!isTablet && <View style={{ flex: 1 }}></View>}
          {createShoutoutCommand}
        </View>

      </Col>
    </>
  )
}

export default CreateShoutout
