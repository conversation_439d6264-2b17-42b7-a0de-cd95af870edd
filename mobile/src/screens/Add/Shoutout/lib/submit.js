import ObjectId from 'bson-objectid'
import { Platform } from 'react-native'
import { mediaCompressor } from 'core'
import { createThumbnail } from 'react-native-create-thumbnail'
import mime from 'mime'
import { Api, uploadWithSignedUrl, getAspectRatio } from 'lib'

const isAndroid = Platform.OS === 'android'

/*  NOT FINISHED,
 *  SUBMIT IS HANDLED AT screens/ShoutoutConfirmation AT THE MOMENT
 */

async function submit({
  dispatch,
  navigation,
  media,
  comment,
  entityId,
  eventId
}) {
  let data = {},
    documentId = ObjectId().toString()

  let uploadedMedia = {}

  dispatch({
    type: 'LOADING_BUTTON',
    payload: 'navigator'
  })

  if (media) {
    if (!media.isVideo) {
      const { aspectRatio } = media || {}
      const dimensions = getAspectRatio.toPixels(aspectRatio, 1080)

      if (media.originalPath) return media

      const imgPath = await mediaCompressor.getPath(media.uri || media, 'image')
      const compressed = await mediaCompressor.image(
        imgPath,
        dimensions.width,
        dimensions.height,
        0.3
      )

      const processedMediaPhoto = {
        uri: compressed,
        type: mime.getType(compressed)
      }
      processedMediaPhoto.type = mime.getType(processedMediaPhoto.uri)

      // Generate thumbnail and filename
      const thumbnail = await mediaCompressor.image(processedMediaPhoto.uri, 216, 216, 1)
      const thumbnailType = mime.getType(thumbnail)
      const fileName = `${documentId}-1.${processedMediaPhoto.type.split('/')[1]}`
      const thumbnailName = `${documentId}-1-thumbnail.${thumbnailType.split('/')[1]}`

      uploadedMedia = {
        uri: fileName,
        thumbnail: thumbnailName
      }

      for await (let key of Object.keys(uploadedMedia)) {
        const name = uploadedMedia[key]

        const file = {
          uri: key === 'uri' ? processedMediaPhoto.uri : thumbnail,
          type: key === 'uri' ? processedMediaPhoto.type : thumbnailType,
          name: name
        }

        // Upload file and get location
        const location = await uploadWithSignedUrl(name, file.type, file)
        uploadedMedia[key] = location
      }
    }

    else {
      let realPath = media.realPath
      /*
        Correct file path in Android
      */
      if (isAndroid) {
        realPath = realPath.match(/file:\/\//g)?.length > 1 ? realPath.replace('file://', '') : realPath
      }

      /*
        Compress video
      */
      const compressed = await mediaCompressor.video(realPath || media)

      /*
        Generate thumbnail
      */
      const thumbnail = await createThumbnail({
        url: compressed,
        timeStamp: 0,
      })
        .then(res => res)
        .catch(err => { err })

      if (thumbnail.err) {
        dispatch({ type: 'LOADING_BUTTON' })
        return alert(thumbnail.err)
      }

      const videoType = mime.getType(compressed),
        thumbnailType = isAndroid ? thumbnail.mime : mime.getType(thumbnail.path),
        fileName = `${documentId}-1.${videoType.split('/')[1]}`,
        thumbnailName = `${documentId}-1-thumbnail.${thumbnailType.split('/')[1]}`

      uploadedMedia = {
        uri: fileName,
        thumbnail: thumbnailName
      }

      for await (let key of Object.keys(uploadedMedia)) {
        const name = uploadedMedia[key]

        const file = {
          uri: key === 'uri'
            ? compressed.replace('file://', 'file:///')
            : thumbnail.path,
          type: key === 'uri' ? videoType : thumbnailType,
          name: name
        }

        const location = await uploadWithSignedUrl(name, file.type, file)

        uploadedMedia[key] = location
      }
    }

  }

  data = { media: media && uploadedMedia, comment, entityId, eventId }
  const res = await Api.post('/shoutouts/create', data)

  if (!res) {
    dispatch({
      type: 'LOADING_BUTTON',
      payload: null
    })

    return
  }

  dispatch({
    type: 'SENT_SHOUTOUT',
    payload: res
  })

  dispatch({
    type: 'ADD_SHOUTOUT',
    payload: res
  })

  const filters = ['Add', 'ShoutoutConfirmation', 'AddShoutout', 'Capture', 'CapturedMediaView', 'CreateMatchDayShoutout']
  const newRoutes = navigation.getState().routes.filter(x => !filters.includes(x.name))

  navigation.reset({
    index: 0,
    routes: newRoutes,
  })

  dispatch({
    type: 'LOADING_BUTTON',
    payload: null
  })
}

export default submit
