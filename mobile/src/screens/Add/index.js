import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigation, useRoute } from '@react-navigation/native'
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs'
import { useFocusListener } from 'core'
import { Spinner } from 'components'

import CaptureStory from './Story/CaptureStory'
import Live from 'screens/Live'

import { TabBar } from './components'

const Tab = createMaterialTopTabNavigator()

const Add = () => {
  const [isFocused, setIsFocused] = useState(false),
    loading = useSelector(state => state.appState.loadingButton === 'add'),
    params = useRoute().params || {},
    { initialRoute, caption, postType, fromAddNavigator } = params,
    dispatch = useDispatch(),
    navigation = useNavigation()

  /*
    Enables the swipe to navigate to home screen on focus,
    disables it on blur.

    Accepts navigations only from AddNavigator
  */
  useFocusListener((e) => {
    if(!fromAddNavigator) return

    setIsFocused(e)

    dispatch({
      type: 'UPDATE_APP_STATE',
      payload: { addSwipeEnabled: e }
    })

    dispatch({ type: 'RESET_SHOUTOUT_DATA' })
  })

  return (
    <React.Fragment>
      {loading && <Spinner fullScreen />}

      <Tab.Navigator
        sceneContainerStyle={{ backgroundColor: '#fff' }}
        screenOptions={{ swipeEnabled: false }}
        initialRouteName={initialRoute || 'Post'}
        tabBar={(props) => <TabBar {...props} fromAddNavigator={fromAddNavigator} />}
        tabBarPosition='top'
      >
        <Tab.Screen
          name='Story'
          component={CaptureStory}
          initialParams={params}
        />

        <Tab.Screen
          name='Live'
          component={Live}
          initialParams={{ isHost: true }}
        />
      </Tab.Navigator>
    </React.Fragment>
  )
}

export default Add
