import React, { useState } from 'react'
import styled from 'styled-components'
import { theme } from 'lib'
import { Row, Clickable, Icon } from 'components'

const Image = styled.ImageBackground`
  height: 96px;
  width: 96px;
  margin: 0 8px 0 ${props => props.index === 0 ? 16 : 0}px;
`

const MediaItem = ({ handleRemove, index, item, noRemove }) => {

  return (
    <Image
      imageStyle={{ borderRadius: 16 }}
      source={{ uri: item.uri }}
      index={index}
    >
      {
        !noRemove &&
        <Clickable onClick={() => handleRemove(index)}>
          <Row
            bg={theme.GREY_20}
            opa='0.7'
            noFlex
            wid='32px'
            ht='32px'
            centerAll
            absolute
            hasRadius='16px'
            rightDistance='8px'
            topDistance='8px'
          >
            <Icon type='close' dimensions={14} />
          </Row>
        </Clickable>
      }
    </Image>
  )
}

export default MediaItem
