import React, { useState, useEffect, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { Dropdown } from 'components'
import { TagModal } from '../../../Create/components'


const FriendsModal = ({ handleSelect, tagged, postId }) => {
  const [selected, setSelected] = useState(tagged || []),
    [visible, setVisible] = useState(false),
    { t } = useTranslation()

  const handleSave = useCallback((tagged) => {
    handleSelect(tagged)
    setSelected(tagged)
  }, [handleSelect, setSelected])

  const handleVisible = useCallback(()=>{
    setVisible(!visible)
    setSelected(tagged)
  }, [setVisible, setSelected])

  return (
    <React.Fragment>
      <Dropdown
        marg='24px 0 0'
        title={t('add.tagPeople')}
        selected={selected.length && selected}
        customText={selected.length && `${selected.length} people`}
        onClick={handleVisible}
      />
      <TagModal
        visible={visible}
        closeModal={() => setVisible(false)}
        tagged={selected}
        handleSelect={handleSave}
        postId={postId}
      />
    </React.Fragment>
  )
}

export default FriendsModal
