import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Input } from 'components'

const SearchBar = ({ setData, text, handleChange }) => {
  const { t } = useTranslation()

  return (
    <Input
      marg={`4px 0 ${text.length ? 12 : 4}px`}
      search
      placeholder={t('common.search')}
      hasRemove
      value={text}
      onChangeText={handleChange}
    />
  )
}

export default SearchBar
