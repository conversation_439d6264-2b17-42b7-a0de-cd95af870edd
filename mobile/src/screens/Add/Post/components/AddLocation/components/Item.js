import React, { useState } from 'react'
import { Col, Text, Clickable } from 'components'
import { theme } from 'lib'

const Item = ({ item, handleSelect }) => {

  return (
    <Clickable onClick={() => handleSelect(item)} marg='12px 0'>
      <Col noFlex>
        <Text bold>{item.title}</Text>
        <Text b2 col={theme.GREY_60}>{item.subtitle}</Text>
      </Col>
    </Clickable>
  )
}

export default Item
