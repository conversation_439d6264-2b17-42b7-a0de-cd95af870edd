import React, { useState, useEffect } from 'react'
import { FlatList, SectionList } from 'react-native'
import { useTranslation } from 'react-i18next'
import { Api } from 'lib'
import { Modal, Dropdown, Col, Text, Spinner } from 'components'

import { Item, SearchBar } from './components'

let timer
const AddLocation = ({ selected, handleSelect }) => {
  const [searchResults, setSearchResults] = useState([]),
    [data, setData] = useState([]),
    [text, setText] = useState(''),
    [visible, setVisible] = useState(false),
    [loading, setLoading] = useState(false),
    { t } = useTranslation()

  useEffect(() => {
    if(!visible) return

    setData([
      {
        title: t('add.suggested'),
        data: []
      },
      {
        title: t('add.recent'),
        data: []
      }
    ])
  }, [visible])

  useEffect(() => {
    if(!visible) return

    searchLocations()
  }, [text, visible])

  async function searchLocations() {
    const getRecent = !text.length

    clearTimeout(timer)

    timer = setTimeout(async () => {
      setLoading(true)

      const res = getRecent
        ? await Api.get('')
        : await Api.post('', { text } )
      setLoading(false)

      if(!res) return

      getRecent
        ? setData(res)
        : setSearchResults(res)
    }, 300)
  }

  function handleChange(opt) {
    setText(opt)
  }

  function handleClick(item) {
    setVisible(false)
    handleSelect(item)
  }

  return (
    <React.Fragment>
      <Dropdown
        title={t('add.addLocation')}
        selected={selected?.title}
        marg='32px 0 0'
        onClick={() => setVisible(true)}
      />

      <Modal
        fullScreen
        title={t('add.addLocation')}
        dropdown
        visible={visible}
        closeModal={() => setVisible(false)}
      >
        <Col pad='0 16px'>
          <SearchBar
            text={text}
            handleChange={handleChange}
            setData={setData}
          />

          {text.length
            ? (
              <FlatList
                data={searchResults}
                renderItem={(props) => (
                  <Item
                    {...props}
                    handleSelect={handleClick}
                  />
                )}
              />
            ) : (
              <SectionList
                sections={data}
                renderItem={(props) => (
                  <Item
                    {...props}
                    handleSelect={handleClick}
                  />
                )}
                renderSectionHeader={({ section: { title, data } }) => !!data.length && (
                  <Col noFlex bg='#fff'>
                    <Text h2 marg='12px 0 4px'>{title}</Text>
                  </Col>
                )}
              />
            )
          }
        </Col>
      </Modal>
    </React.Fragment>
  )
}

export default AddLocation
