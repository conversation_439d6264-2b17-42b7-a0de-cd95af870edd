import React, { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { <PERSON><PERSON>View, FlatList, Switch, Platform } from 'react-native'
import { CommonActions } from '@react-navigation/native'
import ObjectId from 'bson-objectid'
import mime from 'mime'
import { useTranslation } from 'react-i18next'
import { createThumbnail } from 'react-native-create-thumbnail'
import { formatText, Api, uploadWithSignedUrl, getYtId, getAspectRatio, useIsMounted } from 'lib'
import { mediaCompressor, handlePermission } from 'core'
import { SegmentControl, Col, Row, Clickable, Icon, Text, Spinner, Input, Button, Dropdown } from 'components'

import { FriendsModal, MediaItem, AddLocation } from './components'
import { MediaPicker } from '../components'

let timer
const AddPost = ({ navigation, route }) => {
  const { postType, caption: paramsCaption, cameraMedia } = route.params || {},
    [caption, setCaption] = useState(paramsCaption || ''),
    [media, setMedia] = useState([]),
    [location, setLocation] = useState(null),
    [tagged, setTagged] = useState([]),
    [privacy, setPrivacy] = useState({ }),
    [metaData, setMetaData] = useState(null),
    [youtubeId, setYoutubeId] = useState(null),
    [loading, setLoading] = useState(false),
    [subscriptionOnly, setSubscriptionOnly] = useState(false),
    [showMediaPicker, setShowMediaPicker] = useState(false),
    [permissionGiven, setPermissionGiven] = useState(false),
    user = useSelector(state => state.user),
    { t } = useTranslation(),
    dispatch = useDispatch(),
    mounted = useIsMounted(),
    isAndroid = Platform.OS === 'android'

  const privacyOptions = [
    {
      value: t('settings.anyone'),
      option: 'anyone'
    },
    {
      value: t('settings.friendsOnly'),
      option: 'friends'
    },
    {
      value: t('settings.noOne'),
      option: 'noOne'
    }
  ]

  const showPicker = (showPicker = false) => {
    if (permissionGiven) {
      setShowMediaPicker(showPicker)
    } else {
      getPermission()
    }
  }

  async function getPermission() {
    const perm = await handlePermission({
      checkedPermission: 'gallery',
      onBlocked: () => handleBlocked(t('permissions.gallery'))
    })

    if(!mounted.current) return
    setPermissionGiven(perm)
  }

  function handleBlocked(permission) {
    navigation.navigate('PermissionBlocked', { permission })
  }

  useEffect(() => {
    if(postType !== 'media') return

    getPermission()
  }, [])

  useEffect(() => {
    if (permissionGiven) {
      showPicker(permissionGiven)
    } 
  }, [permissionGiven])

  useEffect(() => {
    if(!cameraMedia) return

    setMedia(cameraMedia?.length ? cameraMedia : [cameraMedia])
  }, [cameraMedia])

  function handleRemove(idx) {
    setMedia(media.filter((x, i) => i !== idx))
  }

  async function handleChange(val) {
    clearTimeout(timer)
    setCaption(val)

    setMetaData('')
    setMedia(media => media.filter(x => !x.isMetaData))
    setYoutubeId('')

    timer = setTimeout(async () => {


      /*
        Check if link is actually a URL
      */
      const regexp = /(ftp|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/
      const isURL = regexp.test(val)

      if(!isURL) return null

      /*
        Guard for initial delete button press while deleting and holding.
        Maybe we can extend this for some edgecases?
      */
      if(caption && caption.includes(val)) return
      setLoading(true)

      /*
        Check if URL is YT url or other website URL
      */
      const isYoutube = getYtId(val)

      if(isYoutube) {
        setMetaData(null)
        setYoutubeId(isYoutube)
        setMedia([{
          isMetaData: true,
          uri: `https://img.youtube.com/vi/${isYoutube}/0.jpg`,
          index: 0
        }])

        return setLoading(false)
      }

      /*
        Fetch metadata from our server
      */
      const fetchedMetaData = await Api.post('/posts/getMetaData', { link: val })
      setLoading(false)
      if(!fetchedMetaData) return null

      setYoutubeId(null)
      setMedia([{
        isMetaData: true,
        uri: fetchedMetaData.image || fetchedMetaData.icon,
        index: 0
      }])
      setMetaData(fetchedMetaData)
    }, 300)
  }

  async function submit() {
    const video = media.find(x => x.isVideo),
      { aspectRatio } =
        video
        || metaData
        || (!!media.length && media[0])
        || {},
      dimensions = getAspectRatio.toPixels(aspectRatio, 1080)

    dispatch({
      type: 'LOADING_BUTTON',
      payload: 'navigator'
    })

    const formatted = formatText(caption)

    const tags = formatted.filter(x => x.type === 'tag').map(x => x.text),
      mentioned = formatted.filter(x => x.type === 'mention').map(x => x.text)

    const documentId = ObjectId().toString()
    // const data = new FormData()
    let data = {}
    let res

    if(metaData) {
      data = {
        tagged: tagged.map(x => x._id),
        shareWith: privacy.option,
        caption,
        tags,
        mentioned,
        metaData,
        aspectRatio,
        subscriptionOnly
      }

      res = await Api.post('/posts/create', data)
    }

    /*
      Image post
    */
    if(!youtubeId && !video && !metaData) {
      const mapped = await Promise.all(media.map(async x => {
        if(x.originalPath) return x

        // Resize image
        const imgPath = await mediaCompressor.getPath(x.uri || x , 'image'),
          compressed = await mediaCompressor.image(
            imgPath,
            dimensions.width,
            dimensions.height,
            0.3
          )

        return {
          uri: compressed,
          type: mime.getType(compressed)
        }
      }))

      let newMedia = []

      for(let i = 0; i < mapped.length; i++) {
        const x = mapped[i]

        x.type = mime.getType(x.uri)

        const thumbnail = await mediaCompressor.image(x.uri, 216, 216, 1),
          thumbnailType = mime.getType(thumbnail),
          fileName = `${documentId}-${i+1}.${x.type.split('/')[1]}`,
          thumbnailName = `${documentId}-${i+1}-thumbnail.${thumbnailType.split('/')[1]}`

        let generatedData = {
          uri: fileName,
          thumbnail: thumbnailName
        }

        for await(let key of Object.keys(generatedData)) {
          const name = generatedData[key]

          const file = {
            uri: key === 'uri' ? x.uri : thumbnail,
            type: key === 'uri' ? x.type : thumbnailType,
            name: name
          }

          const location = await uploadWithSignedUrl(name, file.type, file)

          generatedData[key] = location
        }

        newMedia.push(generatedData)
      }

      data = {
        media: newMedia,
        tagged: tagged.map(x => x._id),
        shareWith: privacy.option,
        caption,
        aspectRatio,
        tags,
        mentioned,
        subscriptionOnly
      }

      res = await Api.post('/posts/create', data)
    }

    /*
      Youtube post
    */
    if(youtubeId && !video){
      res = await Api.post(
        '/posts/create',
        {
          youtubeId,
          caption,
          tags,
          tagged: tagged.map(x => x._id),
          shareWith: privacy.option,
          subscriptionOnly
        }
      )
    }

    /*
      Video post
    */
    if(video && !youtubeId) {
      let realPath = video.realPath

      /*
        Correct video path in Android
      */
      if (isAndroid) {
        realPath = realPath.match(/file:\/\//g)?.length > 1 ? realPath.replace('file://', '') : realPath
      }
      
      /*
        Compress video
      */
      const compressed = await mediaCompressor.video(realPath || video)

      /*
        Generate thumbnail
      */
      const thumbnail = await createThumbnail({
        url: compressed,
        timeStamp: 0,
      })
        .then(res => res)
        .catch(err => { err })

      if(thumbnail?.err) return alert(thumbnail.err)

      const videoType = mime.getType(compressed),
        thumbnailType = isAndroid ? thumbnail.mime : mime.getType(thumbnail.path),
        fileName = `${documentId}-1.${videoType.split('/')[1]}`,
        thumbnailName = `${documentId}-1-thumbnail.${thumbnailType.split('/')[1]}`

        let media = [{
          uri: fileName,
          thumbnail: thumbnailName,
          aspectRatio: video.aspectRatio
        }]

        for await(let f of media) {
          const keys = Object.keys(f)

          for await(let key of keys) {
            const name = f[key]
            if(key === 'aspectRatio') {
              media[0][key] = video.aspectRatio

              continue
            }

            const file = {
              uri: key === 'uri'
                ? compressed.replace('file://', 'file:///')
                : thumbnail.path,
              type: key === 'uri' ? videoType : thumbnailType,
              name: name,
            }

            const location = await uploadWithSignedUrl(name, file.type, file)

            media[0][key] = location
          }
        }

      data = {
        media,
        caption,
        tags,
        tagged: tagged.map(x => x._id),
        shareWith: privacy.option,
        aspectRatio: video.aspectRatio,
        mentioned,
        subscriptionOnly
      }

      res = await Api.post('/posts/create', data)
    }

    if(!res) {
      setLoading(false)

      dispatch({
        type: 'LOADING_BUTTON',
        payload: null
      })
    }

    dispatch({
      type: 'ADD_POST',
      payload: {
        ...res,
        owner: user
      }
    })

    dispatch({
      type: 'LOADING_BUTTON',
      payload: null
    })

    navigation.dispatch(
      CommonActions.navigate({
        name: 'Home'
      })
    )
  }
  
  return (
    <Col>
      <ScrollView>
        <Col pad='16px 16px 0'>
          <Input
            placeholder={t('add.writeCaption')}
            value={caption}
            onChangeText={(e) => handleChange(e)}
            multiline
            numberOfLines={5}
            autoFocus={postType === 'link'}
            ht='88px'
          />
        </Col>

        <Col pad='32px 0 0'>
          <FlatList
            data={media}
            keyExtractor={(item) => item.index}
            horizontal
            renderItem={(props) => (
              <MediaItem
                {...props}
                handleRemove={handleRemove}
              />
            )}
          />

        </Col>

        <Col pad={`${media.length ? 32 : 0}px 16px 0`}>
          <Button
            bordered
            icon='addPhotos'
            iconDimensions={21}
            text={t('add.addPhotosAndVideos')}
            onClick={() => showPicker(!showMediaPicker)}
          />

          <MediaPicker
            entityType='post'
            media={media}
            setMedia={setMedia}
            visible={showMediaPicker}
            closeModal={() => showPicker(false)}
          />

        {/*
          <AddLocation
            selected={location}
            handleSelect={setLocation}
          />
        */}

          <FriendsModal
            tagged={tagged}
            handleSelect={setTagged}
          />

          <Dropdown
            title={t('add.shareWith')}
            options={privacyOptions}
            selected={privacy}
            handleSelect={setPrivacy}
            marg='24px 0 0'
          />

          {user.type === 'athlete' && (
            <Row center marg='32px 0 0'>
              <Switch
                value={subscriptionOnly}
                onValueChange={setSubscriptionOnly}
              />

              <Text marg='0 0 0 12px'>{t('add.onlyForSubscribers')}</Text>
            </Row>
          )}
        </Col>
      </ScrollView>

      <Row pad='14px 16px 28px' noFlex bg='#fff'>
        <Button
          text={t('add.createPost')}
          onClick={submit}
          disabled={!media?.length && !caption?.length}
        />
      </Row>
    </Col>
  )
}

export default AddPost
