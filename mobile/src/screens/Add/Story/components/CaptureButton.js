import React, { useState, useEffect } from 'react'
import { Image, Pressable } from 'react-native'
import { Clickable, Text, Col } from 'components'

let timer
const CaptureButton = ({
  cameraRef,
  hasFlash,
  capturePhoto,
  onRecordingStart,
  onRecordingEnd,
  onDurationChange
}) => {
  const [recording, setRecording] = useState(false),
    [duration, setDuration] = useState(0)

  useEffect(() => {
    if(!recording) {
      clearInterval(timer)

      return
    }

    timer = setInterval(() => {
      setDuration(duration + 1)
    }, 1000)

    return () => clearTimeout(timer)
  })

  useEffect(() => {
    if(!recording || !onRecordingStart) return

    onRecordingStart()
  }, [recording])

  useEffect(() => {
    onDurationChange(duration)
    if(duration <= 15) return

    onPressOut()
  }, [duration])

  function onLongPress() {
    if(!cameraRef?.current) return

    setRecording(true)

    cameraRef.current.startRecording({
      flash: hasFlash ? 'on' : 'off',
      onRecordingFinished: (video) => onRecordingEnd(video),
      onRecordingError: (error) => console.error('err', error),
    })
  }

  async function onPressOut() {
    if(!cameraRef?.current) return

    setRecording(false)
    if(!recording) return

    setDuration(0)
    await cameraRef.current.stopRecording()
  }

  return (
    <Clickable
      onPress={capturePhoto}
      onLongPress={onLongPress}
      onPressOut={onPressOut}
    >
      <Image
        style={{ height: 48, width: 48 }}
        source={require('assets/images/capture-button.png')}
      />
    </Clickable>
  )
}

export default CaptureButton
