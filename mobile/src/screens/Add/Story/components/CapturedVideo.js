import React, { useState } from 'react'
import { getTopPadding } from 'core'
import Video from 'react-native-video'

const CapturedVideo = ({ source, deviceWid }) => {
  
  return (
    <Video
      resizeMode='cover'
      source={{ uri: source }}
      style={{
        height: undefined,
        width: deviceWid,
        aspectRatio: 9/16,
        marginTop: getTopPadding(true),
        position: 'absolute',
      }}
      paused={false}
      repeat
    />
  )
}

export default CapturedVideo
