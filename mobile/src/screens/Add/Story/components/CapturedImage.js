import React, { useState } from 'react'
import { getTopPadding } from 'core'
import { Image } from 'react-native'

const CapturedImage = ({ source, deviceWid }) => {
  return (
    <Image
      source={{ uri: source }}
      style={{
        height: undefined,
        width: deviceWid,
        aspectRatio: 9/16,
        marginTop: getTopPadding(true),
        position: 'absolute'
      }}
    />
  )
}

export default CapturedImage
