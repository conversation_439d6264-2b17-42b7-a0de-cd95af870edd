import React, { useState } from 'react'
import { Dimensions, Platform, Switch } from 'react-native'
import { useSelector, useDispatch } from 'react-redux'
import { useRoute } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import ObjectId from 'bson-objectid'
import { createThumbnail } from 'react-native-create-thumbnail'
import mime from 'mime'
import { theme, Api, uploadWithSignedUrl } from 'lib'
import { isNewIphone, mediaCompressor, useFocusListener } from 'core'
import { Header, Col, Row, Clickable, Icon, Text, Button, Spinner } from 'components'
import { CapturedImage, CapturedVideo } from './components'

async function getMedia(source) {
  const isVideo = !!source.path

  switch (true) {
    case isVideo:
      return await mediaCompressor.video(source.path)

    case !isVideo:
      const imgPath = await mediaCompressor.getPath(source.uri || source, 'image')
      return await mediaCompressor.image(imgPath, 1080, 1080, 0.8)
  }
}

async function getThumbnail(source) {
  const isVideo = mime.getType(source).includes('video')

  switch (true) {
    case isVideo:
      return await createThumbnail({
        url: source,
        timeStamp: 0,
      })
        .then(res => res)
        .catch(err => { err })

    case !isVideo:
      return await mediaCompressor.image(source, 216, 216, 1)
  }
}

const EditStory = ({ navigation }) => {
  const { t } = useTranslation(),
    [isPosting, setIsPosting] = useState(false),
    [subscriptionOnly, setSubscriptionOnly] = useState(false),
    { captured, duration } = useRoute().params || {},
    deviceWid = Dimensions.get('window').width,
    user = useSelector(state => state.user),
    dispatch = useDispatch(),
    isVideo = captured?.path,
    isAndroid = Platform.OS === 'android'

  useFocusListener((e) => {
    if (!e) return

    dispatch({
      type: 'UPDATE_BAR_STYLE',
      payload: 'light-content'
    })
  })

  async function submit() {
    setIsPosting(true)

    const documentId = ObjectId().toString()
    const compressedMedia = await getMedia(captured),
      mediaType = mime.getType(compressedMedia),
      thumbnail = await getThumbnail(compressedMedia),
      thumbnailType = thumbnail.mime || mime.getType(thumbnail.path || thumbnail),
      fileName = `${documentId}-1.${mediaType.split('/')[1]}`,
      thumbnailName = `${documentId}-1-thumbnail.${thumbnailType.split('/')[1]}`

    let generatedData = {
      uri: fileName,
      thumbnail: thumbnailName
    }

    for await (let key of Object.keys(generatedData)) {
      const name = generatedData[key]

      const file = {
        uri: key === 'uri'
          ? compressedMedia.replace('file://', 'file:///')
          : thumbnail.path || thumbnail,
        type: key === 'uri' ? mediaType : thumbnailType,
        name: name
      }

      const location = await uploadWithSignedUrl(name, file.type, file)

      generatedData[key] = location
    }

    const data = {
      media: generatedData,
      ownerType: user.type,
      duration: duration ? Number(duration.toFixed(2)) * 1000 : 15000,
      subscriptionOnly
    }

    const res = await Api.post('/stories/create', data)

    setIsPosting(false)
    if (!res) return

    dispatch({
      type: 'ADD_STORY',
      payload: {
        ...res,
        recent: filterOldStories(res.recent),
        owner: user
      }
    })

    dispatch({
      type: 'HANDLE_RES',
      payload: { res: t('add.storyPublished') }
    })

    navigation.navigate('AddNavigator')
  }

  function filterOldStories(stories) {
    const now = new Date(),
      cutoffTime = now.getTime() - (48 * 60 * 60 * 1000) // 48 hours in milliseconds

    return stories.filter(item => {
      const createdAtTime = new Date(item.createdAt).getTime()
      return createdAtTime >= cutoffTime
    })
  }

  return (
    <Col bg='#000'>
      {isPosting && <Spinner fullScreen />}

      <Header
        transparent
        lightContent
      >
        <Col centerAll>
          <Row wid='100%' between centerAll bg='rgba(0, 0, 0, 0.5)'>
            <Clickable
              wid='33%'
              pad='0 16px'
              hitSlop={20}
              onClick={() => navigation.goBack()}
            >
              <Icon type='arrowLeft' dimensions={16} col='#fff' />
            </Clickable>

            <Col centerAll wid='33%'></Col>

            <Col></Col>
          </Row>
        </Col>
      </Header>

      {isVideo ? (
        <CapturedVideo
          source={captured.path}
          deviceWid={deviceWid}
        />
      ) : (
        <CapturedImage
          source={
            (isAndroid && !captured.includes('file://') && !captured.includes('content://')) ?
              `file://${captured}` :
              captured
          }
          deviceWid={deviceWid}
        />
      )}

      <Col pad={isNewIphone() ? '0 0 42px' : '0 0 8px'} endAll>
        {user.type === 'athlete' && (
          <Row
            center
            ht='48px'
            pad='0 16px'
            bg='rgba(0, 0, 0, 0.5)'
            marg='0 0 32px'
            noFlex
          >
            <Switch
              value={subscriptionOnly}
              onValueChange={setSubscriptionOnly}
            />

            <Text col='#fff' marg='0 0 0 12px'>{t('add.onlyForSubscribers')}</Text>
          </Row>
        )}

        <Col marg='0 16px' noFlex>
          <Button
            text={isPosting ? 'Posting...' : 'Create story'}
            bg={theme.SECONDARY}
            onClick={!isPosting && submit}
            disabled={isPosting}
            action='addStory'
          />
        </Col>
      </Col>
    </Col>
  )
}

export default EditStory
