import React, { useState, useEffect } from 'react'
import { Platform } from 'react-native'
import { useActionSheet } from '@expo/react-native-action-sheet'
import { useDispatch, useSelector } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { Modal, Clickable, Row, Text } from 'components'

import MediaPicker from './components/MediaPicker'

const MediaPickerModal = ({ media, setMedia, onSave, visible, closeModal, noSelectMultiple, assetType, entityType, notAddScreen }) => {
  const [selected, setSelected] = useState([]),
    [isMultiple, setIsMultiple] = useState(false),
    [temporaryHide, setTemporaryHide] = useState(false),
    { t } = useTranslation(),
    { showActionSheetWithOptions } = useActionSheet(),
    dispatch = useDispatch(),
    isAndroid = Platform.OS === 'android',
    isStory = entityType === 'story'

  useEffect(() => {
    if(!visible) return

    setIsMultiple(media?.length > 1)
    setSelected(selected.filter(x => media?.find(y => y.index === x.index)))
  }, [visible])

  function handleSelect(item) {
    const { uri, isVideo } = item

    if(isMultiple) {
      const alreadySelected = selected.find(x => x.uri === uri)

      if(alreadySelected && selected.length === 1) return

      if(alreadySelected) {
        return setSelected(selected.filter(x => x.uri !== uri))
      }

      /*
        If selected is video, prompt error to remove other selections
        So we include only a single video
      */
      if(isVideo) {
        return handleVideoSelectMultiple({ file: item })
      }

      if(selected.length === 10) return

      return setSelected([...selected, item])
    }

    setSelected([item])
  }

  function handleSave() {
    setMedia && setMedia(selected)
    onSave && onSave(selected)
    closeModal()
  }

  function handleAddMultiple({ file }) {
    if(!isMultiple) {
      /*
        Check if selection includes videos,
        if so alert user
      */
      const hasVideo = selected.find(x => x.isVideo)
      if(hasVideo) {
        dispatch({
          type: 'HANDLE_RES',
          payload: { err: (t('add.onlySingleVideo')) }
        })

        return
      }

      setIsMultiple(true)
      return
    }

    setIsMultiple(false)
    setSelected([file || selected[0]])
  }

  function handleVideoSelectMultiple({ file }) {
    isAndroid && setTemporaryHide(true)
    showActionSheetWithOptions(
      {
        options: [t('add.selectVideo'), t('common.cancel')],
        destructiveButtonIndex: [0],
        cancelButtonIndex: 1,
        title: t('add.selectionsWillRemove'),
        userInterfaceStyle: 'light'
      },
      (buttonIndex) => {
        switch (buttonIndex) {
          case 0:
            handleAddMultiple({ file })
            setTemporaryHide(false)
            break
          case 1:
            setTemporaryHide(false)
            break
        }
      }
    )
  }

  return (
    <Modal
      fullScreen
      visible={visible && !temporaryHide}
      closeModal={closeModal}
      title={t('add.photosAndVideos')}
      headerRightContent={
        <Clickable onClick={handleSave}>
          <Text bold>{t('common.save')}</Text>
        </Clickable>
      }
      closeIconDimensions={14}
    >
      <MediaPicker
        closeModal={closeModal}
        selected={selected}
        visible={visible}
        handleSelect={handleSelect}
        setSelected={setSelected}
        handleAddMultiple={handleAddMultiple}
        isMultiple={isMultiple}
        entityType={entityType}
        noSelectMultiple={noSelectMultiple}
        assetType={assetType}
        notAddScreen={notAddScreen}
      />
    </Modal>
  )
}

export default MediaPickerModal
