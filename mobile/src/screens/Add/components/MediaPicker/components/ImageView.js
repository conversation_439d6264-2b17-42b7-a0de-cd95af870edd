import React, { useState, useEffect, useRef } from 'react'
import { Dimensions } from 'react-native'
import Animated, { useSharedValue, useAnimatedStyle, withTiming } from 'react-native-reanimated'
import { PanGestureHandler, Gesture, GestureDetector, State } from 'react-native-gesture-handler'
import styled from 'styled-components'
import { ExpandButton } from './'
import { Col } from 'components'

const Image = styled.Image`
  width: 100%;
  height: 100%;
`

const ImageView = ({ src, onAspectRatioChange, entityType }) => {
  const deviceWidth = Dimensions.get('screen').width,
    [aspectRatio, setAspectRatio] = useState(0),
    [originalRatio, setOriginalRatio] = useState(0),
    [width, setWidth] = useState(0),
    [height, setHeight] = useState(0),
    [panState, setPanState] = useState([0, 0]),
    [active, setActive] = useState(false),
    offset = useSharedValue({ x: 0, y: 0 }),
    startOffset = useSharedValue({ x: 0, y: 0 }),
    ref = useRef(),
    isStory = entityType === 'story',
    imageStyles = isStory 
      ? { aspectRatio }
      : { width: width || '100%', height: height || '100%' }

  useEffect(() => {
    const startingRatio = isStory ? 9 / 16 : 1

    setWidth(0)
    setHeight(0)
    setAspectRatio(startingRatio)
    onAspectRatioChange(startingRatio)
    setOriginalRatio(src.originalRatio)
  }, [src.uri])

  useEffect(() => {
    getDimensions()
    
  }, [aspectRatio, src.uri])

  function getDimensions() {
    if(aspectRatio === 1) {
      setHeight(deviceWidth)
      setWidth(deviceWidth)
    }

    if(!aspectRatio) return

    const decrease = deviceWidth - (deviceWidth / aspectRatio)

    return aspectRatio < 1
      ? setWidth(deviceWidth + decrease)
      : setHeight(deviceWidth - decrease)
  }

  function changeRatio() {
    const newRatio = aspectRatio === 1
      ? originalRatio
      : 1

    setAspectRatio(newRatio)
    onAspectRatioChange(newRatio, src.index)
  }

  return (
    <Col
      wid={`${deviceWidth}px`}
      ht={`${deviceWidth}px`}
      noFlex
      style={{ overflow: 'hidden' }}
      center
    >
      <Image
        source={{ uri: src.uri }}
        style={imageStyles}
      />

      {entityType === 'post' && (
        <ExpandButton onClick={changeRatio} />
      )}
    </Col>
  )
}

export default ImageView

// const animatedStyle = useAnimatedStyle(() => {
//   return {
//     transform: [
//       { translateX: offset.value.x },
//       { translateY: offset.value.y }
//     ]
//   }
// })

// <GestureDetector
//   gesture={panGesture}
// >
//   <Animated.View
//     style={[
//       {
//         height: height || '100%',
//
//         zIndex: 3
//       },
//       animatedStyle
//     ]}
//   >
//     <Image source={{ uri: src.uri }} />
//   </Animated.View>
// </GestureDetector>

// const panGesture = Gesture
//   .Pan({ manualActivation: true })
//   .runOnJS(true)
//   .withRef(ref)
//   .onBegin((e) => {
//     offset.value = startOffset.value
//   })
//   .onUpdate((e) => {
//     // if(!active) {
//     //   offset.value = startOffset.value
//     //
//     //   return setActive(true)
//     // }
//
//     offset.value = {
//       x: e.translationX,
//       y: e.translationY
//     }
//   })
//   .onEnd((e) => {
//     setActive(false)
//     startOffset.value = {
//       x: e.translationX,
//       y: e.translationY
//     }
//   })
