import React, { useState, useRef, useEffect, memo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Animated, FlatList, Dimensions, Platform, View, PermissionsAndroid, Image } from 'react-native'
import styled from 'styled-components'
import { CameraRoll } from '@react-native-camera-roll/camera-roll'
import { useNavigation, useFocusEffect } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import { Col, Row, Text, Clickable, Icon, Spinner, Input } from 'components'
import { mediaCompressor, handlePermission, useFocusListener, getMediaFromGallery } from 'core'
import { theme, getAspectRatio, useIsMounted } from 'lib'
import { ListItem, VideoView, ImageView } from './'

const CollapseButtonContainer = styled.View`
  width: 50px;
  height: 50px;
  border-radius: 60px;
  position: absolute;
  bottom: 16px;
  right: 16px;
  align-items: center;
  justify-content: center;
  background-color: ${theme.GREY_30};
  opacity: 0.8;
`

const SelectedMediaContainer = styled.View`
  align-items: center;
  justify-content: center;
  ${props => `
    width: ${props.dimensions}px;
    height: ${props.dimensions}px;
  `}
`

const MediaPicker = ({
  selected = [],
  handleSelect = () => null,
  hidePicker = false,
  setSelected,
  updateRatio,
  setVideoDetails,
  handleAddMultiple,
  isMultiple,
  assetType = 'All',
  entityType,
  visible,
  entityParams,
  HeaderContent,
  marginTop,
  paddingTop,
  outerRef,
  onShoutoutInputFocus,
  closeModal,
  shoutoutInputRef,
  noSelectMultiple,
  notAddScreen
}) => {
  const [images, setImages] = useState({ edges: [] }),
    [showSelected, setShowSelected] = useState(true),
    [screenFocused, setScreenFocused] = useState(true),
    [showScrollButton, setShowScrollButton] = useState(false),
    [permissionGiven, setPermissionGiven] = useState(false),
    [selectedWithin, setSelectedWithin] = useState(selected.length ? selected[0] : undefined),
    [loading, setLoading] = useState(true),
    deviceWidth = Dimensions.get('screen').width,
    height = useRef(new Animated.Value(deviceWidth)).current,
    { t } = useTranslation(),
    isAndroid = Platform.OS === 'android',
    navigation = useNavigation(),
    listRef = outerRef || useRef(),
    itemDimensions = (deviceWidth - 6) / 4,
    mounted = useIsMounted()
    isMediaShare = entityType === 'mediaShare'

  useEffect(() => {
    if(!visible) return

    setScreenFocused(visible)
    if(visible) getPermission()
  }, [visible])

  async function getPermission() {
    const perm = await handlePermission({
      checkedPermission: 'gallery',
      onBlocked: () => handleBlocked(t('permissions.gallery'))
    })

    if(!mounted.current) return
    setPermissionGiven(perm)
  }

  useEffect(() => {
    if(!permissionGiven || hidePicker || !mounted.current) return

    setTimeout(() => getPhotos(true), 300)
  }, [permissionGiven, hidePicker])

  // Get photos after from library
  async function getPhotos(isInitial) {
    if(images.hasNextPage === false) return
    if(!isInitial && !images.edges.length) return

    if(isAndroid) {
      const granted = await PermissionsAndroid
        .request(PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE)

      if(!granted) return
    }

    if(!mounted.current) return

    const { page_info, edges } = await CameraRoll
      .getPhotos({
        first: 100,
        after: images.finalImage || undefined,
        assetType,
        includes: ['playableDuration']
      })

    const filtered = images.edges.filter(x => {
      const item = x.node || x
      const duration = item.playableDuration || item?.image?.playableDuration

      if(duration && duration > 60) return false
      return true
    })

    if(!mounted.current) return 

    setImages({
      edges: [...filtered, ...edges],
      hasNextPage: page_info.has_next_page,
      finalImage: page_info.end_cursor
    })

    // Select first image from library on initial screen load
    if(!selected.length) {
      const item = edges[0]?.node
      onSelect({ item, index: entityType === 'story' || isMediaShare ? 0 : 1 })
    }
  }

  /*
    Returns selected media with detailed info
  */
  async function onSelect({ item, index, duration }) {
    setLoading(true)

    if(!mounted.current) return

    const file = await getMediaFromGallery(item)
    const size = await new Promise((resolve) => {
      Image.getSize(item?.image?.uri, (width, height) => {
        resolve({ width, height })
      })
    })
    const aspectRatio = getAspectRatio.toRatio(size.height, size.width)
    const isVideo = item.type.includes('video')

    const newItem = {
      uri: file.uri || file,
      realPath: file?.realPath,
      originalRatio: aspectRatio,
      duration: duration,
      aspectRatio,
      isVideo,
      index
    }

    if(!mounted.current) return

    setSelectedWithin(newItem)
    handleSelect(newItem)

    setLoading(false)
  }

  function handleCamera() {
    closeModal()
    setTimeout(() => navigation.navigate('Capture', { type: entityType, notAddScreen }), 300)
  }

  function handleBlocked(permission) {
    navigation.navigate('PermissionBlocked', { permission })
  }

  function scrollToTop() {
    listRef?.current.scrollToOffset({ offset: 0, animated: true })
  }

  /*
    Show scroll to top button after a certain offset
  */
  function handleScroll({ nativeEvent }) {
    const offset = nativeEvent.contentOffset.y

    if(offset > 220) return setShowScrollButton(true)
    setShowScrollButton(false)
  }

  function onAspectRatioChange(ratio, idx = 0) {
    if(entityType === 'shoutout') return
    const mapped = selected.map((x) => {
      if(x.index !== idx) return x

      return { ...x, aspectRatio: ratio }
    })

    setSelected(mapped)
  }

  return (
    <Col>
      <FlatList
        ref={listRef}
        onScroll={handleScroll}
        scrollEventThrottle={300}
        data={hidePicker ? [] : [...(entityType !== 'story' && !isMediaShare ? ['openCamera'] : []), ...images.edges]}
        contentContainerStyle={{ paddingTop }}
        numColumns={4}
        ItemSeparatorComponent={() => <View style={{ width: 30, height: 2 }}/>}
        onEndReached={({ distanceFromEnd }) => distanceFromEnd && getPhotos()}
        onEndReachedThreshold={1}
        ListHeaderComponent={(
          <Col noFlex>
            {HeaderContent && <HeaderContent />}

            {!hidePicker && (
              <React.Fragment>
                <Animated.View style={{ height, width: deviceWidth, marginTop }}>
                  {loading ? (
                      <Col centerAll ht='100%'>
                        <Spinner/>
                      </Col>
                    ) : selected[0] && showSelected && (
                      selected[0]?.isVideo ? (
                        <VideoView
                          src={selected[0]}
                          entityType={entityType}
                          onAspectRatioChange={onAspectRatioChange}
                          paused={!screenFocused}
                        />
                      ) : selected.length && (
                        <ImageView
                          src={selectedWithin}
                          entityType={entityType}
                          onAspectRatioChange={onAspectRatioChange}
                        />
                      )
                    )
                  }
                </Animated.View>

                <Row
                  pad='0 0 0 16px'
                  centerAll
                  ht='55px'
                  between
                  noFlex
                >
                  <Text>{t('add.gallery')}</Text>

                  <Row noFlex>
                    {(handleAddMultiple && !noSelectMultiple && entityType === 'post') && (
                      <Row noFlex center >
                        {isMultiple && (
                          <Text col={theme.GREY_60} >
                            {t('add.selectUpTo10')}
                          </Text>
                        )}

                        <Clickable onClick={handleAddMultiple}>
                          <Row
                            centerAll
                            noFlex
                            pad='4px'
                            bg={isMultiple ? theme.SECONDARY : theme.GREY_20}
                            wid='32px'
                            ht='32px'
                            hasRadius='16px'
                            marg='0 16px 0 12px'
                          >
                            <Icon
                              type='clone'
                              dimensions={19}
                              col={isMultiple ? '#fff' : '#000'}
                            />
                          </Row>
                        </Clickable>
                      </Row>
                    )}
                  </Row>
                </Row>
              </React.Fragment>
            )}
          </Col>
        )}
        renderItem={(props) => {
          return props.item === 'openCamera'
            ? (
              <Clickable
                marg='0 2px 0 0'
                onClick={handleCamera}
              >
                <Row
                  noFlex
                  wid={`${itemDimensions}px`}
                  ht={`${itemDimensions}px`}
                  bg={theme.SECONDARY}
                  centerAll
                >
                  <Icon type='camera' dimensions={20} col='#fff' />
                </Row>
              </Clickable>
            ) : (
              <ListItem
                {...props}
                isMultiple={isMultiple}
                isSelected={!!selected.find(x => x.index === props.index)}
                selectedIdx={(selected.findIndex(x => x.index === props.index) || 0) + 1}
                handleSelect={onSelect}
                itemDimensions={itemDimensions}
              />
            )
        }}
      />

      {showScrollButton && (
        <Clickable onClick={scrollToTop}>
          <CollapseButtonContainer>
            <Icon
              type='up'
              dimensions={20}
            />
          </CollapseButtonContainer>
        </Clickable>
      )}
    </Col>
  )
}

export default MediaPicker
