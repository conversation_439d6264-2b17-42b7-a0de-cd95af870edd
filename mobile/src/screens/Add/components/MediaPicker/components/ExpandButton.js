import React, { useState } from 'react'
import styled from 'styled-components'
import { Icon, Col } from 'components'

const Button = styled.TouchableOpacity`
  width: 40px;
  height: 40px;
  border-radius: 60px;
  background-color: #fff;
  position: absolute;
  bottom: 16px;
  left: 16px;
  background-color: rgba(210, 216, 223, 0.7);
`

const ExpandButton = ({ onClick, disabled }) => {

  return (
    <Button onPress={onClick} disabled={disabled}>
      <Col centerAll>
        <Icon type='expand' dimensions={18} />
      </Col>
    </Button>
  )
}

export default ExpandButton
