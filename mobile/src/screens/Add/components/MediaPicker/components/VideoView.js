import React, { useState, useEffect, memo } from 'react'
import { Dimensions } from 'react-native'
import styled from 'styled-components'
import Video from 'react-native-video'
import { generateFilePath } from 'react-native-compressor'
import { Col, Icon } from 'components'
import { ExpandButton } from './'

const VideoView = memo(props => {
  const {
    src,
    marg,
    paused,
    onAspectRatioChange,
    preview,
    entityType
  } = props

  const deviceWidth = Dimensions.get('screen').width
  const [aspectRatio, setAspectRatio] = useState(0)
  const [originalRatio, setOriginalRatio] = useState(0)
  const [width, setWidth] = useState(0)
  const [height, setHeight] = useState(0)
  const isStory = entityType === 'story'
  const videoStyles = isStory 
    ? { aspectRatio, height: '100%', width: undefined }
    : { width: width || '100%', height: height || '100%' }

  useEffect(() => {
    const startingRatio = isStory ? 9 / 16 : 1

    setWidth(0)
    setHeight(0)
    setAspectRatio(startingRatio)
    setOriginalRatio(src.aspectRatio)
    !preview && onAspectRatioChange(startingRatio)
  }, [src.uri])

  useEffect(() => {
    getDimensions()
  }, [aspectRatio])

  function getDimensions() {
    if(aspectRatio === 1) {
      setHeight(deviceWidth)
      setWidth(deviceWidth)
    }

    if(!aspectRatio) return

    const decrease = deviceWidth - (deviceWidth / aspectRatio)

    return aspectRatio < 1
      ? setWidth(deviceWidth + decrease)
      : setHeight(deviceWidth - decrease)
  }

  function changeRatio() {
    const newRatio = aspectRatio === 1
      ? originalRatio
      : 1

    setAspectRatio(newRatio)
    onAspectRatioChange(newRatio)
  }

  return (
    <Col
      noFlex
      ht={`${deviceWidth}px`}
      wid={`${deviceWidth - (preview ? 32 : 0)}px`}
      style={{ overflow: 'hidden' }}
      center
    >
      <Video
        resizeMode='cover'
        source={{ uri: src.realPath || src }}
        style={{
          ...videoStyles,
          alignSelf: 'center'
        }}
        paused={paused}
        repeat
      />

      {entityType === 'post' && (
        <ExpandButton onClick={changeRatio} />
      )}
    </Col>
  )
})

export default VideoView
