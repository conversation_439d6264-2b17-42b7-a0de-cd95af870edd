import React, { useState, memo, useEffect } from 'react'
import styled from 'styled-components'
import { Dimensions } from 'react-native'
import { theme, getDuration } from 'lib'
import { Icon, Clickable, Text } from 'components'

const Item = styled.ImageBackground`
  ${props => `
    width: ${props.dimensions}px;
    height: ${props.dimensions}px;
    margin-right: 2px;
  `}
`

const InnerWrapper = styled.View`
  flex: 1;

`

const SelectedIconContainer = styled.View`
  position: absolute;
  width: 24px;
  height: 24px;
  top: 12px;
  left: 12px;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  background-color: ${props => props.isSelected
    ? theme.SECONDARY
    : 'transparent'
  };
  border: 2px solid #fff;
`

const DurationContainer = styled.View`
  position: absolute;
  display: flex;
  flex: 0;
  align-items: center;
  flex-direction: row;
  bottom: 17px;
  right: 41px;
`

const ListItem = ({
  index,
  item,
  isSelected,
  selectedIdx,
  handleSelect,
  isMultiple,
  itemDimensions
}) => {
  item = item.node || item
  const uri = item.uri || item.image.uri,
    duration = item.playableDuration || item.image.playableDuration,
    isVideo = item.type.includes('video')

  return (
    <Clickable onClick={() => {
      handleSelect({ uri, item: item.node || item, index, duration })
    }}>
      <Item
        dimensions={itemDimensions}
        source={{ uri }}
      >
        <InnerWrapper>
          <SelectedIconContainer isSelected={isSelected}>
            {isSelected && (
              isMultiple ? (
                <Text
                  c2
                  bold
                  col='#fff'
                  size='12px'
                >
                  {selectedIdx}
                </Text>
              ) : (
                <Icon
                  type='check'
                  dimensions={12}
                  col='#fff'
                />
              )
            )}
          </SelectedIconContainer>

          {!!duration && (
            <DurationContainer>
              <Icon type='play' col='#fff' dimensions={11} marg='0 3.5px 0 0'/>

              <Text c2 bold col='#fff'>
                {getDuration(duration?.toFixed() || 0)}
              </Text>
            </DurationContainer>
          )}
        </InnerWrapper>
      </Item>
    </Clickable>
  )
}

export default memo(ListItem)
