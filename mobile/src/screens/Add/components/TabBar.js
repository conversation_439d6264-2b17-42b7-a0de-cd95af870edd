import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Col, Row, Header, SegmentControl, Clickable, Icon, Text } from 'components'
import { useTranslation } from 'react-i18next'
import { Api } from 'lib'

const TabBar = ({ state, navigation, position, fromAddNavigator }) => {
  const [visible, setVisible] = useState(true),
    [isFetching, setIsFetching] = useState(false),
    isLive = useSelector(state => state.appState.isLive),
    { t } = useTranslation(),
    dispatch = useDispatch()

  async function handleSelect(idx) {
    switch (idx) {
      case 0:
        return navigation.navigate({ name: 'Story', merge: true })
      case 1:
        //  return navigation.navigate({ name: 'Live', merge: true })
        // Show coming soon modal for Live
        dispatch({
          type: 'SHOW_COMING_SOON',
          payload: {
            comingSoonDetails: {
              title: t('common.goLive'),
              visible: true,
            },
          },
        })
        return
    }
  }

  const getUnreadNotifications = async () => {
    if (isFetching) return

    setIsFetching(true)
    const res = await Api.get('/notifications/getUnread')

    setIsFetching(false)

    if (!res) return

    dispatch({
      type: 'NEW_NOTIFICATIONS',
      payload: res
    })

    dispatch({
      type: 'UPDATE_NOTIF_COUNT',
      payload: res.length
    })
  }

  if (!visible || isLive) return null

  return (
    <Col noFlex bg='#fff'>
      <Header>
        <Col centerAll>
          <Row noFlex wid='100%' between>
            <Row relative wid='0' noFlex center>
              <Clickable
                pad='0 16px'
                onClick={() => {
                  getUnreadNotifications()

                  fromAddNavigator
                    ? navigation.getParent().navigate('Home')
                    : navigation.getParent().goBack()
                }
                }
                hitSlop={20}
              >
                <Icon type={'close'} dimensions={14} />
              </Clickable>
            </Row>

            <Text h5>{t('common.createNew')}</Text>

            <Row noFlex />
          </Row>
        </Col>
      </Header>

      <Row pad='12px 16px 16px' noFlex>
        <SegmentControl
          options={[t('common.story'), t('add.live')]}
          selectedIdx={state.index}
          handleSelect={(idx) => handleSelect(idx)}
        />
      </Row>
    </Col>
  )
}

export default TabBar
