import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Api, theme } from 'lib'
import { Col, Text, Row, Clickable } from 'components'

const CardItem = ({ item, updatingCards, setUpdatingCards }) => {
  const { t } = useTranslation()

  function handleVisible() {
    setVisible(!visible)
  }

  async function handleSelect() {
    if(item.active) return
    setUpdatingCards(true)

    await Api.post('/billing/changeActiveCard', { cardId: item.cardId }, 'navigator')

    setTimeout(() => {
      setUpdatingCards(false)
    }, 500)
  }

  return (
    <Clickable onClick={handleSelect}>
      <Col
        marg='12px 0 0'
        noFlex
        pad='11px 16px'
        bg={item.active ? theme.SECONDARY : '#fff'}
        hasBorder={!item.active ? `1px solid ${theme.GREY_30}` : undefined}
        >
        <Row center>
          <Row
            noFlex
            hasRadius='20px'
            wid='20px'
            ht='20px'
            hasBorder={`2px solid ${item.active ? '#fff' : '#000'}`}
            centerAll
          >
            {item.active && (
              <Row
                noFlex
                hasRadius='20px'
                wid='10px'
                ht='10px'
                bg='#fff'
              />
            )}
          </Row>

          <Col marg='0 0 0 16px' noFlex>
            <Text col={item.active ? '#fff' : '#000'}>
              **** {item.last4}
            </Text>

            <Text col={item.active ? '#fff' : '#000'}>
              {t('billing.exp')}: {item.exp}
            </Text>
          </Col>
        </Row>
      </Col>
    </Clickable>
  )
}

export default CardItem
