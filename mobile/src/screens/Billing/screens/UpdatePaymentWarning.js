import React, { useState } from 'react'
import { IconBg } from 'screens/ForgotPassword/components'
import { Dimensions, Linking } from 'react-native'
import { Col, Row, Text, Button, Clickable, Icon } from 'components'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'react-redux'
import { isNewIphone } from 'core'
import { theme } from 'lib'

const UpdatePaymentWarning = ({ navigation }) => {
  const { t } = useTranslation(),
    deviceHt = Dimensions.get('screen').height,
    isSmallScreen = deviceHt < 800,
    user = useSelector(state => state.user)

  function handleClick() {
    Linking.openURL(`https://playaz4playaz.com/billing?email=${user.email}&navigateTo=billing`)
  }

  return (
    <Col>
      <Col bg='#fff' center noFlex ht='100%'>
        <IconBg
          source={require('assets/branding/partial-logo-icon-grey-10.png')}
          isSmallScreen={isSmallScreen}
        >
          <Row
            noFlex
            wid='132px'
            ht='132px'
            marg={isNewIphone() ? '128px 0 0' : '64px 0 0'}
            centerAll
            hasRadius='300px'
            bg={theme.SECONDARY}
          >
            <Icon type='roundedQuestionMark' dimensions='60' col='#fff' />
          </Row>
        </IconBg>

        <Col pad='0 16px' center maxWid='350px'>
          <Text h2 align='center'>
            {t('billing.youNeedToAddMethod')}
          </Text>

          <Text marg='35px 0 0' wid='320px' b1 col={theme.GREY_60} align='center'>
            {t('billing.forYourPurches')}
          </Text>
        </Col>

        <Col
          noFlex
          center
          wid='100%'
          pad='0 16px 54px'
        >
          <Button
            big
            text={t('billing.goToWebApp')}
            onClick={handleClick}
          />

          <Clickable onClick={() => navigation.goBack()} marg='20px 0 0'>
            <Text b2 bold>Go back</Text>
          </Clickable>
        </Col>
      </Col>
    </Col>
  )
}

export default UpdatePaymentWarning
