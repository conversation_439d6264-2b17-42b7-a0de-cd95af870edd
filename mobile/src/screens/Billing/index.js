import React, { useState } from 'react'
import { useNavigation, useRoute } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import { useDispatch, useSelector } from 'react-redux'
import { FlatList, Linking, Switch } from 'react-native'
import { isNewIphone } from 'core'
import ObjectId from 'bson-objectid'
import { Col, Row, Text, Icon, Clickable, Header, Button, SwipeButton } from 'components'
import { theme, Api } from 'lib'
import { CardItem } from './components'


/**
 * This is a reusable billing screen,
 * server handles the checkout related to cart or product
 *
 *  { data } will be posted to action, the returned res will be dispatched to dispatchAction,
 * if there is a pre-set, payload it will be sent instead,
 * after the post request, it will navigate to goTo
 */
const Billing = ({  }) => {
  const {
      totalValue,
      originalPrice,
      data,
      endpoint,
      dispatchAction,
      payload,
      goTo
  } = useRoute().params,
    [updatingCards, setUpdatingCards] = useState(false),
    [termsAccepted, setTermsAccepted] = useState(false),
    user = useSelector(state => state.user),
    navigation = useNavigation(),
    dispatch = useDispatch(),
    { t } = useTranslation(),
    { currency, cards } = user.billing,
    totalCostText = (totalValue / 100)
      .toLocaleString(
        undefined, {
          style: 'currency',
          currency: currency || 'usd'
        }
      )

  async function handleSwipe() {
    const res = await Api.post(endpoint, data, 'navigator')
    if(!res) return

    if(dispatchAction) {
      dispatch({
        type: dispatchAction,
        payload: payload || res
      })
    }

    navigation.navigate(goTo)
  }

  function goToBilling() {
    Linking.openURL(`https://playaz4playaz.com/billing?email=${user.email}&navigateTo=billing`)
  }

  return (
    <React.Fragment>
      <Col bg='#fff'>
        <Header>
          <Row center between pad='0 21px'>
            <Row relative noFlex wid='0'>
              <Clickable onClick={() => navigation.goBack()}>
                <Icon type='close' dimensions={14} />
              </Clickable>
            </Row>

            <Text h5>{t('billing.paymentMethod')}</Text>

            <Row noFlex />
          </Row>
        </Header>

        <Col pad='0 16px' marg='24px 0 0'>
          <Row between noFlex marg='0 0 12px'>
            <Text h2>{t('common.total')}</Text>

            <Col noFlex>
              <Text h2>
                <Text h2 col={theme.GREY_60}>{totalCostText.slice(0, 1)}</Text>
                {totalCostText.slice(1)}
              </Text>

              {originalPrice && (
                <Text
                  align='right'
                  col={theme.GREY_60}
                  style={{
                    textDecorationLine: 'line-through',
                    textDecorationStyle: 'solid'
                  }}
                >
                  {(originalPrice / 100)
                    .toLocaleString(
                      undefined, {
                        style: 'currency',
                        currency: currency || 'usd'
                      }
                    )
                  }
                </Text>
              )}
            </Col>
          </Row>

          <FlatList
            data={cards}
            keyExtractor={(item) => ObjectId().toString()}
            renderItem={({ item }) => (
              <CardItem
                item={item}
                updatingCards={updatingCards}
                setUpdatingCards={setUpdatingCards}
              />
            )}
            ListFooterComponent={
              <Button
                marg='24px 0 0'
                bordered
                text={t('billing.addPaymentMethod')}
                icon='add'
                onClick={goToBilling}
              />
            }
          />
        </Col>

        <Col noFlex bg='#fff' pad='16px' style={{ paddingBottom: isNewIphone() ? 37 : 16}}>
          <Row noFlex wid='100%' marg='0 0 24px' ht='54px'>
            <Switch
              value={termsAccepted}
              onValueChange={setTermsAccepted}
              ios_backgroundcolor={theme.GREEN}
            />

            <Text marg='0 0 0 12px' b2 col={theme.GREY_50} wid='290px'>
              {t('billing.termsAndConditions')}
            </Text>
          </Row>

          <SwipeButton
            disabled={!termsAccepted}
            thumbColor={theme.GREEN}
            title={t('billing.swipeRightToPurchase')}
            onClick={handleSwipe}
          />
        </Col>
      </Col>
    </React.Fragment>
  )
}

export default Billing
