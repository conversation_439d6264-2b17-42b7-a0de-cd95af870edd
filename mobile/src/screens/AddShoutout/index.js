import React, { useState, useRef, useMemo } from 'react'
import { ScrollView, View } from 'react-native'
import { useRoute, useNavigation } from '@react-navigation/native'
import { useDispatch } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { Api, theme } from 'lib'
import { getTopPadding } from 'core'
import styled from 'styled-components'
import { KeyboardAvoidingView, Platform } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  Input,
  Col,
  Row,
  Text,
  Clickable,
  Icon,
  Spinner,
  Avatar,
  SegmentControl,
  Header
} from 'components'

import Shoutout from 'screens/Add/Shoutout/components/CreateShoutout'

const SegmentWrapper = styled.View`
  margin: 32px 16px 0;
`

const ShoutoutModal = ({ navigation }) => {
  const [shoutoutType, setShoutoutType] = useState(0),
    [loading, setLoading] = useState(false),
    [comment, setComment] = useState(''),
    { user, eventId, eventObject, cameraMedia } = useRoute().params,
    { t } = useTranslation(),
    dispatch = useDispatch(),
    insets = useSafeAreaInsets();

  async function handleNext() {
    setLoading(true)

    const data = {
      type: user.type,
      comment,
      entityId: user._id,
      otherUser: user
    }

    navigation.push('ShoutoutConfirmation', data)
  }

  const styles = useMemo(() => ({
    container: {
      flex: 1,
      backgroundColor: '#fff',
      paddingBottom: insets.bottom,
    }
  }), [])

  return (
    <View style={{flex:1}}>
  
      <View style={styles.container}>
        {!!eventObject ? 
          <Header
            // title={t('shoutout.createMatchDayShoutout')}
            hasBack
            isModal
            transparent
            bg='#000'
            lightContent
          >
            <Col centerAll>
              <Text h4 col='#fff'>Create</Text>
              <Text h4 col='#fff'>Match Day Shoutout</Text>
            </Col>
          </Header>
          : 
          <Header
            title={t('add.newShoutout')}
            hasBack
            isModal
          />
        }
        <KeyboardAvoidingView
          behavior={Platform.OS === 'android' ? 'height' : 'padding'}
          bounces={false}
          style={{ flex: 1 }}
          >
          <Shoutout
            notAddScreen
            navigation={navigation}
            paramsEvent={eventObject}
            paramsSelected={user}
            eventId={eventId}
            selected={!!eventObject}
            cameraMedia={cameraMedia}
            midTxt={true}
          />
        </KeyboardAvoidingView>
      </View>
    </View>
  )
}

// return (
//   <React.Fragment>
//     <Header
//       hasBack
//       isModal
//       title={t('common.addShoutout')}
//       headerRightContent={
//         <Clickable onClick={handleNext} disabled={!comment} pad='0 16px 0 0'>
//           <Text b1 bold>
//             {t('common.proceed')}
//           </Text>
//         </Clickable>
//       }
//     />
//
//     <ScrollView style={{ backgroundColor: '#fff' }}>
// <Row pad='14px 16px 0'>
//   <Avatar user={user}/>
//
//   <Col marg='13px 0 13px 20px'>
//     <Text b1 med>{user.name}</Text>
//
//     <Text b2 med col={theme.GREY_60}>
//       @{user.username}
//     </Text>
//   </Col>
// </Row>
//
//       <SegmentWrapper>
//         <Text h2>{t('common.type')}</Text>
//
//         <SegmentControl
//           marg='16px 0 0'
//           options={[t('common.text')]}
//           selectedIdx={shoutoutType}
//           handleSelect={setShoutoutType}
//         />
//
//         <Input
//           marg='16px 0 0'
//           placeholder={t('common.typeYourMessage')}
//           title={t('common.comment')}
//           value={comment}
//           onChangeText={(e) => setComment(e)}
//           multiline
//         />
//       </SegmentWrapper>
//     </ScrollView>
//   </React.Fragment>
// )


export default ShoutoutModal
