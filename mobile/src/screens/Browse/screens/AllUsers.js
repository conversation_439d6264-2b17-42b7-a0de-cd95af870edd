import React, { useState, useEffect } from 'react'
import { FlatList } from 'react-native'
import { useDispatch, useSelector } from 'react-redux'
import styled from 'styled-components'
import { isNewIphone } from 'core'
import { theme, Api } from 'lib'
import { useRoute } from '@react-navigation/native'
import { Header, Col, Row, Text, Avatar, Clickable, Spinner } from 'components'
import { useTranslation } from 'react-i18next'

import { UserSearch, UserItem } from '../components'

let timer
const AllUsers = ({ navigation }) => {
  const [fetching, setFetching] = useState(true),
    [filtered, setFiltered] = useState(null),
    [listEndReached, setListEndReached] = useState(false),
    [filterObject, setFilterObject] = useState({ }),
    [users, setUsers] = useState([]),
    { removedSuggestions, following, _id } = useSelector(state => state?.user) || { removedSuggestions: null, following: null, _id: null},
    { t } = useTranslation(),
    dispatch = useDispatch(),
    params = useRoute().params,
    { type, team } = params || {}

  useEffect(() => {
    fetchData()
  }, [])

  useEffect(() => {
    clearTimeout(timer)

    timer = setTimeout(async () => {
      fetchData(true)
    }, 500)
  }, [filterObject])

  useEffect(() => {
    if(!type && !team) return

    setFiltered(users?.filter(x => {
      if(!x?._id) return false

      const hasName = x.name
      const isntSelf = team ? true : x._id !== _id

      return hasName && isntSelf
    }))
  }, [users])

  if(!type && !team) return null

  async function fetchData(searchNew) {
    if(fetching && !searchNew) return

    setFetching(true)
    setListEndReached(false)


    const usersSearchEndpoint = _id ? '/users/search' : '/public/users/search';
    const teamSearchEndpoint = (team?._id && _id) ? '/users/getTeamMembers' : '/public/users/getTeamMembers';
    const endpoint = team?._id ? teamSearchEndpoint : usersSearchEndpoint;

    const res = await Api.post(
      endpoint,
      {
        sortBy: 'name',
        ...filterObject,
        type: type,
        skip: !searchNew && users?.length || 0,
        teamId: team?._id
      }
    )

    setFetching(false)
    if(!res) return

    if(!res?.length) return searchNew
      ? setUsers([])
      : setListEndReached(true)

    setUsers(searchNew ? res : [...users, ...res])
  }

  return (
    <Col bg='#fff' ht='100%'>
      <Header
        title={team
          ? team.name
          : type === 'fan'
            ? t('browse.allFans')
            : type === 'team'
              ? t('browse.allTeams')
              : t('browse.allAthletes')
        }
        hasBack
      />

      <FlatList
        data={filtered}
        keyExtractor={(item) => item._id}
        onEndReached={!team && !listEndReached && (() => fetchData())}
        contentContainerStyle={{ paddingBottom: isNewIphone() ? 38 : 8 }}
        onEndReachedThreshold={1}
        ListHeaderComponent={
          <UserSearch
            showTags={type === 'athlete'}
            handleChange={(obj) => setFilterObject(obj)}
          />
        }
        ListEmptyComponent={!listEndReached && fetching
          ? (
            <Row marg='32px 0 0' centerAll>
              <Spinner />
            </Row>
          ) : (
            <Col centerAll marg='20px 0 0'>
              <Text>
                No Results
              </Text>
            </Col>
          )
        }
        renderItem={({ item, index }) => <UserItem item={item} />}
      />
    </Col>
  )
}

export default AllUsers
