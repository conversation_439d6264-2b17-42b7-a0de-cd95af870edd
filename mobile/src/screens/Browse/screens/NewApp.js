import React, { useState } from 'react'
import { Dimensions } from 'react-native'
import { Col, Text, Button } from 'components'
import { getTopPadding } from 'core'
import { theme } from 'lib'
import { IconBg } from 'screens/ForgotPassword/components'
import { useTranslation } from 'react-i18next'

const NewApp = ({ navigation }) => {
  const deviceHt = Dimensions.get('screen').height,
    isSmallScreen = deviceHt < 800,
    { t } = useTranslation()

  return (
    <Col bg={theme.SECONDARY}>
      <IconBg
        source={require('assets/branding/partial-logo-icon-secondary-red.png')}
        isSmallScreen={isSmallScreen}
      >
        <Col pad={`${getTopPadding() + 28}px 0 0`}>
          <Text lineHt='50.28px' stencil col='#fff' size='40px' marg='-6px 0 0 41px'>EVERYBODY</Text>
          <Text lineHt='50.28px' stencil col='#fff' size='40px' marg='-6px 0 0 16px'>PLAYS</Text>
          <Text lineHt='50.28px' stencil col='#fff' size='40px' marg='-6px 0 0 16px'>BETTER</Text>
          <Text lineHt='50.28px' stencil col='#fff' size='40px' marg='-6px 0 0 58px'>TOGETHER.</Text>
        </Col>
      </IconBg>

      <Col center noFlex marg='95px 0 0'>
        <Text h2 col='#fff' align='center'>
          {t('browse.newAppHasArrived')}
        </Text>

        <Text col='#fff' align='center' wid='320px' marg='8px 0 0'>
          {t('browse.newAppDesc')}
        </Text>
      </Col>

      <Col endAll pad='0 16px 58px'>
        <Button
          onClick={() => navigation.navigate('Browse')}
          text={t('browse.seeTheApp')}
          big
        />
      </Col>
    </Col>
  )
}

export default NewApp
