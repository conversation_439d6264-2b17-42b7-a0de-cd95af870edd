import React, { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { Animated, FlatList, SectionList, Dimensions, Platform } from 'react-native'
import styled from 'styled-components'
import { theme } from 'lib'
import { Col, TabControl, Text, Spinner, Row } from 'components'
import { useTranslation } from 'react-i18next'

import { SearchItem } from './'

const Wrapper = styled(Animated.View)`
  position: absolute;
  width: 100%;
  height: ${props => props.contentHt}px;
  ${Platform.OS === 'android' ? 'elevation' : 'z-index'}: ${props => props.zIndex};
  top: ${props => props.topDistance + 100}px;
  background-color: #fff;
`

const EmptyBg = styled.ImageBackground`
  height: 424px;
  width: 100%;
`

const ReverseCol = styled(Col)`
  flex-direction: column-reverse;
`

const TextContainer = styled.View`
  position: absolute;
  top: 52px;
`

const SearchResults = ({
  positionFromTop,
  visible,
  data,
  handleSearch,
  searchText,
  loading
}) => {
  const { t } = useTranslation()
  const [opacity, setOpacity] = useState(new Animated.Value(0)),
    [filter, setFilter] = useState(t('browse.top')),
    [zIndex, setZIndex] = useState(-1),
    deviceHt = Dimensions.get('window').height,
    user = useSelector(state => state?.user),
    contentHt = deviceHt - positionFromTop,
    isAndroid = Platform.OS === 'android',
    options = [t('browse.top'), t('browse.athletes'), t('browse.fans'), t('browse.tags')]

  useEffect(() => {
    if(!visible) return hide()

    show()
  }, [visible])

  useEffect(() => {
    switch (filter) {
      case t('browse.top'):
        handleSearch(searchText)
        break

      case t('browse.athletes'):
        handleSearch(searchText, 'athlete')
        break

      case t('browse.fans'):
        handleSearch(searchText, 'fan')
        break

      case t('browse.teams'):
        handleSearch(searchText, 'team')
        break

      case t('browse.tags'):
        handleSearch(searchText, 'tag')
        break
    }
  }, [searchText, filter])

  function show() {
    setZIndex(2)

    Animated
      .timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: false
      })
      .start()
  }

  function hide() {
    Animated
      .timing(opacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false
      })
      .start()

    setTimeout(() => {
      setZIndex(-1)
    }, 300)
  }

  function handleFilterSelect(val) {
    setFilter(val)
  }

  return (
    <Wrapper
      topDistance={positionFromTop + 10}
      style={{ opacity }}
      visible={visible}
      contentHt={contentHt}
      zIndex={zIndex}
    >
      {!user?.recentSearches?.length && !data && !loading && (
        <Col wid='100%' ht={`${contentHt}px`} endAll center>
          <EmptyBg
            resizeMode='stretch'
            source={require('assets/branding/partial-logo-upper-grey-10.png')}
          />
          <TextContainer>
            <Text
              wid='300px'
              align='center'
              h4
              col={theme.GREY_60}
            >
              {t('browse.trySearchAthlete')}
            </Text>
          </TextContainer>
        </Col>
      )}

      {(data || loading) ? (
        <React.Fragment>
          <TabControl
            options={options}
            selected={filter}
            handleSelect={handleFilterSelect}
          />

          {loading ? (
            <Col pad='24px 0 0' center noFlex>
              <Spinner />
            </Col>
          ) : (
            <FlatList
              renderItem={(props) => <SearchItem {...props} fromSearch />}
              data={data}
              keyExtractor={(item, idx) => (item?._id || idx).toString()}
              keyboardShouldPersistTaps='handled'
              contentContainerStyle={{
                paddingBottom: positionFromTop + 60,
                paddingTop: 24
              }}
              ListEmptyComponent={
                <Row marg='20px'>
                  <Text b2 wid='100%' align='center'>
                    {t('add.noUserFound')}
                  </Text>
                </Row>
              }
            />
          )}
        </React.Fragment>
      ) : !!user?.recentSearches?.length && (
        <SectionList
          sections={[{ title: t('browse.recent'), data: user.recentSearches }]}
          renderItem={SearchItem}
          keyboardShouldPersistTaps='handled'
          renderSectionHeader={({ section: { title } }) => (
            <Col noFlex bg='#fff'>
              <Text h2 marg='14px 16px 8px'>{title}</Text>
            </Col>
          )}
        />
      )}
    </Wrapper>
  )
}

export default SearchResults
