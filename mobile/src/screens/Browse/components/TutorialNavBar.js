import React, { useState } from 'react'
import { isNewIphone } from 'core'
import { Row, Col, Icon, Text } from 'components'
import styled from 'styled-components'
import { theme } from 'lib'
import { useTranslation } from 'react-i18next'

const Circle = styled.View`
  height: 120px;
  width: 120px;
  border-radius: 120px;
  background-color: ${props => props.selected ? theme.SECONDARY : 'transparent'};
  z-index: ${props => props.selected ? '1' : '0'};
  elevation: ${props => props.selected ? '1' : '0'};
  justify-content: center;
  align-items: center;
`

const NavItem = ({ type, selected }) => {

  return (
    <Col centerAll wid='75px' ht='49px'>
      <Circle selected={selected}>
        <Icon
          dimensions={20}
          type={`${type.toLowerCase()}${selected ? 'Filled' : 'Outline'}`}
          col={theme.GREY_10}
          marg='0 0 10px'
        />

        <Text c1 bold col={theme.GREY_10}>{type}</Text>
      </Circle>
    </Col>
  )
}

const TutorialNavBar = ({  }) => {
  const NavBarTypes = ['Home', 'Browse', 'Shoutout', 'Marketplace', 'Profile'],
    { t } = useTranslation()

  const selected = 'Browse'

  return (
    <Row wid='100%' noFlex marg={isNewIphone() ? '0 0 18px' : '0'}>
      <Row marg='10px 2px' between>
        {NavBarTypes.map((x, i) => (
          <NavItem key={i} type={x} selected={x === selected} />
        ))}
      </Row>
    </Row>
  )
}

export default TutorialNavBar
