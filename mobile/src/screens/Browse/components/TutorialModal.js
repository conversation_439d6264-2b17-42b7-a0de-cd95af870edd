import React, { useState, useEffect } from 'react'
import { useDispatch } from 'react-redux'
import { Api } from 'lib'
import { useNavigation } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import { Modal, Row, Col, MultipageDisplay, Button, Text } from 'components'
import { ScrollView } from 'react-native'

import { TutorialNavBar, UpcomingEvents, TrendingTeams, SuggestedAccounts, TrendingPosts } from './'

const TutorialModal = ({ user }) => {
  const [visible, setVisible] = useState(false),
    [tutorialStep, setTutorialStep] = useState(0),
    dispatch = useDispatch(),
    navigation = useNavigation(),
    { t } = useTranslation(),
    pageCount = 4

  useEffect(() => {
    if(user.browseTutorialDone) return

    setTimeout(() => setVisible(true), 600)
  }, [])

  function getPage(step) {
    switch(step) {
      case 0:
        return <UpcomingEvents />
      case 1:
        return <TrendingTeams />
      case 2:
        return <SuggestedAccounts />
      case 3:
        return <TrendingPosts />
      default:
        return <Row />
    }
  }


  async function closeModal() {
    setVisible(false)

    /*
      Delay actions until modal is closed, so the feel is not clunky
    */
    setTimeout(() => {
      navigation.navigate('SubscribeModal')
      Api.post('/users/completeTutorial')

      dispatch({
        type: 'UPDATE_USER',
        payload: { browseTutorialDone: true }
      })
    }, 400)
  }

  function handleClick() {
    tutorialStep === pageCount - 1
      ? closeModal()
      : setTutorialStep(tutorialStep + 1)
  }

  return (
    <Modal
      fullScreen
      visible={visible}
      closeModal={closeModal}
      ignorePadBottom
      // animationInTiming={0}
      headerRightContent={
        <Row centerAll noFlex wid='100%' marg='0 46px 0 0'>
          <MultipageDisplay
            pageCount={pageCount}
            current={tutorialStep}
            wid='210px'
            unchosenWid='30px'
            chosenWid='96px'
          />
        </Row>
      }
    >
      <Col between>
        <ScrollView>
          {getPage(tutorialStep)}
        </ScrollView>

        <Col noFlex>
          <Row noFlex marg='0 16px 60px'>
            <Button
              big
              text={tutorialStep === pageCount - 1 ? t('browse.goBrowse') : t('common.next') }
              onClick={handleClick}
            />
          </Row>

          <TutorialNavBar />
        </Col>
      </Col>
    </Modal>
  )
}

export default TutorialModal
