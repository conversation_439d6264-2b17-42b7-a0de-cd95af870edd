import React, { useState } from 'react'
import { Text, Col, Row } from 'components'
import { theme } from 'lib'
import styled from 'styled-components'
import { useTranslation } from 'react-i18next'

const Image = styled.Image`
`

const TrendingPosts = ({  }) => {
  const { t } = useTranslation()
  
  return (
    <Col noFlex center marg='64px 16px 16px'>
      <Text h2>{t('browse.trendingPosts')}</Text>

      <Text b1 col={theme.GREY_60} align='center' marg='8px 0 0'>
        {t('browse.chooseSportAndFavoriteTeam')}
      </Text>

      <Row startAll wid='100%' marg='36px 0 0'>
        <Image source={require('assets/images/Sample-Posts.png')} />
      </Row>
    </Col>
  )
}

export default TrendingPosts
