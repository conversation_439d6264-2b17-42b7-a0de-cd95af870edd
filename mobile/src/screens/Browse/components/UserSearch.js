import React, { useState, useEffect, memo } from 'react'
import { Input, Row, Col, Clickable, Text, Icon, Modal } from 'components'
import { theme } from 'lib'
import { useTranslation } from 'react-i18next'
import { ScrollView } from 'react-native'
import { useDispatch, useSelector } from 'react-redux'

import { UserSearchModal } from './'

const SportItem = ({ handleSelect, sport, isSelected }) => {
  return (
    <Clickable onClick={() => handleSelect(sport)} marg='0 0 0 8px'>
      <Row
        pad='9px 24px'
        centerAll
        hasRadius='48px'
        bg={isSelected ? theme.SECONDARY_20 : theme.GREY_10}
        hasBorder={`1px solid ${isSelected ? theme.SECONDARY : 'transparent'}` }
        noFlex
      >
        <Text med>{sport}</Text>
      </Row>
    </Clickable>
  )
}

const UserSearch = ({
  handleChange = () => null,
  showTags
}) => {
  const [searchText, setSearchText] = useState(''),
    [selectedSports, setSelectedSports] = useState(-1),
    [showTeams, setShowTeams] = useState(false),
    [sortVisible, setSortVisible] = useState(false),
    [sortOption, setSortOption] = useState('browse.name'),
    [filterOption, setFilterOption] = useState('browse.showAll'),
    { t } = useTranslation(),
    favoriteSports = useSelector(state => state.user?.favoriteSports)

  useEffect(() => {
    handleChange({
      text: searchText,
      sports: selectedSports?.length ? selectedSports : null,
      teams: showTeams ? 'team' : null,
      sortBy: sortOption === 'browse.name' ? 'name' : '-createdAt',
      option: filterOption
    })
  }, [searchText, selectedSports, showTeams, sortOption, filterOption])

  function handleSearch(text) {
    setSearchText(text)
  }

  function handleUpdateSort(opt) {
    setSortOption(opt)
  }

  function handleUpdateFilter(opt) {
    setFilterOption(opt)
  }

  function handleSportSelect(sport) {
    if(!sport) return setSelectedSports([])

    if(selectedSports.includes(sport)) {
      return setSelectedSports(selectedSports.filter(x => x !== sport))
    }
    return setSelectedSports([...selectedSports, sport])
  }

  function handleTeams() {
    setShowTeams(!showTeams)
  }

  return (
    <Col noFlex>
      <Row noFlex centerAll pad='0 16px'>
        <Col>
          <Input
            search
            value={searchText}
            onChangeText={handleSearch}
            placeholder={t('common.search')}
          />
        </Col>

        <Clickable marg='0 0 0 12px' onClick={() => setSortVisible(true)}>
          <Row pad='7px 16px' centerAll hasRadius='48px' hasBorder='2px solid #000' noFlex>
            <Text b2 bold>{t('common.sort')}</Text>
          </Row>
        </Clickable>
      </Row>

      <UserSearchModal
        sortVisible={sortVisible}
        sortOption={sortOption}
        filterOption={filterOption}
        handleClose={() => setSortVisible(false)}
        handleUpdateSort={handleUpdateSort}
        handleUpdateFilter={handleUpdateFilter}
      />

      {showTags && (
        <ScrollView 
          horizontal
          keyboardShouldPersistTaps='never'
        >
          <Row marg='24px 0 0 16px' center noFlex>
            {selectedSports !== -1 && (
              <Clickable onClick={() => setSelectedSports(-1)}>
                <Row
                  noFlex
                  wid='40px'
                  ht='40px'
                  centerAll
                  pad='8px'
                  bg={theme.SECONDARY_20}
                  hasBorder={`1px solid ${theme.SECONDARY}` }
                  hasRadius='48px'
                  marg='0 12px 0 0'
                  >
                  <Icon type='close' dimensions={12}/>
                </Row>
              </Clickable>
            )}

            <Clickable onClick={() => handleSportSelect()}>
              <Row
                pad='9px 24px'
                centerAll
                hasRadius='48px'
                bg={selectedSports === -1 ? theme.GREY_10 : theme.SECONDARY_20}
                hasBorder={`1px solid ${selectedSports === -1 ? 'transparent' : theme.SECONDARY}` }
                noFlex
                >
                <Text med>{t('common.sport')}</Text>
              </Row>
            </Clickable>

            {/*
              <Clickable marg='0 0 0 8px' onClick={handleTeams}>
                <Row
                  pad='9px 24px'
                  centerAll
                  hasRadius='48px'
                  bg={showTeams ? theme.SECONDARY_20 : theme.GREY_10}
                  hasBorder={`1px solid ${showTeams ? theme.SECONDARY : 'transparent'}` }
                  noFlex
                >
                  <Text med>{t('common.teams')}</Text>
                </Row>
              </Clickable>
            */}

            {selectedSports !== -1 && favoriteSports?.map((x, i) => (
              <SportItem
                key={i}
                sport={x.sport}
                isSelected={selectedSports.includes(x.sport)}
                handleSelect={handleSportSelect}
              />
            ))}
          </Row>
        </ScrollView>
      )}
    </Col>
  )
}

export default memo(UserSearch)
