import React, { useState } from 'react'
import { Text, Col } from 'components'
import { theme } from 'lib'
import styled from 'styled-components'
import { useTranslation } from 'react-i18next'

const Background = styled.View`
  width: 100%;
  height: 96px;
  background-color: ${theme.SECONDARY};
  position: relative;
  top: -96px;
  z-index: -1;
`

const ImageBg = styled.ImageBackground`
  height: 220px;
  width: 220px;
  background-color: ${theme.GREY_10};
  justify-content: space-between;
  align-items: center;
  padding: 16px;
`

const Follow = styled.View`
  width: 188px;
  height: 32px;
  border-radius: 48px;
  background-color: rgba(210, 216, 223, 0.75);
  align-items: center;
  justify-content: center;
`

const TrendingTeams = ({  }) => {
  const { t } = useTranslation()

  return (
    <Col noFlex center marg='64px 0 0'>
      <Text h2>{t('browse.trendingTeams')}</Text>

      <Text b1 col={theme.GREY_60} align='center' marg='8px 30px 36px'>
        {t('browse.seeTeamsYourFellow')}
      </Text>

      <ImageBg source={require('assets/images/Atalanta.png')}>
        <Col />

        <Follow>
          <Text b2 bold>{t('common.subscribe')}</Text>
        </Follow>
      </ImageBg>

      <Col noFlex marg='8px 0 23px' wid='220px'>
        <Text med col='#fff'>{t('browse.atalanta')}</Text>

        <Text b2 col='#fff'>{t('browse.serieaFootballClub')}</Text>
      </Col>

      <Background />
    </Col>
  )
}

export default TrendingTeams
