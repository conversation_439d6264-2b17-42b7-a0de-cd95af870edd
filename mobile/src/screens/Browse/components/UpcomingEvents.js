import React, { useState } from 'react'
import { Text, Row, Col, Icon } from 'components'
import { theme } from 'lib'
import { useTranslation } from 'react-i18next'

const UpcomingEvents = ({  }) => {
  const { t } = useTranslation()
  
  return (
    <Col noFlex center marg='64px 35px 0'>
      <Text h2>{t('browse.upcomingEvents')}</Text>

      <Text b1 col={theme.GREY_60} align='center' marg='8px 0 0'>
        {t('browse.eventsComingUpYourFavorite')}
      </Text>

      <Col
        marg='36px 0 0'
        bg={theme.SECONDARY}
        pad='16px'
        ht='159px'
        wid='320px'
        noFlex
      >
        <Row between wid='100%' noFlex>
          <Text b2 col='#fff'>
            {t('browse.superBantamweight')}
          </Text>

          <Row bg='#fff' pad='2px 8px' noFlex ht='20px'>
            <Text c1 bold>LIVE</Text>
          </Row>
        </Row>

        <Col marg='8px 0 0' noFlex>
          <Text b1 bold col='#fff'>{t('browse.tiffanyVanSoestAlinePereira')}</Text>

          <Text b2 marg='4px 0 0' col='#fff'>{t('browse.chicagoVenue')} </Text>
        </Col>

        <Row between marg='16px 0 0' noFlex>
          <Row noFlex centerAll bg='#000' wid='140px' pad='0 9px' hasRadius='48px'>
            <Icon type='shoutoutOutline' col='#fff' dimensions={22} />

            <Text marg='0 0 0 5px' b2 bold col='#fff'>{t('common.shoutOut')}</Text>
          </Row>

          <Row noFlex centerAll bg='#000' wid='140px' pad='9px 0' hasRadius='48px'>
            <Icon type='video' col='#fff' dimensions={22} />

            <Text marg='0 0 0 6px' b2 bold col='#fff'>Watch</Text>
          </Row>
        </Row>
      </Col>
    </Col>
  )
}

export default UpcomingEvents
