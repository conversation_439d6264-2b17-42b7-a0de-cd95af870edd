import React, { useState, memo} from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigation } from '@react-navigation/native'
import { Api, theme } from 'lib'
import { Col, Row, Clickable, Avatar, Text } from 'components'
import { useTranslation } from 'react-i18next'


const Item = ({ item, onClick, pad }) => {
  const [loading, setLoading] = useState(false),
    { following } = useSelector(state => state?.user) || { following: [] },
    isFollowing = following?.find(x => x?._id || x === item._id),
    dispatch = useDispatch(),
    navigation = useNavigation(),
    { t } = useTranslation()

  async function follow() {
    if(loading) return
    setLoading(true)

    const res = await Api.post(
      isFollowing ? '/users/unfollow' : '/users/follow',
      { userId: item._id }
    )
    if(!res) return

    dispatch({
      type: isFollowing ? 'UNFOLLOW' : 'FOLLOW',
      payload: item
    })

    setLoading(false)
  }

  return (
    <Clickable
      onClick={() => onClick ? onClick() :  navigation.push('StackProfile', item)}
      marg='12px 0 0'
      pad={pad || '0 16px'}
    >
      <Row between center noFlex>
        <Row noFlex>
          <Avatar user={item}/>

          <Col marg='0 0 0 12px' noFlex >
            <Row center>
              <Col noFlex>
                <Text b1 med>{item.name}</Text>

                {!!item.bio && (
                  <Text col={theme.GREY_60} numberOfLines={1}>
                    {item.bio}
                  </Text>
                )}
              </Col>
            </Row>
          </Col>
        </Row>


        {/*
          <Clickable onClick={follow} action='follow'>
            {loading
              ? (
                <Col noFlex wid='50px'>
                  <Spinner />
                </Col>
              ) : (
                <Text bold>
                  {t(
                    item.type === 'fan'
                      ? isFollowing ? 'common.following' : 'common.follow'
                      : isFollowing ? 'common.subscribed' : 'common.subscribe'
                  )}
                </Text>
              )
            }
          </Clickable>
        */}
      </Row>
    </Clickable>
  )
}

export default memo(Item)