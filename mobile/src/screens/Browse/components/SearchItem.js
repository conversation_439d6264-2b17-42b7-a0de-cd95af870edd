import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigation } from '@react-navigation/native'
import { theme, Api } from 'lib'
import { Col, Row, Text, Avatar, Clickable, Icon } from 'components'

const SearchItem = ({ item, fromSearch }) => {
  const navigation = useNavigation(),
    dispatch = useDispatch(),
    user = useSelector(state => state.user)

  item = item.target || item

  async function goToTagPostFeed(tag) {
    const res = await Api.post('/posts/get', { tag }, 'navigator')
    if(!res) return

    navigation.navigate('PostsFeed', { data: res, title: tag })
  }

  function handleSelect() {
    const type = item?.tag || item.type === 'tag'
      ? 'tag'
      : 'user'

    if(fromSearch) {
      const data = {
        type,
        target: item,
        updatedAt: new Date()
      }

      dispatch({
        type: 'ADD_SEARCH_ITEM',
        payload: data
      })

      Api.put('/users/addRecentSearch', { type, targetId: item._id, text: item?.tag })
    }

    switch (type) {
      case 'user':
        return navigation.navigate('StackProfile', item)
      case 'tag':
        return goToTagPostFeed(item.tag || item.text)
    }
  }

  function removeSearch() {
    dispatch({
      type: 'REMOVE_SEARCH_ITEM',
      payload: item
    })

    Api.post('/users/removeSearch', { targetId: item._id })
  }

  if(item?.tag || item?.text) return (
    <Clickable onClick={handleSelect}>
      <Row noFlex pad='0 16px 8px'>
        <Row>
          <Row centerAll wid='72px' ht='72px' bg={theme.GREY_10} hasRadius='72px' noFlex>
            <Row centerAll wid='68px' ht='68px' bg='#fff' hasRadius='68px' noFlex>
              <Row centerAll wid='64px' ht='64px' bg={theme.GREY_10} hasRadius='64px' noFlex>
                <Text h4>#</Text>
              </Row>
            </Row>
          </Row>

          <Col noFlex marg='0 0 0 16px' center>
            <Col noFlex>
              <Text bold marg='0 0 2px 0'>{item.tag || item.text}</Text>

              <Text b2 col={theme.GREY_60} wid='240px'>
                {item.count} post{item.count !== 1 && 's'}
              </Text>
            </Col>
          </Col>
        </Row>

        {!fromSearch && (
          <Col noFlex center>
            <Clickable onClick={removeSearch}>
              <Icon type='close' col={theme.GREY_60} dimensions={14}/>
            </Clickable>
          </Col>
        )}
      </Row>
    </Clickable>
  )

  if(item?.username || item?.target?.username) return (
    <Clickable onClick={handleSelect}>
      <Row noFlex pad='0 16px 8px'>
        <Row>
          <Avatar
            user={item}
          />

          <Col noFlex marg='0 0 0 16px' center>
            <Col noFlex>
              <Text bold marg={item.bio && '0 0 2px 0'}>{item.name}</Text>

              {!!item.bio &&
                <Text b2 col={theme.GREY_60} numberOfLines={1} wid='240px'>
                  {item.bio}
                </Text>
              }
            </Col>
          </Col>
        </Row>

        {!fromSearch && (
          <Col noFlex center>
            <Clickable onClick={removeSearch}>
              <Icon type='close' col={theme.GREY_60} dimensions={14}/>
            </Clickable>
          </Col>
        )}
      </Row>
    </Clickable>
  )

  return null
}

export default SearchItem
