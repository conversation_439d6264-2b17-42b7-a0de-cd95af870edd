import React, { useState } from 'react'
import { Icon, Clickable, Col, Row, Text } from 'components'
import { theme } from 'lib'
import styled from 'styled-components'
import { useTranslation } from 'react-i18next'

const SettingObject = ({ check, text, onClick }) => {
  const { t } = useTranslation()

  return (
    <Col marg='4px 0 0' noFlex>
      <Clickable marg='4px 0 0' onClick={() => onClick(text)}>
        <Row noFlex ht='48px' center wid='100%'>
          <Row center>
            <Text med>{t(text)}</Text>
          </Row>

          {check &&
            <Row pad='8px 0 0' noFlex centerAll>
              <Icon type='check' dimensions={20} col={theme.SECONDARY}/>
            </Row>
          }
        </Row>
      </Clickable>
    </Col>
  )
}

export default SettingObject
