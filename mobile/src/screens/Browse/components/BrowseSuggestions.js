import React, { useState } from 'react'
import { Suggestions, SuggestionsV2, Col } from 'components'
import { theme } from 'lib'
import { useTranslation } from 'react-i18next'

const BrowseSuggestions = ({
  data,
  suggestionRefs,
  athletesOnApp,
  fansOnApp,
  navigation,
  isLoggedIn
}) => {
  const { t } = useTranslation()

  const getCount = (num) =>{
    if (num < 0){
      return 0;
    }
    return num;
  }

  return (
    <Col>
      {!!data?.athlete?.length && (
        <SuggestionsV2
          listRef={suggestionRefs[0]}
          title={t('browse.athletesOnPlatform')}
          data={data.athlete}
          subtitleText={(user) => (
            user.sports?.length && user.sports[0]?.sport
              ? t(`sports.${user.sports[0].sport}`)
              : ''
          )}
          seeAll={() => navigation.navigate('AllUsers', { type: 'athlete' })}
          cantRemove
          isBrowse
          isLoggedIn={isLoggedIn}
        />
      )}

      {!!data?.team?.length && (
        <SuggestionsV2
          showFollowing
          listRef={suggestionRefs[2]}
          title={t('browse.teamsOnPlatform')}
          seeAll={() => navigation.navigate('AllUsers', { type: 'team' })}
          data={data?.team}
          subtitleText={(user) => {
            const league = user.league ? user.league : '',
              sport = user.sport ? t(`sports.${user.sport}`) : ''

            return (sport || league)
              ? `${league}${sport ? `${league ? ' ' + sport + ' club' : ''}` : ''}`
              : ''
          }}
          isBrowse
          isLoggedIn={isLoggedIn}
        />
      )}

      {!!data?.fan?.length && (
        <Suggestions
          listRef={suggestionRefs[1]}
          title={t('browse.fansOnPlatform')}
          data={data.fan}
          subtitleText={(user) => (
            `${getCount(user.followersCount)} ${(getCount(user.followersCount) !== 1) ? t('browse.followers') : t('browse.follower')}`
          )}
          seeAll={() => navigation.navigate('AllUsers', { type: 'fan' })}
          col={theme.GREY_10}
          textCol='#000'
          cantRemove
          isBrowse
        />
      )}
    </Col>
  )
}

export default BrowseSuggestions
