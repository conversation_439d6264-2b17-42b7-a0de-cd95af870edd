import React, { useState } from 'react'
import styled from 'styled-components'
import { Col, Modal, Text } from 'components'
import { theme } from 'lib'
import { useTranslation } from 'react-i18next'

import { SettingObject } from './'

const Seperator = styled.View`
  width: 100%;
  height: 1px;
  background-color: ${theme.GREY_20};
  margin: 4px 0;
`

const UserSearchModal = ({
  sortVisible,
  handleClose,
  handleUpdateSort,
  handleUpdateFilter,
  sortOption,
  filterOption
}) => {
  const { t } = useTranslation()

  return (
    <Modal visible={sortVisible} closeModal={handleClose}>
      <Col noFlex pad='0 16px 15px'>
        <Text b2 col={theme.GREY_60} marg='8px 0 0'>
          {t('browse.sortBy')}
        </Text>

        <SettingObject
          text={'browse.name'}
          onClick={(opt) => handleUpdateSort(opt)}
          check={sortOption === 'browse.name'}
        />

        {/*
          <SettingObject
            text={'browse.popularity'}
            onClick={(opt) => handleUpdateSort(opt)}
            check={sortOption === 'browse.popularity'}
          />
        */}

        <SettingObject
          text={'browse.recentlyRegistered'}
          onClick={(opt) => handleUpdateSort(opt)}
          check={sortOption === 'browse.recentlyRegistered'}
        />

        {/*
          <Seperator />

          <Text b2 col={theme.GREY_60} marg='8px 0 0'>
            {t('browse.filterByConnection')}
          </Text>

          <SettingObject
            text={'browse.showAll'}
            onClick={(opt) => handleUpdateFilter(opt)}
            check={filterOption === 'browse.showAll'}
          />

          <SettingObject
            text={'browse.showOnlyConnect'}
            onClick={(opt) => handleUpdateFilter(opt)}
            check={filterOption === 'browse.showOnlyConnect'}
          />
        */}
      </Col>
    </Modal>
  )
}

export default UserSearchModal
