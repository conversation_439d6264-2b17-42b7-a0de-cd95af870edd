import React, { useState, useEffect, useRef } from 'react'
import styled from 'styled-components'
import { useDispatch, useSelector } from 'react-redux'
import { RefreshControl, FlatList, Dimensions } from 'react-native'
import { useRoute } from '@react-navigation/native'
import { getCalculatedText } from 'core'
import { Api } from 'lib'
import { useTranslation } from 'react-i18next'

import {
  Col,
  TabScreenHeader,
  PublicTabScreenHeader,
  Spinner,
  Input,
  PostsGrid,
  Clickable,
  Text,
  Button,
} from 'components'

import {
  Filters,
  Search,
  BrowseSuggestions,
  TutorialModal,
  SearchResults,
} from './components'

const Image = styled.Image`
  height: ${props => props.size}px;
  width: ${props => props.size}px;
  margin: 0 0 4px 0;
`

let timer
const Browse = ({ navigation }) => {
  const scrollRef = useRef(null),
    [text, setText] = useState(''),
    [searchData, setSearchData] = useState(null),
    [searchVisible, setSearchVisible] = useState(false),
    [height, setHeight] = useState(0),
    [loading, setLoading] = useState(false),
    [refreshing, setRefreshing] = useState(false),
    [latestPostDate, setLatestPostDate] = useState(null),
    user = useSelector(state => state.user),
    browse = useSelector(state => state.browse),
    { users, posts, type, sports } = browse || { users: [], posts: [], type: '', sports: []},
    imageWidth = Dimensions.get('window').width / 2 - 2,
    dispatch = useDispatch(),
    route = useRoute(),
    inputRef = useRef(),
    suggestion1Ref = useRef(),
    suggestion2Ref = useRef(),
    suggestion3Ref = useRef(),
    suggestion4Ref = useRef(),
    suggestionRefs = [suggestion1Ref, suggestion2Ref, suggestion3Ref, suggestion4Ref],
    { t } = useTranslation(),
    isLoggedIn = !!user?._id;

    
  useEffect(() => {
    if(!browse) {
      dispatch({ type: 'SET_BROWSE' })

      return
    }

    getData()
  }, [type, sports])

  useEffect(() => {
    if(!route.params?.scrollToTop) return

    scrollToTop()

    navigation.setParams({
      ...route.params,
      scrollToTop: false
    })
  }, [route.params?.scrollToTop])

  function scrollToTop() {
    scrollRef.current?.scrollToOffset({ animated: true, offset: 0 })
  }

  function handleSearchVisible() {
    setSearchVisible(!searchVisible)

    if(searchVisible) {
      inputRef.current.blur()

      setTimeout(() => {
        setSearchData(null)
        setText('')
      }, 300)
    }
  }
  function handleFocus() {
    scrollToTop()
    setRefreshing(false)
    !searchVisible && handleSearchVisible()
  }

  async function onRefresh() {
    setRefreshing(true)
    getData(true)
  }

  async function getData(initial) {
    const endpoint = isLoggedIn ? '/browse/getData' : '/public/browse/getData';

    const res = await Api.post(
      endpoint,
      {
        type,
        sports
      }
    )

    if(!res) return

    const mappedPosts = await Promise.all(
      res.posts.map(async x => {
        return {
          ...x,
          calculatedText: await getCalculatedText(x.caption)
        }
      })
    )

    dispatch({
      type: initial ? 'SET_BROWSE' : 'UPDATE_BROWSE',
      payload: { ...res, posts: mappedPosts}
    })

    dispatch({
      type: 'ADD_POSTS',
      payload: mappedPosts
    })

    setLatestPostDate(res.latestDate)

    suggestionRefs.forEach(x => {
      x?.current?.scrollToOffset({ offset: 0, animated: true })
    })

    setTimeout(() => {
      setRefreshing(false)
    }, 500)
  }

  async function fetchMoreData() {
    const endpoint = isLoggedIn ? '/browse/getData' : '/public/browse/getData';

    const res = await Api.post(
      endpoint,
      {
        type: type?.toLowerCase().slice(0, -1),
        fetchStartDate: latestPostDate,
        sports
      }
    )

    if(!res) return

    dispatch({
      type: 'ADD_POSTS',
      payload: res.posts
    })

    dispatch({
      type: 'ADD_POSTS_BROWSE',
      payload: res.posts
    })

    setLatestPostDate(res.latestDate)
  }

  function handleChange(val, type) {
    setText(val)
  }

  async function handleSearch(val, type) {
    clearTimeout(timer)
    setLoading(true)

    timer = setTimeout(async () => {
      if(!val.trim().length) {
        setLoading(false)
        return setSearchData(null)
      }

      const endpoint = isLoggedIn ? '/browse/search' : '/public/browse/search';
      const res = await Api.post(endpoint, { text: val, type })
      setLoading(false)
      if(!res) return
      const allowedUsers = res.filter(users => !users.banned)
      setSearchData(allowedUsers)
    }, 300)
  }

  function setSports(payload) {
    dispatch({
      type: 'SET_SPORTS_BROWSE',
      payload
    })
  }

  function setType(payload) {
    dispatch({
      type: 'SET_TYPE_BROWSE',
      payload: type
    })
  }

  function handlePostSelect(index, itemId) {
    const startingSliceRange = [
      index - 5 < 0 ? 0 : index - 5,
      index + 5
    ]

    navigation.push(
      'PostsFeed',
      {
        startingSliceRange,
        title: t('browse.trendingPosts'),
        index: index,
        initialItemId: itemId,
        prevScreen: 'Browse'
      }
    )
  }

  return (
    <Col bg='#fff'>
      { isLoggedIn ? <TabScreenHeader /> : <PublicTabScreenHeader />}
      { isLoggedIn && <TutorialModal user={user} /> }

      <FlatList
        // Performance settings
        removeClippedSubviews={true}
        initialNumToRender={6}
        maxToRenderPerBatch={4}
        updateCellsBatchingPeriod={100}
        windowSize={3}

        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
          />
        }
        ref={scrollRef}
        scrollEnabled={!searchVisible}
        data={posts?.filter(x => typeof x === 'object')}
        keyExtractor={(item, i) => item._id + i} // IMPORTANT: This is temporary
        numColumns={2}
        onEndReached={fetchMoreData}
        onEndReachedThreshold={0.6}
        ListEmptyComponent={!!posts?.length && (
          <Col pad='0 16px' ht='500px' centerAll>
            <Text center b2 med>Nothing to see here yet.</Text>
          </Col>
        )}
        ListHeaderComponent={(
          <Col noFlex>
            <Col onLayout={e => setHeight(e.nativeEvent.layout.height)}>
              <Input
                disabled={!refreshing}
                hasBack={searchVisible && handleSearchVisible}
                onFocus={handleFocus}
                pad='12px 16px 0'
                search
                value={text}
                onChangeText={(e) => handleChange(e)}
                placeholder={t('common.search')}
                inputRef={inputRef}
              />
            </Col>

            <Filters
              refreshing={refreshing}
              setType={setType}
            />

            {!posts?.length ? (
                <Col centerAll marg='24px 0 0'>
                  <Spinner />
                </Col>
              ) : (
                <React.Fragment>
                  <BrowseSuggestions
                    data={users}
                    suggestionRefs={suggestionRefs}
                    navigation={navigation}
                    isLoggedIn={isLoggedIn}
                  />

                  <Text marg='24px 0 12px 16px' h2>
                    {t('browse.trendingPosts')}
                  </Text>
                </React.Fragment>
              )
            }
               
          </Col>
        )}
        renderItem={({ item, index }) => (
          <Clickable
            marg={index % 2 === 0 && '0 4px 0 0'}
            onClick={() => handlePostSelect(index, item._id)}
          >
            <Image
              size={imageWidth}
              source={{
                uri: item?.metaData
                  ? item.metaData.image || item.metaData.icon || ''
                    : item?.youtubeId
                      ? `https://img.youtube.com/vi/${item.youtubeId}/0.jpg`
                      : item?.media?.length
                        ? item.media[0]?.thumbnail || item.oldMedia[0]
                        : null
              }}
            />
          </Clickable>
        )}
      />

      {/* add additional height for public browse screen */}
      <SearchResults
        positionFromTop={isLoggedIn ? height : (height + 24)}
        visible={searchVisible}
        data={searchData}
        handleSearch={handleSearch}
        searchText={text}
        loading={loading}
      /> 

    </Col>
  )
}

export default Browse

