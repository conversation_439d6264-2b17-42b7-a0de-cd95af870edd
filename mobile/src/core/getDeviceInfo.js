import messaging from '@react-native-firebase/messaging'
import { getModel, getBrand, getDeviceId } from 'react-native-device-info'

/*
  Collets relative device data
  eg. os, token, model
*/

async function getDeviceInfo() {
  const [token, brand, model, deviceId] = await Promise.all([
    await messaging()
      .getToken()
      .then(tk => tk)
      .catch(err => false),
    getBrand(),
    getModel(),
    getDeviceId()
  ])


  return {
    token,
    model,
    brand,
    deviceId
  }
}

export default getDeviceInfo

