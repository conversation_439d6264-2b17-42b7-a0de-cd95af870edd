import { navigator } from 'core';
import { store } from 'store';
import { Api } from 'lib'

async function redirectLink(url) {
    const { navigate, navigationRef } = navigator;

    if (url.includes('/reset-password') || url.includes('/forgot-password')) {
        const urlParts = url.split('?');
        let code = null;
        let lang = null;
        
        if (urlParts.length > 1) {
            const queryString = urlParts[1];
            const params = queryString.split('&');

            params.forEach(param => {
                const [key, value] = param.split('=');
                if (key === 'code') {
                    code = decodeURIComponent(value);
                } else if (key === 'lang') {
                    lang = decodeURIComponent(value);
                }
            });
        }
        
        waitForNavigation(() => {
            navigate('CreateNewPassword', { code, lang });
        });
        return;
    }

    if (url.includes('/verify-email')) {
        const urlParts = url.split('/');
        const token = urlParts[urlParts.length - 1];
        
        waitForNavigation(() => {
            navigate('VerifyEmail', { token });
        });
        return;
    }

    if (url.includes('/signup')) {
        waitForNavigation(() => {
            navigate('SignUp');
        });
        return;
    }

    if (url.includes('/shared/post')) {
        const urlParts = url.split('/');
        const postId = urlParts[urlParts.length - 1];
        
        waitForNavigation(() => {
            navigate('PostsFeed', { redirectedId: postId, data: [{ _id: postId }] });
        });
        return;
    }

    const isMediaShare = (url.includes('post') || url.includes('shoutout')) && url.includes('profile');
    let userDetails = null;
    const { username, ctaAction, mediaShareId } = extractUsernameAndCta(url);

    if (isMediaShare) {
        userDetails = await getUserByUsername(username);
    } else if (url.includes('profile')) {
        userDetails = await getId(username);

        store.dispatch({
            type: 'UPDATE_CTA_USER_DETAIL',
            payload: userDetails
        });
    } 

    let params = {
        username,
        ctaAction,
        _id: userDetails?._id,
        fromLink: true,
        mediaShareId,
    };
    console.log("🚀 ~ redirectLink ~ params:", params);

    if (url.includes('profile')) {
        waitForNavigation(() => {
            navigate('StackProfile', params);
        });
    } else if (url.includes('post') && !url.includes('shared')) {
        waitForNavigation(() => {
            navigate('PostsFeed', { redirectedId: mediaShareId, data: [{ _id: mediaShareId }] });
        });
    }

    // Wait for the Navigation to be ready before navigating to Profile
    function waitForNavigation(callback, attempts = 10) {
        if (navigationRef.isReady()) {
            callback();
        } else if (attempts > 0) {
            setTimeout(() => waitForNavigation(callback, attempts - 1), 300);
        }

        return;
    }
}

const getUserByUsername = async (username) => {
    try {
        const res = await Api.get(`/users/getByUsername/${username}`);
        return res

    } catch (error) {
        console.error('Error fetching user:', error);
    }
};

export const getId = async (username) => {
    const user = store.getState().user;

    if (!username) return null;

    if (user?.username === username) {
        // do not pass id if selected `link is the current user
        return null;
    } else {
        return await getUserByUsername(username);
    }
}

export const extractUsernameAndCta = (url) => {
    if (!url) return { username: null, ctaAction: null, mediaShareId: null }; 

    const isMediaShare = (url.includes('post') || url.includes('shoutout'));
    const isProfile = url.includes('profile');
    const urlParts = url.split("/");

    if (isMediaShare) {
        const username = decodeURIComponent(urlParts.at(-3)) || null;
        const ctaAction = decodeURIComponent(urlParts.at(-2)) || null;
        const mediaShareId = decodeURIComponent(urlParts.at(-1)) || null;
        return { username, ctaAction, mediaShareId };
    }

    if (isProfile) {
        const username = decodeURIComponent(urlParts.at(-1)) || null;
        return { username, ctaAction: null, mediaShareId: null };
    }

    const username = decodeURIComponent(urlParts.at(-2)) || null;
    const ctaAction = decodeURIComponent(urlParts.at(-1)) || null;
    return { username, ctaAction };
};


export default redirectLink;
