import { Api } from 'lib'
import { navigator } from 'core'

function redirectPushNotif(initialData, _navigation, user) {
  if(!initialData) return

  const { data, target, notifId, targetId } = initialData?.data?.json
    ? JSON.parse(initialData.data.json)
    : { data: null }

  if (!data && !target) return

  notifId && Api.post('/notifications/markOneRead', { notifId })
  
  switch (target) {
    case 'chat':
      navigator.navigate('Chat', { chat: data.chat })
      break
    case 'post':
      navigator.navigate('Comments', { postId: targetId, autoFocus: false })
      break
    case 'comment':
      navigator.navigate('Comments', { postId: data.entityId, scrollToId: data.commentId })
      break
    case 'shoutout':
      navigator.navigate('Shoutouts', { shoutoutId: targetId, title: user?.name || '', data: [{ _id: targetId }] })
      break
    case 'chat':
      navigator.navigate('Chat', { chat: data.chat })
      break
    case 'follow':
    case 'subscribe':
      navigator.navigate('StackProfile', data)
      break
    case 'athleteShoutout':
      switch (data?.status) {
        
        case 'pending':
          navigator.navigate('RequestList', { isYourRequest: false, isListDisplayed: true, fromRecording: false, fromNotif: true })
          break
        case 'completed':
          navigator.navigate('RequestVideoGallery')
          break
        default:
          navigator.navigate('RequestList', { isYourRequest: true, isListDisplayed: true, fromRecording: false, fromNotif: true })
      }
    default:

  }
}

export default redirectPushNotif
