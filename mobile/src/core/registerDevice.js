import { store } from 'store'
import { getDeviceInfo, handlePermission } from 'core'
import dinfo from 'react-native-device-info'
import { Api } from 'lib'

async function registerDevice() {
  await handlePermission({ checkedPermission: 'notification' })

  const { devices } = store.getState().user
  const deviceInfo = await getDeviceInfo()
  
  Api.post('/sessions/register', deviceInfo)
}

export default registerDevice