import { store } from 'store'
import { ws } from 'lib'

import {
  RTCPeerConnection,
  RTCIceCandidate,
  RTCSessionDescription,
  RTCView,
  MediaStream,
  MediaStreamTrack,
  mediaDevices,
  registerGlobals,
} from 'react-native-webrtc'


const config = {
  transportPolicy: 'relay',
  iceServers: [
    {
      urls: [ 'stun:fr-turn1.xirsys.com' ]
     },
     {
       username: 'w0OlszfvWKgSRmZ90SZvsj6AaSoGP6p4rOB16vqq6BnTE5Ytnx7SV2RvL9w5NjwFAAAAAGQPhtxwbGF5YXo0cGxheWF6',
       credential: '473b7006-c1dd-11ed-810c-0242ac120004',
       urls: [
          'turn:fr-turn1.xirsys.com:80?transport=udp',
          'turn:fr-turn1.xirsys.com:3478?transport=udp',
          'turn:fr-turn1.xirsys.com:80?transport=tcp',
          'turn:fr-turn1.xirsys.com:3478?transport=tcp',
          'turns:fr-turn1.xirsys.com:443?transport=tcp',
          'turns:fr-turn1.xirsys.com:5349?transport=tcp'
       ]
     }
  ]
}

let myConn = null,
  local = null,
  clients = {}

async function localStream() {
  myConn = new RTCPeerConnection(config)

  const stream = await mediaDevices
    .getUserMedia({
      audio: true,
      video: {
        mandatory: {
          minWidth: 360,
          minHeight: 640,
          minFrameRate: 20
        },
        facingMode: 'user'
      }
    })
      .then(stream => {
        myConn.addStream(stream)

        return stream
      })
      .catch(error => {
        // Log error
        console.log('Local Error: ', error)
      })

  if(!stream) return null

  // stream.getTracks().forEach((track) => myConn.addStream(track, stream))
  local = stream

  store.dispatch({
    type: 'UPDATE_STREAMING',
    payload: {
      local: stream,
      myConn
    }
  })

  return true
}

function sendOffer({ roomId, senderId, otherUserId }) {
  delete clients[otherUserId]

  const newPeer = new RTCPeerConnection(config)
  clients[otherUserId] = newPeer
  newPeer.addStream(local)

  newPeer
    .createOffer({ iceRestart: true })
    .then(sdp => newPeer.setLocalDescription(sdp))
    .then(() => {
      // send the offer to every other user
      ws.send(
        'newOffer',
        {
          roomId,
          senderId,
          offer: newPeer.localDescription
        }
      )
    })
    .catch(e => console.log('err create offer', e))

  newPeer.onicecandidate = e => {
    if(!e.candidate) return
    ws.send('iceCandidate', { roomId, senderId, candidate: e.candidate })
  }

  newPeer.onaddstream = (e) => {
    if(!e.stream) return

    newPeer.stream = e.stream

    store.dispatch({
      type: 'UPDATE_STREAMING',
      payload: { clients }
    })
  }

  newPeer.oniceconnectionstatechange = e => {
    const connState = newPeer.iceConnectionState
    // console.log('sendOffer connState', connState)

    /*
      On succesful rtc connection, send the other peer initial mute states
    */
    if(['completed', 'connected'].includes(connState)) {
      const { currentUserState: { audioMute, videoMute} } = store.getState().streaming
      const userId = store.getState().user._id

      ws.send('sendMediaState', { roomId, audioMute, videoMute, senderId: userId })
    }

    store.dispatch({
      type: 'UPDATE_STREAMING',
      payload: { clients }
    })
  }
}

function onOffer({ roomId, offer, senderId, userId }) {
  delete clients[senderId]

  let newPeer = new RTCPeerConnection(config)
  clients[senderId] = newPeer

  newPeer.addStream(local)

  newPeer
    .setRemoteDescription(new RTCSessionDescription(offer))
    .then(() => newPeer.createAnswer())
    .then(sdp => newPeer.setLocalDescription(sdp))
    .then(() => {
      ws.send(
        'newAnswer',
        {
          roomId,
          senderId: userId,
          offer: newPeer.localDescription,
        }
      )
    })
    .catch(e => console.log('err onOffer', e, offer))

  newPeer.onicecandidate = e => {
    if(!e.candidate) return
    ws.send('iceCandidate', { roomId, senderId: userId, candidate: e.candidate })
  }

  newPeer.onaddstream = (e) => {
    if(!e.stream) return

    newPeer.stream = e.stream

    store.dispatch({
      type: 'UPDATE_STREAMING',
      payload: { clients }
    })
  }

  newPeer.oniceconnectionstatechange = e => {
    const connState = newPeer.iceConnectionState
    // console.log('onOffer connState', connState)

    /*
      On succesful rtc connection, send the other peer initial mute states
    */
    if(['completed', 'connected'].includes(connState)) {
      const { currentUserState: { audioMute, videoMute} } = store.getState().streaming
      const userId = store.getState().user._id

      ws.send('sendMediaState', { roomId, audioMute, videoMute, senderId: userId })
    }

    store.dispatch({
      type: 'UPDATE_STREAMING',
      payload: { clients }
    })
  }
}

function onAnswer({ senderId, offer, userId }) {
  clients[senderId].setRemoteDescription(
    new RTCSessionDescription(offer)
  )

  clients[senderId].isReady = true

  store.dispatch({
    type: 'UPDATE_STREAMING',
    payload: { clients }
  })
}

function addIceCandidate({ senderId, candidate }) {
  if(clients[senderId]) {
    clients[senderId].addIceCandidate(new RTCIceCandidate(candidate))

    clients[senderId].isReady = true

    store.dispatch({
      type: 'UPDATE_STREAMING',
      payload: { clients }
    })
  }
}

function toggleMute() {
  local
    .getAudioTracks()
    .forEach(track => {
      track.enabled = !track.enabled
    })
}

function toggleVideo() {
  if(!local) return
  local
    .getVideoTracks()
    .forEach(track => {
      track.enabled = !track.enabled
    })
}

function switchCamera() {
  local
    .getVideoTracks()
    .forEach(track => {
      track._switchCamera()
    })
}

function close() {
  if(!myConn) return

  // CLOSE CONN FOR EACH PEER
  Object.keys(clients).forEach(x => clients[x].close())
  // CLOSE CONN FOR SELF
  myConn.close()

  // STOP LOCAL STREAM
  local.getTracks().forEach((track) =>  {
    track.stop()
  })

  clients = {}
  myConn = undefined
  local = undefined

  store.dispatch({
    type: 'UPDATE_STREAMING',
    payload: {
      clients: {},
      clientStates: {},
      myConn: undefined,
      local: undefined,
    }
  })
}

function hangUp({ userId }) {
  clients[userId]?.close()
  delete clients[userId]

  store.dispatch({
    type: 'UPDATE_STREAMING',
    payload: { clients }
  })
}

export default {
  localStream,
  sendOffer,
  onOffer,
  onAnswer,
  addIceCandidate,
  close,
  local,
  clients,
  hangUp,
  toggleMute,
  toggleVideo,
  switchCamera
}
