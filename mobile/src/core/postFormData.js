import { Platform } from 'react-native'
import { store } from '../store'
import { Api, getToken } from 'lib'
const API_URL = process.env.REACT_APP_API_URL

/*
  This is a fix for axios not sending formdata requests
  on android when there is a file appended
*/
async function postFormData(endpoint, data, button) {
  if(Platform.OS === 'ios') {
    const res = await Api.post(endpoint, data, button)

    return res
  }

  store.dispatch({
    type: 'LOADING_BUTTON',
    payload: button
  })

  const token = await getToken(),
    headers = new Headers()

  headers.append('Authorization', `Bearer ${token}`)

  const res = await fetch(
    `${API_URL}${endpoint}`,
    {
      method: 'POST',
      headers,
      body: data
    }
  )

  store.dispatch({
    type: 'LOADING_BUTTON',
    payload: null
  })

  return res
}

export default postFormData
