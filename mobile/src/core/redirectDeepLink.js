import { navigator } from 'core'
import { store } from 'store'

function redirectDeepLink(props) {
  const url = props?.url
  if(!url) return

  const { navigate } = navigator
  const user = store.getState().user

  const replacedUrl = url.replace('https://playaz4playaz.com/', '')
  const [target, params] = replacedUrl.split('?')

  switch (true) {
    case target.includes('reset-password') || target.includes('forgot-password'):
      const resetCode = params.split('=')[1]

      if(user) return
      navigate('CreateNewPassword', { code: resetCode.replace('&lang', '') })
      break

    case target.includes('verify-email'):
      navigate('VerifyEmail', { emailToken: target.split('/')[1] })
      break

    case target.includes('signup'):
      navigate('SignUp', { code: params.split('=')[1] })
      break

    case target.includes('post'):
      const postId = target.split('/')[1]
      navigate('PostsFeed', { redirectedId: postId, data: [{ _id: postId }] })
      break

    case target.includes('profile'):
      const username = target.split('/')[1]
      navigate('StackProfile', { username, getByUsername: true })
      break
  }
}

export default redirectDeepLink
