import {
  check, checkMultiple, request, requestMultiple,
  PERMISSIONS, RESULTS, openSettings, requestNotifications
} from 'react-native-permissions'
import { Platform, Alert } from 'react-native'

//  perm is the specific permission that we are requesting
//  permissionResult is the current state of perm
let perm, permissionResult
const { UNAVA<PERSON><PERSON>LE, DENIED, GRANTED, LIMITED, BLOCKED } = RESULTS
const osVersion = Platform.constants['Release']

function getPermissionResultFromObject(obj) {
  let result = UNAVAILABLE
  const permissionValues = Object.entries(obj)

  const hasFullPermission = permissionValues.every(([_key, value]) => value === GRANTED)
  const hasLimitedPermission = permissionValues.some(([_key, value]) => value === GRANTED || value === LIMITED)
  const isBlocked = permissionValues.some(([_key, value]) => value === BLOCKED)
  const isDenied = permissionValues.some(([_key, value]) => value === DENIED)

  if (isDenied) {
    result = DENIED
  }

  if (isBlocked) {
    result = BLOCKED
  }

  if (hasLimitedPermission) {
    result = LIMITED
  }

  if (hasFullPermission) {
    result = GRANTED
  }

  return result
}


async function handleResult(args) {
  const { checkedPermission, onBlocked } = args

  switch (permissionResult) {
    case GRANTED:
    case LIMITED:
      return true
    case UNAVAILABLE:
      return false
    case BLOCKED:
      //  Custom option if user blocks permission
      if(onBlocked) {
        onBlocked()
        return false
      }

      Alert.alert(
        'Required Permissions Blocked',
        `Please go to your device settings and enable ${checkedPermission}.`,
        [
          {
            text: 'OK',
            onPress: openSettings
          }
        ]
      )
      return false
    case DENIED:
      if (Array.isArray(perm)) {
        await requestMultiple(perm).then(result => {
          permissionResult = getPermissionResultFromObject(result)
        })
      } else {
        await request(perm).then(result => {
          permissionResult = result
        })
      }

      // To handle Android permissions asking twice when the user denied
      if (Platform.OS === 'android' && permissionResult === DENIED) {
        permissionResult = BLOCKED
      }
  }

  return await handleResult(args)
}

async function handlePermission(args) {
  const { checkedPermission, onBlocked } = args,
    isAndroid = Platform.OS === 'android',
    permissionObject = PERMISSIONS[isAndroid ? 'ANDROID' : 'IOS']

  switch (checkedPermission) {
    case 'camera':
      perm = permissionObject.CAMERA
      break

    case 'gallery':
      if (isAndroid) {        
        if (osVersion >= 13) {
          perm = [
            permissionObject.READ_MEDIA_IMAGES,
            permissionObject.READ_MEDIA_VIDEO,
          ]
        } else {
          perm = permissionObject.READ_EXTERNAL_STORAGE
        }
      } else {
        perm = permissionObject.PHOTO_LIBRARY
      }
      break

    case 'microphone':
      perm = permissionObject[isAndroid ? 'RECORD_AUDIO' : 'MICROPHONE']
      break

    case 'notification':
      perm = permissionObject[isAndroid ? 'POST_NOTIFICATIONS' : null]

      if(!isAndroid) {
        await requestNotifications(['alert', 'sound', 'badge'])
          .then(({ status, settings }) => {
            // console.log('---------------------------------------------------------------------------------')
            // console.log(status)
            // console.log('---------------------------------------------------------------------------------')
            // console.log('---------------------------------------------------------------------------------')
            // console.log(settings)
            // console.log('---------------------------------------------------------------------------------')
          })
      }
      break
  }

  if(!perm) return

  if (Array.isArray(perm)) {
    permissionResult = UNAVAILABLE

    await checkMultiple(perm).then(result => {
      permissionResult = getPermissionResultFromObject(result)
    })
  } else {
    await check(perm).then(result => {
      permissionResult = result
    })
  }
  
  return await handleResult(args)
}

export default handlePermission
