import { Platform, NativeModules } from 'react-native'

function getLanguage() {
  let lang = Platform.OS === 'android'
    ? NativeModules.I18nManager.localeIdentifier
    : NativeModules.SettingsManager.settings.AppleLocale
      || NativeModules.SettingsManager.settings.AppleLanguages[0] //iOS 13

  lang = lang.split('_')[0]

  if(!['en', 'fr', 'el', 'nl', 'ja', 'it'].includes(lang)) lang = 'en'

  return lang
}

export default getLanguage
