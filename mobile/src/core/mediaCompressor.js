import {
  Image,
  Video,
  getRealPath
} from 'react-native-compressor'
import { Platform } from 'react-native'

async function image(uri, maxWidth = 1080, maxHeight = 1080, quality = 0.6) {
  const res = await Image.compress(
    uri,
    {
      maxWidth,
      maxHeight,
      quality,
    }
  )

  return res
}

async function video(uri) {
  const res = await Video.compress(
    uri,
    {
      compressionMethod: 'auto',
      minimumFileSizeForCompress: 0.1,
      maxSize: 1000,
    },
    (progress) => {
      // console.log('progress', progress)
    }
  )

  return res
}

async function getPath(uri, type) {
  const isAndroid = Platform.OS === 'android'
  const realPath = await getRealPath(uri, type)

  if (isAndroid && realPath.match(/file:\/\//g)?.length > 1) {
    return realPath.replace('file://', '')
  }

  return realPath
}

export default { image, video, getPath }
