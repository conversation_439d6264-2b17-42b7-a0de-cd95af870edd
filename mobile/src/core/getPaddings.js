import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { getStatusBarHeight } from 'react-native-status-bar-height'
import { Platform } from 'react-native'
import { hasNotch, hasDynamicIsland } from 'react-native-device-info'

function getPaddings() {
  const insets = useSafeAreaInsets()
  const top = getStatusBarHeight()
    
  return {
    ...insets,
    top
  }
}

export default getPaddings