import { createNavigationContainerRef, StackActions } from '@react-navigation/native'

const navigationRef = createNavigationContainerRef()

/**
 * Custom navigator to access from anywhere within the app
 */
function navigate(name, params = {}, operation) {
  if(navigationRef.isReady()) {
    navigationRef[operation || 'navigate'](name, params)
  }
}

function replace(name, params = {}) {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(StackActions.replace(name, params));
  }
}

export default { navigationRef, navigate, replace }
