import DeviceInfo from 'react-native-device-info'
import { Dimensions } from 'react-native'

const deviceHeights = {
  'iPhone X': 812,
  'iPhone XS': 812,
  'iPhone XS Max': 896,
  'iPhone XR': 896,
  'iPhone 8': 667,
  'iPhone 8 Plus': 736
}

function checkIsZoomed() {
  const { height } = Dimensions.get('window')
  const deviceName = DeviceInfo.getModel()

  const foundHt = deviceHeights[deviceName]
  if(!foundHt) return false

  return foundHt > height
}

export default checkIsZoomed
