import { Dimensions } from 'react-native'
const { width, height } = Dimensions.get('window')

const guidelineBaseWidth = 390
const guidelineBaseHeight = 844
const screenSize = Math.sqrt(width * height) / 100

function scale(size) {
  return (width / guidelineBaseWidth) * size
}

function verticalScale(size) {
  return (height / guidelineBaseHeight) * size
}

function moderateScale (size, factor = 0.5) {
  return size + (scale(size) - size) * factor
}

const scaleToScreen = { scale, verticalScale, moderateScale, screenSize }

export default scaleToScreen
