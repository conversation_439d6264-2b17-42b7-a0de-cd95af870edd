import rnTextSize from 'react-native-text-size'
import { Dimensions, Platform } from 'react-native'

async function getCalculatedText(caption, options = {}) {
  if(!caption) return caption

  const {
    numberOfLines = 2,
    width = Dimensions.get('window').width - 32,
    maxLength = 1280,
    addToStart = ''
  } = options

  const replaced = caption.replace(/\n/g, " ")

  const [calc, shortCalc] = Platform.OS === "ios" ? ["", ""] :  await Promise.all([
    rnTextSize
      .measure({
        text: caption,
        fontSize: 14,
        fontFamily: 'CeraPro-Regular',
        usePreciseWidth: true,
        lineInfoForLine: numberOfLines - 1,
        width
      })
      .then(res => res)
      .catch(err => false),
    rnTextSize
      .measure({
        text: replaced,
        fontSize: 14,
        fontFamily: 'CeraPro-Regular',
        usePreciseWidth: true,
        lineInfoForLine: numberOfLines - 1,
        width
      })
      .then(res => res)
      .catch(err => false)
  ])

  if(calc.lineCount <= numberOfLines || !calc || !shortCalc) {
    return caption
  }

  const sliceEnd = calc.lineInfo.end < 12
    ? shortCalc?.lineInfo?.end || replaced.length
    : shortCalc?.lineInfo?.end ? shortCalc?.lineInfo?.end - 12 : replaced.length

  const shortText = `${replaced.slice(0, sliceEnd)}`

  return shortText
}

export default getCalculatedText
