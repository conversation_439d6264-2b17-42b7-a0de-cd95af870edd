import { useEffect } from 'react'
import { useNavigation } from '@react-navigation/native'

const useFocusListener = (callBack) => {
  const navigation = useNavigation()

  useEffect(() => {
    const focusListener = navigation.addListener('focus', () => {
      callBack(true)
    })

    const blurListener = navigation.addListener('blur', () => {
      callBack(false)
    })

    return () => {
      focusListener()
      blurListener()
    }
  }, [])
}

export default useFocusListener
