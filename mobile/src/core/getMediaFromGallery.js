import { mediaCompressor } from './'

/*
  NOTE: There is an issue with file paths beginning with "ph" on IOS,
  read it from asset library instead
*/
async function getFile(item) {
  let file = ''
  if(!item) return
  if(item.type.includes('video')) {
    const appleId = item.image.uri.substring(5, 41)
    const realPath = await mediaCompressor.getPath(item.image.uri, 'video')

    const fileName = item.image.filename

    const fileNameLength = fileName?.length
    const ext = fileName?.substring(fileNameLength - 3)

    file = {
      realPath,
      uri: fileName
        ? `assets-library://asset/asset.${ext}?id=${appleId}&ext=${ext}`
        : item.image.uri,
      duration: item.image.playableDuration
    }
  } else {
    const realPath = await mediaCompressor.getPath(item.image.uri, 'image')
    file = {
      realPath,
      uri: item.image.uri
    }
  }

  return file
}

export default getFile