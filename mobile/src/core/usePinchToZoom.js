import { useMemo } from 'react'
import { Gesture } from 'react-native-gesture-handler'
import {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  cancelAnimation,
  runOnJS
} from 'react-native-reanimated'
import { useDispatch, useSelector } from 'react-redux'

const usePinchToZoom = ({ onPinch, item }) => {
  const isPinchingPost = useSelector(state => state.appState.pinchedPost?.isPinchingPost)
  const dispatch = useDispatch()

  const offset = useSharedValue({ x: 0, y: 0 })
  const start = useSharedValue({ x: 0, y: 0 })
  const scale = useSharedValue(1)
  const isPinching = useSharedValue(false)

  const minimumZoomScale = 1
  const maximumZoomScale = 8

  const translationX = useSharedValue(0)
  const translationY = useSharedValue(0)
  const originX = useSharedValue(0)
  const originY = useSharedValue(0)
  const viewHeight = useSharedValue(0)
  const viewWidth = useSharedValue(0)

  const prevScale = useSharedValue(0)
  const offsetScale = useSharedValue(0)
  const prevTranslationX = useSharedValue(0)
  const prevTranslationY = useSharedValue(0)

  const panTranslateX = useSharedValue(0)
  const panTranslateY = useSharedValue(0)

  const pinchAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: offset.value.x },
        { translateY: offset.value.y },
        { scale: scale.value },
      ]
    }
  })

  const dragGesture = useMemo(() => {
    const drag = Gesture.Pan()
      .minPointers(2)
      .maxPointers(2)
      .runOnJS(true)
      .onStart((_e) => {
        if (scale.value !== 1 && !isPinchingPost) {
          dispatch({
            type: 'SET_PINCHING_POST',
            payload: { postId: item?._id }
          })
        }

        isPinching.value = true
        onPinch(true)
      })
      .onUpdate((e) => {
        if (scale.value !== 1) {
          offset.value = {
            x: e.translationX + start.value.x,
            y: e.translationY + start.value.y,
          }
        }
      })
      .onEnd(() => {
        start.value = { x: 0, y: 0 }
        offset.value = { x: 0, y: 0 }
        scale.value = withTiming(1)

        isPinching.value = false

        setTimeout(() => {
          dispatch({
            type: 'RESET_PINCHING_POST'
          })

          onPinch(false)
        }, 100);
      })

    return drag
  }, [])

  const pinchGesture = useMemo(() => {
    const pinch = Gesture.Pinch()
      .onStart(() => {
        cancelAnimation(translationX)
        cancelAnimation(translationY)
        cancelAnimation(scale)
        prevScale.value = scale.value
        offsetScale.value = scale.value

        if (!isPinchingPost) {
          runOnJS(dispatch)({
            type: 'SET_PINCHING_POST',
            payload: { postId: item?._id }
          })
        }

        runOnJS(onPinch)(true)
      })
      .onUpdate((e) => {
        if (e.numberOfPointers === 2) {
          const newScale = prevScale.value * e.scale

          if (newScale < minimumZoomScale || newScale > maximumZoomScale)
            return

          scale.value = newScale

          // reset the origin
          if (!isPinching) {
            isPinching.value = true

            originX.value = e.focalX
            originY.value = e.focalY
            prevTranslationX.value = translationX.value
            prevTranslationY.value = translationY.value
            offsetScale.value = scale.value
          }

          if (isPinching) {
            translationX.value = (prevTranslationX.value + (-1) * ((scale.value - offsetScale.value) * (originX.value - viewWidth.value / 2)))
            translationY.value = (prevTranslationY.value + (-1) * ((scale.value - offsetScale.value) * (originY.value - viewHeight.value / 2)))
          }
        }
      })
      .onEnd(() => {
        isPinching.value = false

        prevTranslationX.value = translationX.value
        prevTranslationY.value = translationY.value

        translationX.value = withTiming(0)
        translationY.value = withTiming(0)
        scale.value = withTiming(1)

        originX.value = 0
        originY.value = 0

        prevScale.value = 0
        prevTranslationX.value = 0
        prevTranslationY.value = 0

        panTranslateX.value = 0
        panTranslateY.value = 0
        
        runOnJS(dispatch)({ type: 'RESET_PINCHING_POST' })
        runOnJS(onPinch)(false)
      })

    return pinch
  }, [])

  return {
    pinchAnimatedStyle,
    scale,
    dragAndPinchGesture: Gesture.Simultaneous(dragGesture, pinchGesture)
  }
}

export default usePinchToZoom