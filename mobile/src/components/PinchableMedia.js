import React, { useState, useEffect } from 'react'
import { Gesture, GestureDetector } from 'react-native-gesture-handler'
import { Platform, View, AppState } from 'react-native'
import mime from 'mime'
import Video from 'react-native-video'
import ImageViewer from '@react-native-ohos/react-native-image-zoom-viewer'
import Animated from 'react-native-reanimated'
import { useNavigation, useRoute } from '@react-navigation/native'
import { isTablet } from 'react-native-device-info'
// import convertToProxyURL from 'react-native-video-cache'
import usePinchToZoom from '../core/usePinchToZoom'
import { Media, Youtube } from '../components/Post/components'
import { Col, Modal, Spinner } from '../components'
import { useSelector } from 'react-redux'
import useUpdateViewsCount from '../screens/Hooks/useUpdateViewsCount'


const PinchableMedia = ({ item, onDoubleTap, onPinch, allowPinching = true, isNoAspectRatio = false, displayPreview = false, setOtherUser, isCurrentUser, didUpdateProfileCount, ...props }) => {
  const [showImageFullscreen, setShowImageFullscreen] = useState(false)
  const [showVideoFullscreenAndroid, setShowVideoFullscreenAndroid] = useState(false)
  const [showYoutubeFullscreenAndroid, setShowYoutubeFullscreenAndroid] = useState(false)
  const [isVideoLoading, setIsVideoLoading] = useState(true)
  const [isPause, setIsPause] = useState(false)
  const [videoDuration, setVideoDuration] = useState(0)
  const [hasCountedView, setHasCountedView] = useState(false);
  const { pinchAnimatedStyle, dragAndPinchGesture, scale } = usePinchToZoom({ onPinch, item })
  const firstMedia = item?.media[0]
  const isVideo = mime.getType(firstMedia?.uri)?.includes('video') || firstMedia?.uri?.includes('m3u8')
  const youtubeId = item?.youtubeId
  const isAndroid = Platform.OS === 'android',
    navigation = useNavigation(),
    routeName = useRoute().name

  const currentUserId = useSelector(state => state?.user?._id) || null;
  const updateViewsCount = useUpdateViewsCount();

  let videoPlayerRef = null

  useEffect(() => {
    const handleAppStateChange = (nextAppState) => {
      if (nextAppState === 'background' || nextAppState === 'inactive') {
        setIsPause(true);
      } else {
        setIsPause(false);
      }
    };
    // Will listen if the app is in background, inactive or active
    const appState = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      appState.remove();
    };
  }, []);

  useEffect(() => {
    // Reset the view count when the video changes
    setHasCountedView(false);
  }, [firstMedia?.uri]);

  const handleCount = async ({
    userId,
    currentUserId,
    trigger,
    postId = null,
  }) => {
    if (routeName === 'LetsChatSportsComments') return;

    const res = await updateViewsCount(userId, currentUserId, trigger, postId);

    if (res?.user && !isCurrentUser && !res.error) {
      didUpdateProfileCount(res.user.newCount);

    }
  };

  const handleSingleTap = Gesture.Tap()
    .runOnJS(true)
    .onEnd((_event, success) => {
      if (success && scale.value === 1) {
        let trigger = "video";
        if (isVideo) {
          if (isAndroid && !showVideoFullscreenAndroid) {
            navigation.navigate('VideoPreview', { item: { ...item, aspectRatio: isTablet ? 2 : 1 } })
          } else {
            videoPlayerRef?.presentFullscreenPlayer()
          }
        } else if (youtubeId) {
          trigger = "link"
          setShowYoutubeFullscreenAndroid(true)
        } else {
          trigger = "photo"
          setShowImageFullscreen(true)
        }
        //Will call for Post and Profile Views
        handleCount({
          userId: item.owner._id,
          currentUserId,
          trigger,
          postId: item?._id,
        });

      }
    });

  const handleDoubleTap = Gesture.Tap()
    .runOnJS(true)
    .numberOfTaps(2)
    .onEnd((_event, success) => {
      if (success && scale.value === 1) {
        onDoubleTap()
      }
    });

  const composedGestures = allowPinching ?
    Gesture.Race(dragAndPinchGesture, Gesture.Exclusive(handleDoubleTap, handleSingleTap)) :
    Gesture.Exclusive(handleSingleTap)


  // For debugging purposes
  const onError = (err) => {
    console.log('Error displaying video: ', err, item.caption)
  }

  const handleVideoProgress = (progress) => {
    const { currentTime } = progress;

    if (videoDuration > 0 &&
      (currentTime >= 30 || Math.trunc(currentTime) === Math.trunc(videoDuration)) &&
      !hasCountedView) {

      handleCount({
        userId: item.owner._id,
        currentUserId,
        trigger: "video",
        postId: item?._id,
      });

      setHasCountedView(true);

    }
  }

  const videoStyle = {
    aspectRatio: isNoAspectRatio ? undefined : item.aspectRatio || 1,
    height: isNoAspectRatio ? "100%" : undefined
  };

  return (
    <>
      <GestureDetector gesture={composedGestures}>
        <Animated.View style={pinchAnimatedStyle}>
          {
            isVideo && firstMedia?.uri ?
              <Video
                ref={ref => { videoPlayerRef = ref }}
                resizeMode='cover'
                source={{ ...firstMedia, uri: firstMedia.uri }}
                style={videoStyle}
                controls={false}
                paused={!props.isInView || showVideoFullscreenAndroid || isPause}
                repeat
                onError={onError}
                ignoreSilentSwitch={'ignore'}
                onLoad={(data) => {
                  setVideoDuration(data.duration)
                  // fallback for android
                  if (displayPreview) {
                    videoPlayerRef.seek(0);
                  }
                }}
                onProgress={handleVideoProgress}
              /> :
              <Media
                item={item}
                {...props}
              />
          }
        </Animated.View>
      </GestureDetector>

      <View style={{ position: 'absolute', flex: -1 }}>
        <Modal
          visible={showImageFullscreen && !isVideo}
          transparent={true}
          fullScreen
          closeModal={() => setShowImageFullscreen(false)}
          bg='#000'
          closeCol='#fff'
        >
          <ImageViewer imageUrls={item?.media.map((med) => {
            if (med) {

              return { url: med.uri }
            }
          })} />
        </Modal>
      </View>
      <Modal
        visible={showVideoFullscreenAndroid && isVideo}
        transparent={true}
        fullScreen
        closeModal={() => setShowVideoFullscreenAndroid(false)}
        bg='#000'
        closeCol='#fff'
      >
        {
          isVideoLoading && (
            <Col centerAll ht='100%' noFlex>
              <Spinner size={40} />
            </Col>
          )
        }

        {
          isVideo && firstMedia?.uri &&
          <Video
            ref={ref => { videoPlayerRef = ref }}
            resizeMode='contain'
            source={{ ...firstMedia, uri: firstMedia.uri }}
            style={{ flex: 1, aspectRatio: item.aspectRatio || 1, maxWidth: '100%' }}
            controls={true}
            paused={!props.isInView && !showVideoFullscreenAndroid}
            onReadyForDisplay={() => setIsVideoLoading(false)}
            onError={onError}
            repeat
          />
        }

      </Modal>

      {/* <Modal
        visible={showYoutubeFullscreenAndroid}
        transparent={true}
        fullScreen
        closeModal={() => setShowYoutubeFullscreenAndroid(false)}
        bg='#000'
        closeCol='#fff'
      >
        <View style={{ flex: 1, justifyContent: 'center' }}>
          <Youtube
            youtubeId={youtubeId}
            style={{ flex: 1, aspectRatio: item.aspectRatio || 1 }}
            paused={false}
            isInView={false}
          />
        </View>
      </Modal> */}
    </>
  )
}

export default PinchableMedia