import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import { useNavigation } from '@react-navigation/native'
import { CameraRoll } from '@react-native-camera-roll/camera-roll'
import { getMediaFromGallery, mediaCompressor } from 'core'
import { Clickable } from 'components'
import { MediaPicker } from 'screens/Add/components'

const Image = styled.Image`
  width: 38px;
  height: 38px;
  border-width: 2px;
  border-color: #fff;
  border-radius: 40px;
`

const OpenGallery = (props) => {
  const [thumbnail, setThumbnail] = useState(null),
    [visible, setVisible] = useState(false),
    navigation = useNavigation(),
    { type } = props

  function handleVisible() {
    setVisible(!visible)
  }

  useEffect(() => {
    getThumbnail()  
  }, [])

  async function getThumbnail() {
    const cRoll = await CameraRoll
      .getPhotos({ first: 1 })
      .then(r => r)
      .catch(e => false)

    if(!cRoll) return
    
    const { page_info, edges } = cRoll

    const item = edges[0]?.node
    if(!item?.image) return

    const file = await getMediaFromGallery(item)
    setThumbnail(file)
  }

  async function onSave(selected = []) {
    const data = selected[0]
    if(!data) return
    
    const { isVideo, realPath, uri, duration } = data

    switch (type) {
      case 'startChat':
        navigation.navigate('StartChat', {
          cameraMedia: data
        })
        break;

      default:
        navigation.navigate(
          'EditStory', 
          { 
            captured: isVideo ? { path: realPath } : uri, 
            duration 
          }
        )
        break;
    }
  }

  return (
    <React.Fragment>
      <Clickable onClick={handleVisible}>
        <Image source={{ uri: thumbnail?.uri || thumbnail }} resizeMode='center' />
      </Clickable>

      <MediaPicker
        entityType='story'
        visible={visible}
        onSave={onSave}
        closeModal={handleVisible}
      />
    </React.Fragment>
  )
}

export default OpenGallery