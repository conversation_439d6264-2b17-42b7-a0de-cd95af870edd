import React from 'react'
import { getTopPadding } from 'core'
import { Col, Clickable, Icon } from 'components'

const Flash = ({ isStory, hasFlash, handleFlash }) => {

  return (
    <Col noFlex marg={`${isStory ? '16' : getTopPadding(true) + 48}px 16px 0`}>
      <Clickable onClick={handleFlash}>
        <Col 
          wid='40px' 
          ht='40px' 
          noFlex
          hasRadius='40px' 
          centerAll
          bg='rgba(210, 216, 223, 0.7)'
        >
          <Icon
            dimensions={20}
            type={hasFlash ? 'flashOn' : 'flashOff'}
          />
        </Col>
      </Clickable>
    </Col>
  )
}

export default Flash