import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { useTranslation } from 'react-i18next'
import { theme } from 'lib'
import { Clickable } from 'components'

const Circle = styled.View`
  width: 60px;
  height: 60px;
  border-width: 4px;
  border-color: #fff;
  border-radius: 60px;
  align-items: center;
  justify-content: center;
`

const RecordingCircle = styled.View`
  width: 80%;
  height: 80%;
  border-radius: 100px;
  background-color: ${theme.SECONDARY};
`

let timer
const CaptureButton = ({
  cameraRef,
  hasFlash,
  capturePhoto,
  onRecordingStart,
  onRecordingEnd,
  onDurationChange,
  durationLimit = 0
}) => {
  const [recording, setRecording] = useState(false),
    [duration, setDuration] = useState(0),
    { t } = useTranslation()

  useEffect(() => {
    if(!recording) {
      clearInterval(timer)

      return
    }

    timer = setInterval(() => {
      setDuration(duration + 1)
    }, 1000)

    return () => clearTimeout(timer)
  })

  useEffect(() => {
    if(!recording || !onRecordingStart) return

    onRecordingStart()
  }, [recording])

  useEffect(() => {
    onDurationChange(duration)
    if(!durationLimit || durationLimit >= duration) return 

    onPressOut()
  }, [duration])

  function onLongPress() {
    if(!cameraRef?.current) return

    setRecording(true)

    cameraRef.current.startRecording({
      flash: hasFlash ? 'on' : 'off',
      onRecordingFinished: (video) => onRecordingEnd(video),
      onRecordingError: (error) => console.error('err', error),
    })
  }

  async function onPressOut() {
    if(!cameraRef?.current) return

    setRecording(false)
    if(!recording) return

    setDuration(0)
    await cameraRef.current.stopRecording()
  }

  return (
    <Clickable
      onPress={capturePhoto}
      onLongPress={onLongPress}
      onPressOut={onPressOut}
      hitSlop={40}
    >
      <Circle>
        {recording && (
          <RecordingCircle />
        )}
      </Circle>
    </Clickable>
  )
}

export default CaptureButton
