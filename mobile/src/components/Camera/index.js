import React, { useState, useEffect, useRef } from 'react'
import { Dimensions, Image, Platform } from 'react-native'
import { useDispatch } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { Camera, useCameraDevices } from 'react-native-vision-camera'
import { useRoute, useNavigation } from '@react-navigation/native'
import { isNewIphone, handlePermission, useFocusListener } from 'core'
import { getDuration } from 'lib'
import { Col, Header, Text, Row, Clickable, Icon } from 'components'
import { CaptureButton, Flash, OpenGallery } from './components'

let timer
const CustomCamera = ({ type, entityParams, fromAddNavigator, notAddScreen, dontUpdateBarStyle }) => {
  const [captured, setCaptured] = useState(null),
    [camera, setCamera] = useState(null),
    [hasFlash, setHasFlash] = useState(false),
    [isFront, setIsFront] = useState(false),
    [zoom, setZoom] = useState(1),
    [duration, setDuration] = useState(0),
    [recording, setRecording] = useState(false),
    [isFocused, setIsFocused] = useState(false),
    { t } = useTranslation(),
    dispatch = useDispatch(),
    navigation = useNavigation(),
    cameraRef = useRef(),
    route = useRoute(),
    isAndroid = Platform.OS === 'android',
    deviceWid = Dimensions.get('window').width,
    isStory = type === 'story',
    isMediaShare = type === 'mediaShare',
    isMediaShareGallery = type === 'mediaShareGallery';

  const devices = useCameraDevices()

  useFocusListener(handleFocus)

  useEffect(() => {
    handleFocus(true);
  }, [])
  
  useEffect(() => {
    if (!isFocused) {
      setCamera(null)
      return
    }

    if (!devices || devices.length === 0) return

    const targetPosition = isFront ? 'front' : 'back'
    const selectedDevice = devices.find(device => device.position === targetPosition)

    if (selectedDevice && selectedDevice !== camera) {
      setCamera(selectedDevice)
      setZoom(1)
    }
  }, [devices, isFocused, isFront])

  useEffect(() => {
    return () => {
      setCamera(null)
      clearTimeout(timer)
    }
  }, [])

  /*
    Listen to screen focus in order to update camera and other conditions
  */
  async function handleFocus(e) {
    clearTimeout(timer)

    if(e) {
      if(!dontUpdateBarStyle) {
        dispatch({
          type: 'UPDATE_BAR_STYLE',
          payload: 'light-content'
        })
      }

      await handlePermission(
        {
          checkedPermission: 'camera',
          onBlocked: () => handleBlocked(t('permissions.camera'))
        }
      )
        .then(() => (
          handlePermission({
            checkedPermission: 'microphone',
            onBlocked: () => handleBlocked(t('permissions.microphone'))
          })
        ))
    }

    timer = setTimeout(() => {
      setIsFocused(e)
    }, e ? 0 : 500)
  }

  function handleBlocked(permission) {
    navigation.navigate('PermissionBlocked', { permission })
  }

  function switchCamera() {
    if (!devices || devices.length === 0) return
    
    setIsFront(!isFront)
  }

  function handleFlash() {
    setHasFlash(!hasFlash)
  }

  function handleZoom(isFront) {
    if(!cameraRef?.current) return
    const nextZoom = zoom * 2
    const maxZoom = camera.maxZoom || 10

    setZoom(nextZoom > maxZoom ? 1 : nextZoom)
  }

  async function capturePhoto() {
    if(!cameraRef?.current || captured) return

    const photo = await cameraRef.current.takePhoto({
      qualityPrioritization: 'speed',
      flash: hasFlash ? 'on' : 'off'
    })

    if(!photo) {
      dispatch({
        type: 'LOADING_BUTTON',
        payload: null
      })

      return
    }

    setCaptured(photo.path)
    handleNext(photo.path)
  }

  function onRecordingStart() {
    setRecording(true)
  }

  function onRecordingEnd(video) {
    setRecording(false)

    handleNext(video)
  }

  function handleNext(captured) {
    if (type === 'story') {
      navigation.navigate('EditStory', { captured, duration: captured.duration })
    } else if (isMediaShare) {
      navigation.navigate("AppProfile", { captured });
    } else if(isMediaShareGallery){
      navigation.navigate("MediaShare", { captured });
    } else {
      navigation.push('CapturedMediaView', { captured, type, entityParams, notAddScreen })
    }

    setTimeout(() => setCaptured(null), 500)
  }

  function closeView() {
    if (type === 'story') {
      navigation.navigate('TabNavigator')
    } else if (isMediaShare) {
      navigation.navigate("AppProfile", { didGoBack: true })
    } else {
      navigation.goBack()
    }
  }

  return (
    <Col bg='#000' marg={isStory && '16px 0 0'} style={{ overflow: 'hidden' }}>
      {!isStory && (
        <Header
          transparent
          lightContent
          hasBack
          isModal
          title={t('common.addNew')}
          onBack={closeView}
        />
      )}

      <Flash
        isStory={isStory}
        hasFlash={hasFlash}
        handleFlash={handleFlash}
      />

      <Col ht='100%' absolute zIndex='-1' center>
        <Col noFlex style={{ overflow: 'hidden' }}>
          {captured ? (
            <Image
              source={{ uri: captured.toString() }}
              style={{
                height: undefined,
                width: deviceWid,
                aspectRatio: 9/16
              }}
            />
          ) : camera && isFocused && (
            <Camera
              ref={cameraRef}
              device={camera}
              isActive={!captured && isFocused}
              photo={true}
              video={true}
              audio={true}
              zoom={zoom}
              style={[
                {
                  height: undefined,
                  width: deviceWid,
                  aspectRatio: 9/16,
                  overflow: 'hidden'
                }
              ]}
            />
          )}
        </Col>
      </Col>

      <Col endAll pad={isNewIphone() ? '0 56px 42px' : '0 56px 8px'}>
        <Row noFlex centerAll>
          <Col>
            {(!isMediaShare && !isMediaShareGallery) && <OpenGallery type={type} />}
          </Col>

          {!recording
            ? (
              <Col
                noFlex
                absolute
                style={{ top: -150 }}
              >
                <Clickable onClick={handleZoom}>
                  <Col
                    marg='0 0 0'
                    wid='36.5px'
                    ht='36.5px'
                    hasRadius='50px'
                    centerAll
                    noFlex
                    hasBorder='1.5px #fff'
                    borderColor='#fff'
                  >
                    <Text col='#fff' med size='13px'>
                      {zoom}x
                    </Text>
                  </Col>
                </Clickable>
              </Col>
            ) : (
              <Col
                absolute
                zIndex={5}
                noFlex
                // topDistance={'-50px'}
                style={{ top: -50 }}
              >
                <Text marg='0 0 30px' col='#fff' b1 med align='center' >
                  {getDuration(duration)}
                </Text>
              </Col>
            )
          }

          <CaptureButton
            cameraRef={cameraRef}
            hasFlash={hasFlash}
            capturePhoto={capturePhoto}
            onRecordingStart={onRecordingStart}
            onRecordingEnd={onRecordingEnd}
            onDurationChange={setDuration}
            durationLimit={type === 'post' || isMediaShare
              ? 60
              : type === 'story'
                ? 15
                : null
            }
          />

          <Row endAll>
            <Clickable  onClick={switchCamera}>
              <Row wid='40px' ht='40px' hasRadius='40px' centerAll noFlex bg='rgba(210, 216, 223, 0.7)'>
                <Icon type='switchCamera' dimensions={20} />
              </Row>
            </Clickable>
          </Row>
        </Row>

        <Row noFlex centerAll marg='16px 0 0' ht='22px'>
          <Text col='#fff'>{!recording && t('add.tapOrHold')} </Text>
        </Row>
      </Col>
    </Col>
  )
}

export default CustomCamera
