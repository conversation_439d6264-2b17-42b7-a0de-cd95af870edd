import React, { useState } from 'react'
import styled from 'styled-components'
import { theme } from 'lib'

const StyledBadge = styled.View`
  height: 12px;
  width: 12px;
  border-radius: 12px;
  background-color: ${theme.SECONDARY};
  z-index: 1;

  ${props => props.absolute && (`
    position: absolute;
    top: -2px;
    right: -2px;
  `)}

  ${props => props.rightDistance && `right: ${props.rightDistance};`};
  ${props => props.leftDistance && `left: ${props.leftDistance};`};
  ${props => props.topDistance && `top: ${props.topDistance};`};
`

export default StyledBadge
