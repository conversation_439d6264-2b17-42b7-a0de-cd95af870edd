import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { Api } from 'lib'
import { Modal, Col, Text, Button, Input } from 'components'

const ReportModal = ({
  visible,
  item,
  entityType,
  closeModal
}) => {
  const [description, setDescription] = useState(''),
    { t } = useTranslation()

  useEffect(() => {
    setDescription('')
  }, [visible])

  async function submit() {
    const res = await Api.post(
      '/reports/create',
      {
        entityType,
        entityId: entityType === 'story' ? item[0]._id : item._id,
        description
      },
      'report'
    )
    
    if(!res) return

    closeModal()
  }

  function getTitle() {
    switch (entityType) {
      case 'post':
          return t('reports.reportingPost', { name: item?.owner?.name })
        break

      case 'shoutout':
          return t('reports.reportingShoutout', { name: item?.from?.name })
        break

      case 'story':
          return t('reports.reportingStory', { name: item?.owner?.name })
        break

      default:
    }
  }

  return (
    <Modal
      visible={visible}
      closeModal={closeModal}
      avoidKeyboard
    >
      <Col noFlex pad='16px'>

        <Text h4 align='center' marg='0 0 16px'>
          {getTitle()}
        </Text>

        <Input
          marg='0 0 24px 0'
          multiline
          ht='150px'
          placeholder={t('reports.descriptionPlaceholder')}
          value={description}
          onChangeText={setDescription}
        />

        <Col noFlex>
          <Button
            big
            disabled={!description}
            onClick={submit}
            marg='0 0 8px 0'
            text={t('common.proceed')}
            action='report'
          />

          <Button
            big
            bordered
            onClick={() => closeModal()}
            text={t('common.cancel')}
          />
        </Col>
      </Col>
    </Modal>
  )
}

export default ReportModal
