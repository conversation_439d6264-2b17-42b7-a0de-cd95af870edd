import React, { useState } from 'react'
import { useNavigation } from '@react-navigation/native'
import styled from 'styled-components'
import { Icon } from 'components'

const Wrapper = styled.TouchableOpacity`
  padding: 16px;
`

const BackButton = ({ lightContent, isModal, onBack}) => {
  const navigation = useNavigation()

  return (
    <Wrapper onPress={onBack ? onBack : () => navigation.goBack()}>
      <Icon
        type={isModal ? 'close' : 'arrowLeft'}
        dimensions={16}
        col={lightContent ? '#fff' : '#000'}
      />
    </Wrapper>
  )
}

export default BackButton
