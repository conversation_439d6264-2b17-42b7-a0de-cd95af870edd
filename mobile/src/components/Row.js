import styled from 'styled-components'
import { Platform } from 'react-native'

const isAndroid = Platform.OS === 'android'

const Row = styled.View`
  ${props => `
    flex-direction: row;
    background-color: ${props.bg || 'transparent'};

    ${
      props.centerAll &&
      `
      justify-content: center;
      align-items: center;
    `
    };

    ${
      props.center &&
      `
      align-items: center;
    `
    };

    ${props.between && `justify-content: space-between;`};
    ${props.around && `justify-content: space-around;`};
    ${props.evenly && `justify-content: space-evenly;`};
    ${props.alignEnd && `align-self: flex-end;`};
    ${props.alignStart && `align-self: flex-start;`};
    ${props.selfStart && `align-self: flex-start;`};
    ${props.selfCenter && `align-self: center;`};
    ${props.endAll && `justify-content: flex-end`};
    ${props.wid && `width: ${props.wid}`};
    ${props.minWid && `min-width: ${props.minWid}`};
    ${props.maxWid && `max-width: ${props.maxWid}`};
    ${
      props.startAll &&
      `
      justify-content: flex-start;
      align-items: flex-start;
    `
    };
    ${
      props.itemsStart &&
      `
      align-items: flex-start;
    `
    };
    ${props.marg && `margin: ${props.marg}`};
    ${props.pad && `padding: ${props.pad}`};
    ${props.opa && `opacity: ${props.opa}`};
    ${props.bg && `background-color: ${props.bg}`};
    ${props.ht && `height: ${props.ht}`};
    ${!props.noFlex && `flex: 1`};
    ${props.hasFlex && `flex: ${props.hasFlex}`};
    ${props.wrap && `flex-wrap: wrap`};
    ${props.hasBorder && `border: ${props.hasBorder}`};
    ${
      props.hasBorderBottom &&
      `border-bottom: ${props.hasBorderBottom}`
    };
    ${props.hasRadius && `border-radius: ${props.hasRadius}`};
    ${props.absolute && `position: absolute;`};
    ${props.relative && `position: relative;`};
    ${props.topDistance && `top: ${props.topDistance};`};
    ${props.bottomDistance && `bottom: ${props.bottomDistance};`};
    ${props.rightDistance && `right: ${props.rightDistance};`};
    ${props.leftDistance && `left: ${props.leftDistance};`};
    ${props.hiddenOverflow && `overflow: hidden;`};
    ${props.borderBottom && `border-bottom: ${props.borderBottom};`};
    ${props.borderTop && `border-top: ${props.borderTop};`};
    ${props.position && `position: ${props.position};`};
    ${props.minHt && `min-height: ${props.minHt};`};
    ${props.zIndex && `z-index: ${props.zIndex};`};

    ${props.individualRadius && `
      border-top-left-radius: ${props.individualRadius[0]};
      border-top-right-radius: ${props.individualRadius[1]};
      border-bottom-right-radius: ${props.individualRadius[2]};
      border-bottom-left-radius: ${props.individualRadius[3]};
    `}
  `}
`

export default Row
