import React, { useState } from 'react'
import { useSelector } from 'react-redux'
import styled from 'styled-components'

const StyledClickable = styled.TouchableOpacity`
  margin: ${props => props.marg || 0};
  padding: ${props => props.pad || 0};
  ${props => props.wid && `width: ${props.wid};`}
  ${props => props.ht && `height: ${props.ht};`}

  opacity: ${props => (props.disabled && !props.ogOpacity && !props.stop) ? 0.5 : 1};
`

const Clickable = (props) => {
  const loadingButton = useSelector(state => state?.appState?.loadingButton),
    isLoading = props.action && props.action === loadingButton

  return (
    <StyledClickable
      onPress={props.onClick}
      disabled={isLoading || props.stop}
      {...props}
    >
      {props.children}
    </StyledClickable>
  )
}

export default Clickable
