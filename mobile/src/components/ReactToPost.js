import React, { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import styled from 'styled-components'
import { Platform } from 'react-native'
import { Animated } from 'react-native'
import { theme, Api } from 'lib'
import { Icon, Clickable } from 'components'
import { getTopPadding, scaleToScreen } from 'core'

const reactions = [
  {
    icon: 'likeReact',
    type: 'like',
  }, {
    icon: 'loveReact',
    type: 'love',
  }, {
    icon: 'angryReact',
    type: 'angry',
  }, {
    icon: 'careReact',
    type: 'care',
  }, {
    icon: 'hahaReact',
    type: 'haha',
  }, {
    icon: 'sadReact',
    type: 'sad',
  }, {
    icon: 'wowReact',
    type: 'wow'
  }
]

const ReactionsContainer = styled.View`
  position: absolute;
  top: -35px;
  left: -25px;
  flex-direction: row;
  background-color: #fff;
  align-items: center;
  padding: 16px;
  border-radius: 64px;
  justify-content: space-between;
  width: 352px;
  z-index: 2;
  elevation: 2;

  ${props => props.post && `
    top: -20px;
    left: 16px;
  `}

  ${props => props.shoutout && `
    top: -20px;
    left: -10px;
  `}

  ${props => props.letsChatSportList && `
    top: -35px;
    left: -10px;
  `}
`

const ReactToPost = ({
  shoutout,
  small,
  post,
  handleSubmit,
  reactionsVisible,
  showReactions,
  setReactionsVisible,
  hideReaction,
  reaction,
  dimensions,
  letsChatSportList
}) => {
  const { verticalScale } = scaleToScreen

  function handleLongPress() {
    showReactions ? showReactions() : setReactionsVisible(!reactionsVisible)
  }

  function getIcon() {
    return reactions.find(x => x.type === reaction)?.icon || 'like'
  }

  return (
    <React.Fragment>
      {reactionsVisible && (
        <ReactionsContainer
          post={post}
          shoutout={shoutout}
          letsChatSportList={letsChatSportList}
          style={{
            shadowColor: 'black',
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 0.2,
            shadowRadius: 16
          }}
        >
          {reactions.map((x, i) => (
            <Clickable
              onClick={() => handleSubmit(x.type)}
              key={i}
              marg={i + 1 !== reactions.length && '0 5px 0 0'}
            >
              <Icon type={x.icon} dimensions={32} />
            </Clickable>
          ))}
        </ReactionsContainer>
      )}

      <Clickable
        pad={post ? `0 ${verticalScale(13)}px 0 16px` : '0'}
        marg={post ? '0' : '0 5px 0 0'}
        onLongPress={handleLongPress}
        onClick={() => handleSubmit(reaction || 'like')}
        hitSlop={10}
      >
        <Icon
          type={getIcon()}
          dimensions={dimensions || (small ? 16.5 : verticalScale(24))}
          col={small ? '#000' : theme.GREY_50}
        />
      </Clickable>
    </React.Fragment>
  )
}

export default ReactToPost
