import styled from 'styled-components'

const Col = styled.View`
  ${props => `
    flex-direction: ${props.reverse ? 'column-reverse' : 'column'};

    ${
      props.startAll &&
      `
      justify-content: flex-start;
      align-items: flex-start;
    `
    };

    ${
      props.centerAll &&
      `
      justify-content: center;
      align-items: center;
    `
    };

    ${
      props.center &&
      `
      justify-content: center;
      align-items: center;
    `
    };

    ${props.between && `justify-content: space-between;`};
    ${props.useEnd && `align-self: flex-end;`};
    ${props.endAll && `justify-content: flex-end`};
    ${props.endHorizontal && `align-items: flex-end`};
    ${props.pad && `padding: ${props.pad}`};
    ${props.bg && `background-color: ${props.bg}`};
    ${!props.noFlex && `flex: 1`};
    ${props.alignEnd && `align-self: flex-end;`};
    ${props.ht && `height: ${props.ht}`};
    ${props.minHt && `min-height: ${props.minHt}`};
    ${props.wid && `width: ${props.wid}`};
    ${props.zIndex && `z-index: ${props.zIndex}`};
    ${props.maxWid && `max-width: ${props.maxWid}`};
    ${props.maxHt && `max-height: ${props.maxHt}`};
    ${props.marg && `margin: ${props.marg}`};
    ${props.wrap && `flex-wrap: wrap`};
    ${props.hasRadius && `border-radius: ${props.hasRadius}`};
    ${props.hasBorder && `border: ${props.hasBorder}`};
    ${props.absolute && `position: absolute;`};
    ${props.relative && `position: relative;`};
    ${props.pointer && `cursor: pointer;`};
    ${props.margBtm && `marginBottom: ${props.margBtm}`};

    ${props.individualRadius && `
      border-top-left-radius: ${props.individualRadius[0]};
      border-top-right-radius: ${props.individualRadius[1]};
      border-bottom-right-radius: ${props.individualRadius[2]};
      border-bottom-left-radius: ${props.individualRadius[3]};
    `}

    ${props.topDistance && `top: ${props.topDistance};`};
    ${props.bottomDistance && `bottom: ${props.bottomDistance};`};
    ${props.rightDistance && `right: ${props.rightDistance};`};
    ${props.leftDistance && `left: ${props.leftDistance};`};
  `}
`

export default Col
