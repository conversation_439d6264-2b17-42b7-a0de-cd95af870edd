import React from 'react'
import { Dimensions, FlatList } from 'react-native'
import styled from 'styled-components'
import { Col, Row, Text, Clickable } from 'components'
import { useNavigation } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'

const Image = styled.Image`
  height: ${props => props.size}px;
  width: ${props => props.size}px;
  margin: 0 0 4px 0;
`

const PostsGrid = ({ data, title, profilePost }) => {
  const imageWidth = Dimensions.get('window').width / 2 - 2,
    navigation = useNavigation(),
    { t } = useTranslation()

  return (
    <Col>
      <Text marg='24px 0 12px 16px' h2>
        {profilePost ? t('home.myPosts') : title}
      </Text>

      <Row wrap between>
        {data.map((x, index) => {
          return (
            <Clickable
              key={index}
              onClick={() => navigation.navigate('PostsFeed', { data, title, index })}
            >
              <Image
                size={imageWidth}
                source={{
                  uri: post?.metaData
                    ? post.metaData.image || post.metaData.icon || ''
                      : post?.youtubeId
                        ? `https://img.youtube.com/vi/${post.youtubeId}/0.jpg`
                        : post?.media?.length
                          ? post.media[0]?.thumbnail || post.item.oldMedia[0]
                          : null
                }}
              />
            </Clickable>
          )
        })}
      </Row>
    </Col>
  )
}

export default PostsGrid
