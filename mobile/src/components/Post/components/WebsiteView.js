import React from 'react'
import { Dimensions, Linking } from 'react-native'
import styled from 'styled-components'
import { theme } from 'lib'
import { Col, Text, Icon, Clickable, Row } from 'components'

const ContentImg = styled.ImageBackground`
  flex: 1;
  align-items: center;
  justify-content: center;
`

const WebsiteView = ({ metaData, isAdd, noText }) => {
  const { title, description, url, image, icon } = metaData,
    deviceWid = Dimensions.get('screen').width,
    contentDimms = `${deviceWid - (isAdd ? 32 : 0)}px`

  return (
    <Clickable
      ht={contentDimms}
      wid={contentDimms}
      onClick={() => Linking.openURL(metaData.url)}
    >
      <Col>
        <ContentImg
          source={{ uri: image || icon }}
          resizeMode='cover'
        />

        <Col noFlex pad={!isAdd && '0 16px'}>
          {!noText ? (
            <>
              <Text
                med
                marg='12px 0 0'
                numberOfLines={1}
              >
                {title}
              </Text>

              <Text
                marg='4px 0 0'
                numberOfLines={1}
                c1
              >
                {description}
              </Text>
            </>
          ) : null}

          <Row noFlex marg='4px 0 0' center>
            <Text c1 numberOfLines={1} maxWid={!noText ? '200px' : '95%'}>
              {url}
            </Text>

            <Icon
              type='link'
              col={theme.GREY_60}
              marg='0 0 0 4px'
              dimensions={16}
            />
          </Row>
        </Col>
      </Col>
    </Clickable>
  )
}

export default WebsiteView
