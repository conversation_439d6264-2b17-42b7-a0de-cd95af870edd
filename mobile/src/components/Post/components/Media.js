import React, { useState } from 'react'
import mime from 'mime'
import { Video, Youtube, Slider, WebsiteView } from './'
import { Col } from 'components'

/*
  This component contains regular post items,
  Images & Videos for the FlatList view
*/
const Media = ({
  item,
  deviceWidth,
  hideReactions,
  scrollPosition,
  position,
  index,
  screenFocused,
  isInView,
  pageNum,
  setPageNum,
  ...props
}) => {
  const [paused, setPaused] = useState(true),
    { youtubeId, media, oldMedia, metaData, aspectRatio } = item,
    firstMedia = item.media && media[0],
    isVideo = mime.getType(firstMedia?.uri)?.includes('video')
      || firstMedia?.uri?.includes('m3u8'),
    isNewData = media?.length && typeof media[0] === 'object'

  // useEffect(() => {
  //   if(!item) return
  //   if(!isVideo && !youtubeId) return
  //   if(!screenFocused) return setPaused(true)
  //   if(!position && index === 0) {
  //     setTimeout(() => setPaused(false), 100)
  //     return
  //   }
  //   if(!position) return
  //
  //   if(scrollPosition > position.start && scrollPosition < position.end) {
  //     return setPaused(false)
  //   }
  //
  //   setPaused(true)
  // }, [scrollPosition, screenFocused])


  if(metaData) {
    return (
      <Col center noFlex marg='8px 0 0'>
        <WebsiteView metaData={metaData} />
      </Col>
    )
  }

  if(youtubeId) {
    return (
      <Youtube
        youtubeId={youtubeId}
        paused={!paused}
        position={position}
        isInView={isInView}
      />
    )
  }

  if(isVideo) {
    return (
      <Video
        src={firstMedia}
        aspectRatio={aspectRatio}
        isNewData={isNewData}
        marg='12px 0 0'
        deviceWidth={deviceWidth}
        paused={!isInView}
        position={position}
      />
    )
  }

  return (
    <Slider
      media={media}
      aspectRatio={aspectRatio}
      oldMedia={oldMedia}
      isNewData={isNewData}
      deviceWidth={deviceWidth}
      hideReactions={hideReactions}
      pageNum={pageNum}
      setPageNum={setPageNum}
      style={props.style}
    />
  )
}

export default Media
