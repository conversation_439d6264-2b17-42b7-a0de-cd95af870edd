import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Platform } from 'react-native'
import { useActionSheet } from '@expo/react-native-action-sheet'
import { useDispatch, useSelector } from 'react-redux'
import { Row, Col, Text, Modal, Icon, Clickable, ReportModal } from 'components'
import { theme, Api } from 'lib'

import { EditModal } from './'

const Item = ({ title, icon, onClick, closeModal }) => {
  function handleClick(){
    // closeModal()

    setTimeout(() => {
      onClick()
    }, 350)
  }

  return (
    <Clickable
      marg='0 0 4px'
      onClick={handleClick}
    >
      <Row noFlex pad='12px 16px' center>
        <Row noFlex ht='24px' wid='24px' centerAll>
          <Icon type={icon} col={theme.GREY_60} dimensions={18} />
        </Row>

        <Text marg='0 0 0 8px' med>
          {title}
        </Text>
      </Row>
    </Clickable>
  )
}

const Menu = ({ item }) => {
  const [menuVisible, setMenuVisible] = useState(false),
    [showScreen, setShowScreen] = useState(null),
    { showActionSheetWithOptions } = useActionSheet(),
    user = useSelector(state => state.user),
    dispatch = useDispatch(),
    { t } = useTranslation(),
    isAndroid = Platform.OS === 'android'

  const ownerOptions = [
    {
      title: t('post.editPost'),
      onClick: showEdit,
      icon: 'edit'
    },
    {
      title: t('post.deletePost'),
      onClick: handleRemove,
      icon: 'delete'
    }
  ]

  const publicOptions = [
    {
      title: t('post.reportPost'),
      onClick: showReport,
      icon: 'block'
    }
  ]

  function closeModal() {
    setMenuVisible(false)
  }

  function showEdit() {
    closeModal()
    setTimeout(() => setShowScreen('edit'), 350)
  }

  function showReport() {
    closeModal()
    setTimeout(() => setShowScreen('report'), 350)
  }

  function handleRemove() {
    isAndroid && setMenuVisible(false)
    
    showActionSheetWithOptions({
      options: [t('post.deletePost'), t('common.cancel')],
      destructiveButtonIndex: [0],
      cancelButtonIndex: 1,
      title: t('post.areYouSure'),
    }, (buttonIndex) => {
      switch (buttonIndex) {
        case 0:
          return removePost()
      }
    })
  }

  async function removePost() {
    const res = Api.post('/posts/remove', { postId: item._id }, 'remove')
    if(!res) return

    closeModal()
    setTimeout(() => {
      dispatch({
        type: 'REMOVE_POST',
        payload: item._id
      })
    }, 350)
  }

  if(!user) return null

  return (
    <React.Fragment>
      <Clickable
        pad='0 12px'
        hitSlop={20}
        onClick={() => setMenuVisible(!menuVisible)}
      >
        <Icon
          type='menu'
          col={theme.GREY_50}
          dimensions={16}
        />
      </Clickable>

      <Modal
        visible={menuVisible}
        closeModal={closeModal}
      >
        {item.owner._id !== user._id && publicOptions.map((x, i) => (
          <Item
            {...x}
            key={i}
            closeModal={closeModal}
          />
        ))}

        {item.owner._id === user._id && ownerOptions.map((x, i) => (
          <Item
            {...x}
            key={i}
            closeModal={closeModal}
          />
        ))}
      </Modal>

      {showScreen === 'edit' && <EditModal
        visible={showScreen === 'edit'}
        closeModal={() => setShowScreen(null)}
        item={item}
      />}

      <ReportModal
        entityType='post'
        item={item}
        visible={showScreen === 'report'}
        closeModal={() => setShowScreen(null)}
      />
    </React.Fragment>
  )
}

export default Menu
