import React, { useState } from 'react'
import { Share, View } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { Icon, Modal, Row, Col, Text, Clickable, Divider } from 'components'
import { Api, theme } from 'lib'
import { useTranslation } from 'react-i18next'

const ShareModal = ({ visible, setVisible, item, isLoggedIn, onHandleTriggerPromptModal }) => {
  const { t } = useTranslation(),
    navigation = useNavigation(),
    link = item.dynamicLink || `https://playaz4playaz.com/post/${item._id}`,
    emailSubject = 'Check out this post on P4P'

  function repost() {
    setVisible(false)

    if (!isLoggedIn) {
      setTimeout(() => {
        onHandleTriggerPromptModal()
      }, 500);
      return;
    }

    setTimeout(() => navigation.navigate('Repost', { post: item }), 300)
  }

  async function share() {
    await Share.share({ message: link })
    setVisible(false)
  }

  return (
    <View style={{ flex: 1, position: 'absolute' }}>
      <Modal visible={visible} closeModal={() => setVisible(false)}>
        <Col noFlex>
          <Clickable marg="4px 0 0" onClick={repost}>
            <Row noFlex ht="48px" pad="0 16px" center wid="100%">
              <Icon type="repost" dimensions={20} col={theme.GREY_60} />
              <Text marg="0 0 0 10px" med>
                {t('common.repost')}
              </Text>
            </Row>
          </Clickable>

          <Divider />

          <Clickable marg="4px 0 0" onClick={share}>
            <Row noFlex ht="48px" pad="0 16px" center wid="100%">
              <Icon type="share" dimensions={20} col={theme.GREY_60} />
              <Text marg="0 0 0 10px" med>
                {t('common.share')}
              </Text>
            </Row>
          </Clickable>
        </Col>
      </Modal>
    </View>
  );
}

export default ShareModal
