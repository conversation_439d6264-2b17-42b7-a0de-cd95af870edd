import React, { useState, useEffect } from 'react'
import { FlatList, Dimensions, Image } from 'react-native'
import styled from 'styled-components'
import { theme, getAspectRatio } from 'lib'
import { Text, Col, Row, Clickable } from 'components'

import { PostItem } from './'

const SliderCountContainer = styled.View`
  width: 45px;
  height: 25px;
  border-radius: 20px;
  position: absolute;
  background-color: rgba(1, 1, 1, 0.4);
  top: 25px;
  align-items: center;
  justify-content: center;
  right: 16px;
  z-index: 1;
`

const MediaSlider = ({
  media,
  oldMedia,
  hideReactions,
  deviceWidth,
  isNewData,
  aspectRatio = 1,
  pageNum,
  setPageNum,
  ...props
}) => {
  const isMultiple = media?.length > 1

  function handleSwipe(e) {
    const contentOffset = e.nativeEvent.contentOffset
    const viewSize = e.nativeEvent.layoutMeasurement

    const pageNum = Math.round(contentOffset.x / viewSize.width)

    setPageNum(pageNum + 1)
  }


  if(media?.length && !media[0].uri) return null

  return (
    <Col noFlex>
      {isMultiple && (
        <SliderCountContainer>
          <Row centerAll>
            <Text c1 col='#fff' marg='0 2px 0 0'>{pageNum}</Text>
            <Text c1 col='#fff' marg='0 2px 0 0'>/</Text>
            <Text c1 col='#fff'>{media.length}</Text>
          </Row>
        </SliderCountContainer>
      )}

      {isMultiple ? (
        <FlatList
          data={media}
          horizontal
          pagingEnabled
          scrollEnabled={isMultiple}
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item, idx) => idx.toString()}
          onScrollBeginDrag={hideReactions && hideReactions}
          onMomentumScrollEnd={handleSwipe}
          renderItem={({ item, index }) => (
            <Image
              source={{ uri: isNewData ? item.uri : oldMedia[index] }}
              style={{
                width: deviceWidth,
                marginTop: 12,
                height: undefined,
                aspectRatio
              }}
            />
          )}
        />
      ) : (
        <Clickable
          onClick={hideReactions}
          activeOpacity={1}
        >
          <Image
            source={{ uri: isNewData ? media[0].uri : oldMedia[0] }}
            style={{
              width: deviceWidth,
              marginTop: 12,
              height: undefined,
              aspectRatio,
              ...props.style
            }}
          />
        </Clickable>
      )}
    </Col>
  )
}

export default MediaSlider
