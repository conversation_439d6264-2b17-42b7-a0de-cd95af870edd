import React, { useEffect, useState } from 'react'
import { theme } from 'lib'
import { useNavigation } from '@react-navigation/native'
import { useTranslation } from 'react-i18next'
import { Gesture, GestureDetector } from 'react-native-gesture-handler'
import { Clickable, Text, Row, Col, Reactions, ExpandableText } from 'components'
 
const Caption = ({ post, isTextPost, handleClick, hideCaption, isShared, isInView, isIOS, isFromPreview = false}) => {

  const { reactionCounts, comments, commentsCount, calculatedText, viewCount, views } = post,
    caption = !hideCaption && post.caption,
    navigation = useNavigation(),
    { t } = useTranslation(),
    [displayViewCount, setDisplayViewCount] = useState(0)

  useEffect(() => {
    if (viewCount && displayViewCount < viewCount) {
      setDisplayViewCount(viewCount)
    } else if (views && views?.length > 0 && (displayViewCount < views?.length)) {
      setDisplayViewCount(views?.length)
    }
  }, [viewCount, views?.length]);

  const handleSingleTap = Gesture.Tap()
    .runOnJS(true)
    .onEnd((_event, success) => {
      if (isIOS && success && !isFromPreview) {
        navigation.navigate('Comments', { post, autoFocus: false })
      }
    });

  const TextCaption = () => {
    return (
      <ExpandableText
        originalText={caption}
        calculatedText={calculatedText}
        handleClick={handleClick}
        include={{
          mention: post.mentioned?.map(x => x ? `@${x?.username}` : null)
        }}
        numberOfLines={isInView ? 2 : null}
      />
    )
  }

  return (
    <Col noFlex={isTextPost} pad='0 16px' marg={caption ? '12px 0 0' : '8px 0 0'} zIndex={-1}>
      {!!caption && (
        isIOS ?
          <GestureDetector gesture={handleSingleTap}>
            <TextCaption />
          </GestureDetector> :
          <TextCaption />
      )}

      <Row wid='100%' noFlex between center marg={isShared ? '40px 0 0' : '8px 0 0'}>
        <Row noFlex center>
          <Reactions entity={reactionCounts} />
          <Text c1 med col={displayViewCount ? null : theme.GREY_60} zIndex={-1} marginLeft={"15px"}>{t('dashboard.views')} {displayViewCount}</Text>
        </Row>
        {
          commentsCount === 0 &&
          <Clickable onClick={() => {
            if(!isFromPreview){
              navigation.navigate('Comments', { post, autoFocus: false })
            }
          }}>
            <Row endAll noFlex>
              <Text c1 col={theme.GREY_60} marg='0 0 0 4px'>
                {t('comments.noComments')}
              </Text>
            </Row>
          </Clickable>
        }
      </Row>
    </Col>
  )
}

export default Caption
