import React, { useState, useEffect, useCallback } from 'react'
import styled from 'styled-components'
import YoutubePlayer from "react-native-youtube-iframe";
import { View, StyleSheet } from 'react-native';
import { Spinner } from 'components'

const ThumbBg = styled.ImageBackground`
  height: 100%;
  width: 100%;
  margin: 0 0 4px 0;
  background-color: #000;
  margin: 8px 0 0;
  align-items: center;
  justify-content: center;
`

const Youtube = ({ youtubeId, paused, isInView }) => {
  const [playing, setPlaying] = useState(false);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    if (isInView && !paused) {
      setPlaying(true);
    } else if (!isInView) {
      setPlaying(false);
    }
  }, [isInView, paused]);

  const onReady = useCallback(() => {
    setIsReady(true);
  }, []);

  const onStateChange = useCallback((state) => {
    if (state === 'ended' || !isInView) {
      setPlaying(false);
    }
  }, [isInView]);

  const thumbnailUrl = `https://img.youtube.com/vi/${youtubeId}/maxresdefault.jpg`;

  return (
    <View style={styles.container}>
      <View style={{ width: '100%', aspectRatio: 16 / 9, position: 'relative', marginTop: 10 }} renderToHardwareTextureAndroid>
        <YoutubePlayer
          videoId={youtubeId}
          height={'100%'}
          width={'100%'}
          play={playing && !paused && isInView}
          onReady={onReady}
          onChangeState={onStateChange}
          webViewStyle={{
            opacity: isReady ? 1 : 0,
            backgroundColor: 'transparent',
          }}
          webViewProps={{
            injectedJavaScript: `
                true;
              `,
            // Android specific optimizations
            androidHardwareAccelerationDisabled: false,
            androidLayerType: 'hardware',
            cacheEnabled: true,
            domStorageEnabled: true,
            javaScriptEnabled: true,
            startInLoadingState: false,
          }}
        />


        {!isReady && (
          <ThumbBg
            source={{ uri: thumbnailUrl }}
            resizeMode="cover"
            style={{
              ...StyleSheet.absoluteFillObject
            }}
          >
            <Spinner size={40} />
          </ThumbBg>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    position: 'relative',
  },
});

export default Youtube