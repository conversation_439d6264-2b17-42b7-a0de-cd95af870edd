import React, { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ScrollView, KeyboardAvoidingView } from 'react-native'
import { useDispatch } from 'react-redux'
import styled from 'styled-components'
import { Modal, Row, Clickable, Text, Dropdown, Spinner, Input, Col } from 'components'
import { theme, Api } from 'lib'
import { TagModal } from 'screens/Create/components'

const ImagesWrapper = styled.View`
  width: 96px;
  height: 96px;
  position: relative;
`

const SelectedImage = styled.Image`
  width: 96px;

  height: 96px;
  position: absolute;
  border-radius: 16px;
`

const AndMoreBubble = styled.View`
  width: 35px;
  height: 25px;
  right: -8px;
  top: -8px;
  border-radius: 20px;
  position: absolute;
  background-color: ${theme.GREY_20};
  align-items: center;
  justify-content: center;
  z-index: 1;
`

const EditModal = ({ visible, closeModal, item }) => {
  const { t } = useTranslation(),
    privacyOptions = [
      {
        value: t('settings.anyone'),
        option: 'anyone'
      },
      {
        value: t('settings.friendsOnly'),
        option: 'friends'
      },
      {
        value: t('settings.noOne'),
        option: 'noOne'
      }
    ]

  const {
    youtubeId,
    video,
  } = item,
    [caption, setCaption] = useState(item.caption || ''),
    [privacy, setPrivacy] = useState(privacyOptions.find(x => x.option === item.shareWith) || {}),
    [tagged, setTagged] = useState(item.tagged || []),
    [loading, setLoading] = useState(false),
    [tagVisible, setTagVisible] = useState(false),
    dispatch = useDispatch()

  useEffect(() => {
    getTagged()
  }, [])

  const getTagged = useCallback(async () => {
    if (item._id) {
      const res = await Api.get(`/posts/getTagged/${item._id}`)
      if (!res) return
      setTagged(res)
    }
  }, [item._id, Api, setTagged])

  async function handleSubmit() {
    setLoading(true)
    const res = await Api.post(
      '/posts/edit',
      {
        postId: item._id,
        caption,
        shareWith: privacy.option,
        tagged: tagged.map(x => x._id)
      },
      'navigator'
    )
    if (!res) return setLoading(false)

    dispatch({
      type: 'UPDATE_POST',
      payload: {
        _id: item._id,
        caption,
        shareWith: privacy.option,
        tagged: tagged.map(x => x._id)
      }
    })

    setLoading(false)
    closeModal()
  }

  return (
    <Modal
      fullScreen
      visible={visible}
      closeModal={closeModal}
      title={t('post.editPost')}
      headerRightContent={
        !loading ? (
          <Clickable onClick={handleSubmit} action='editPost'>
            <Text b2 bold>
              {t('common.proceed')}
            </Text>
          </Clickable>
        ) : (
          <Spinner />
        )
      }
    >
      <ScrollView>
        <Col pad='0 16px'>
          <KeyboardAvoidingView behavior='position'>
            <Row marg={youtubeId || video ? '32px 0 0' : '12px 0 0'} noFlex>
              <Row ht='96px'>
                <Input
                  placeholder={t('add.writeCaption')}
                  value={caption}
                  onChangeText={(e) => setCaption(e)}
                  multiline
                  numberOfLines={5}
                  autoFocus
                  ht='96px'
                />
              </Row>
            </Row>

            <Dropdown
              marg='24px 0 0'
              title={t('add.tagPeople')}
              selected={tagged.length && tagged}
              customText={tagged.length && `${tagged.length} people`}
              onClick={() => { setTagVisible(true) }}
            />

            <Dropdown
              marg='24px 0 0'
              title={t('add.shareWith')}
              selected={privacy}
              handleSelect={setPrivacy}
              options={privacyOptions}
            />

            <TagModal
              visible={tagVisible}
              closeModal={() => setTagVisible(false)}
              tagged={tagged}
              handleSelect={setTagged}
              postId={item._id}
            />
          </KeyboardAvoidingView>
        </Col>
      </ScrollView>
    </Modal>
  )
}

export default EditModal
