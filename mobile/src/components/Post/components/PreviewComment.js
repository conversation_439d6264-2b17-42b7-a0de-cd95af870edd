import React, { useRef } from 'react'
import { useNavigation } from '@react-navigation/native'
import { FlatList } from 'react-native'
import { useTranslation } from 'react-i18next'
import { theme } from 'lib'

import { Text, Col, Clickable, Row } from 'components'
import { Item } from '../../../screens/Comments/components'

const PreviewComment = ({ commentsPreview, show, post, isLoggedIn = true }) => {
  const { reactionCounts, commentsCount, calculatedText } = post,
    navigation = useNavigation(),
    { t } = useTranslation(),
    listRef = useRef()

  function getCommentsText(commentsCount) {
    switch (commentsCount) {
      case 0:
        return t('comments.noComments')
      case 1:
        return t('comments.comment')
      default:
        return t('comments.comments')
    }
  }

  if (!show || commentsCount < 1) return null

  return (
    <Col marg='8px 0'>
      <FlatList
        ref={listRef}
        contentContainerStyle={{ flexGrow: 0, flex: 0 }}
        data={commentsPreview}
        keyExtractor={(item) => item._id}
        renderItem={(props) => (
          <Item
            {...props}
            previewComment
            post={post}
            isLoggedIn={isLoggedIn}
          />
        )}
      />

      <Clickable onClick={() => navigation.navigate('Comments', { post, autoFocus: false, commentsPreview })}>
        <Row pad='4px 16px' noFlex>
          <Text c1 bold col={theme.GREY_60}>
            View all {!!commentsCount && post.commentsCount} {getCommentsText(commentsCount)}
          </Text>
        </Row>
      </Clickable>
    </Col>
  )
}

export default PreviewComment
