import React from 'react'
import { Dimensions, Platform } from 'react-native'
import Video from 'react-native-video'
import { Col, Text } from 'components'

const VideoView = ({ src, marg, aspectRatio, paused }) => {
  const isAndroid = Platform.OS === 'android'

  return (
    <Col
      noFlex
      bg='#000'
      marg={marg}
    >
      <Video
        resizeMode='cover'
        source={src}
        style={{ flex: 1, aspectRatio: aspectRatio || 1 }}
        paused={paused}
        controls={!isAndroid}
        // onBuffer={e => console.log(e)}
        repeat
      />
    </Col>
  )
}

export default VideoView
