import React, { useState, useEffect } from 'react'
import { useNavigation } from '@react-navigation/native'
import { View } from 'react-native'
import styled from 'styled-components'
import { Api, theme } from 'lib'
import { Col, Row, Icon, Text, Clickable, Avatar, Modal, Divider, Spinner } from 'components'

const ButtonWrapper = styled.View`
  position: absolute;
  bottom: ${props => props.isMetaData ? 90 : 16}px;
  left: 16px;
`

const Tagged = ({ data, isMetaData, isLoggedIn = true }) => {
  const [visible, setVisible] = useState(false),
    [tagged, setTagged] = useState([]),
    navigation = useNavigation()

  useEffect(() => {
    if (!visible || tagged.length) return

    setTimeout(() => getTagged(), 300)
  }, [visible])

  function handleVisible() {
    setVisible(!visible)
  }

  async function getTagged() {
    const endpoint = isLoggedIn ? '/posts/getTagged/' : '/public/posts/getTagged/'
    const res = await Api.get(`${endpoint}${data._id}`)
    if (!res) return

    setTagged(res)
  }

  function handleNavigation(user) {
    handleVisible()
    setTimeout(() => {
      navigation.navigate('StackProfile', user)
    }, 300)
  }

  return (
    <React.Fragment>
      <ButtonWrapper isMetaData={isMetaData}>
        <Clickable onClick={handleVisible}>
          <Col
            noFlex
            wid='32px'
            ht='32px'
            hasRadius='32px'
            bg='rgba(210, 216, 223, 0.7)'
            centerAll
          >
            <Icon type='profileOutline' dimensions={20} />
          </Col>
        </Clickable>
      </ButtonWrapper>

      <View style={{ flex: 1, position: 'absolute' }}>
        <Modal visible={visible} closeModal={handleVisible}>
          <Col noFlex marg='4px 0 0' pad='0 16px'>
            <Text bold align='center'>Tagged in this post</Text>
            <Divider marg='8px 0' />

            <Col centerAll noFlex minHt='60px'>
              {!tagged.length ? (
                <Spinner />
              ) : (
                tagged.map((x, i) => (
                  <Clickable
                    wid='100%'
                    key={i}
                    onClick={() => handleNavigation(x)}
                  >
                    <Row noFlex useStart marg='0 0 4px'>
                      <Avatar user={x} />

                      <Row center marg='0 0 0 12px'>
                        <Col>
                          <Text
                            bold
                            numberOfLines={1}
                          >
                            {x.name}
                          </Text>

                          {!!x.bio && (
                            <Text
                              b2
                              col={theme.GREY_60}
                              numberOfLines={2}
                              marg='2px 0 0'
                            >
                              {x.bio}
                            </Text>
                          )}
                        </Col>
                      </Row>
                    </Row>
                  </Clickable>
                ))
              )}
            </Col>
          </Col>

        </Modal>
      </View>
    </React.Fragment>
  )
}

export default Tagged
