import React, { useState, memo, useRef, useMemo, useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Dimensions, Linking, Platform, TouchableOpacity, View } from 'react-native'
import { useNavigation, useRoute} from '@react-navigation/native'
import AnimatedDotsCarousel from 'react-native-animated-dots-carousel'
import { scaleToScreen } from 'core'
import { normalizeDate, theme, Api } from 'lib'
import { Col, Row, Icon, Text, Avatar, Clickable, ReactToPost, Spinner, ExpandableText } from 'components'
import { useTranslation, Trans } from 'react-i18next'
import { Caption, ShareModal, Tagged, Menu, PreviewComment } from './components'
import PinchableMedia from '../PinchableMedia'
import { Gesture, GestureDetector } from 'react-native-gesture-handler'
import useUpdateViewsCount from '../../screens/Hooks/useUpdateViewsCount'
import { useNavigationState } from '@react-navigation/native';

const ContentText = memo(({ item, onClickCaption, onDoubleTap, isInView, isIOS, navigation, isFromPreview = false }) => {
  const updateViewsCount = useUpdateViewsCount();
  const currentUserId = useSelector(state => state.user._id)

  const handleSingleTap = Gesture.Tap()
    .runOnJS(true)
    .onEnd((_event, success) => {
      if (success && !isFromPreview) {
        handleViewCount(item?.owner?._id, currentUserId, 'text', item?._id)
        navigation.navigate('Comments', { post: item, autoFocus: false })
      }
    });

  const handleDoubleTap = Gesture.Tap()
    .runOnJS(true)
    .numberOfTaps(2)
    .onEnd((_event, success) => {
      if (success) {
        onDoubleTap()
      }
    });

  const handleViewCount = async (userId, currentUserId, trigger, postId = null) => {
    await updateViewsCount(userId, currentUserId, trigger, postId);
  }

  const composedGestures = isIOS && isInView ?
    Gesture.Exclusive(handleDoubleTap, handleSingleTap) :
    Gesture.Exclusive(handleDoubleTap)

  return item && (
    <GestureDetector gesture={composedGestures}>
      <Col noFlex pad='16px 16px 0'>
        <ExpandableText
          originalText={item.caption}
          calculatedText={item.calculatedText}
          handleClick={onClickCaption}
          numberOfLines={isInView ? 2 : null}
          include={{
            mention: item.mentioned.map(x => x ? `@${x.username}` : null)
          }}
        />
      </Col>
    </GestureDetector>
  )
})

const Post = memo(({
  id,
  ownerData,
  showReactions,
  hideReactions,
  marg,
  scrollPosition,
  index,
  screenFocused,
  isInView,
  showPreviewComment,
  isReacting,
  isShared,
  isFromPreview = false,
  setOtherUser,
  isCurrentUser,
  isFromCommentScreen = false, 
  setOtherUsersData = () => {}, 
  didUpdateProfileCount = () => { },
  setIsPromptModalVisible,
  homeItem = [],
}) => {
  const [pageNum, setPageNum] = useState(1),
    [reactionsVisible, setReactionsVisible] = useState(false),
    [commentsVisible, setCommentsVisible] = useState(false),
    [followDelay, setFollowDelay] = useState(false),
    [showShare, setShowShare] = useState(false),
    [position, setPosition] = useState(null),
    item = useSelector(state => state.posts)?.find(x => x?._id === id)
    user = useSelector(state => state.user) || {},
    deviceWidth = Dimensions.get('screen').width,
    windowHeight = Dimensions.get('window').height,
    { t } = useTranslation(),
    navigation = useNavigation(),
    dispatch = useDispatch(),
    componentRef = useRef(),
    { scale, verticalScale, moderateScale, screenSize } = scaleToScreen,
    pinchedPostId = useSelector(state => state.appState.pinchedPost?.id),
    isPinchingPost = useSelector(state => state.appState.pinchedPost?.isPinchingPost),
    isIOS = Platform.OS === 'ios',
    isLoggedIn = !!user?._id;

  const reactionLoadingRef = useRef(false)

  const isUserOwner = user?._id === (item?.owner?._id || item?.owner)

  const owner = ownerData ? ownerData : isUserOwner ? user : item?.owner

  const isTextPost = !item?.metaData && !item?.media?.length && !item?.oldMedia?.length && !item?.youtubeId

  const prevReaction = user.reactions?.find(x => x.entityId === item?._id)?.reaction
  
  const [isPinching, setIsPinching] = useState(false)
  
  const updateViewsCount = useUpdateViewsCount();

  const currentRouteName = useRoute().name;

  let following = user.following?.includes(owner?._id)

  const follow = useCallback(async () => {
    if (followDelay) return

    setFollowDelay(true)
    const res = await Api.post('/users/follow', { userId: owner?._id }, 'follow')
    if (!res) return

    setFollowDelay(false)
    dispatch({
      type: 'FOLLOW',
      payload: owner?._id
    })

    dispatch({
      type: 'HANDLE_RES',
      payload: { res: t('profile.nowFollowing', { name: owner?.name }) }
    })

  }, [
    followDelay,
    owner?.type,
    navigation,
    owner,
    owner?._id,
    owner?.name
  ])

  const handleViewCount = async (userId, currentUserId, trigger, postId = null) => {
    return await updateViewsCount(userId, currentUserId, trigger, postId);
  }

  const handleTriggerPromptModal = () => {
    if (isLoggedIn) return;

    // handle modals stacking issue of iOS, preventing this modal to render twice
    if (setIsPromptModalVisible && typeof setIsPromptModalVisible === 'function') {
      setIsPromptModalVisible(true);
    } else {
      dispatch({
        type: 'SHOW_PROMPT_MODAL',
        payload: {
          isPromptModalVisible: true,
        },
      });
    }
  };

  const react = useCallback(async (reaction) => {
    if (!isLoggedIn) {
      handleTriggerPromptModal();
      return;
    }
   
    if (reactionLoadingRef.current) return
    reactionLoadingRef.current = true;
    setReactionsVisible(false)

    const data = {
      reaction,
      prevReaction,
      entityId: item?._id,
      entityType: 'post',
      userId: user._id
    }

    dispatch({
      type: 'ADD_REACTION_TO_POST',
      payload: data
    })

    if (!prevReaction || (prevReaction !== reaction)) {
      const res = await handleViewCount(item?.owner?._id, user?._id, 'react', item?._id)
        .catch(err => {
          console.error('error:', err);
        });

      isFromCommentScreen ?  setOtherUsersData(res?.user?.newCount) : didUpdateProfileCount(res?.user?.newCount)
      
    }

    hideReactions()

    await Api.post(
      '/users/react',
      data
    )
    reactionLoadingRef.current = false;
  }, [
    prevReaction,
    item?._id,
    user._id,
  ])

  const handleReactionsVisible = useCallback(() => {
    handleReactionsVisible(!reactionsVisible)
  }, [reactionsVisible])

  const handleClickCaption = useCallback(async (link, type) => {
    let res

    switch (type) {
      case 'mention':
        const user = item?.mentioned?.find(x => x.username === link.slice(1))
        if (!user) return

        return navigation.push('StackProfile', user)

      case 'tag':
        res = await Api.post('/posts/get', { tag: link }, 'navigator')
        if (!res) return

        return navigation.push('PostsFeed', { data: res, title: link })

      case 'url':
        return Linking.openURL(link)
    }
  }, [
    item?.mentioned,
    navigation,
    user,
  ])

  const avatar = useMemo(() => (
    <View style={{ marginRight: 8 }}>
      <Avatar
        small
        user={owner}
      />
    </View>
  ), [
    owner
  ])

  const postHeaderDetails = useMemo(() => {
    return (
      <View style={{ flex: 1 }}>
        {item?.originalOwner ? (
          <Trans
            parent={({ children }) => (
              <Text b2 col={theme.GREY_60}>{children}</Text>
            )}
            components={{
              strong: <Text b2 bold />
            }}
          >
            {t('profile.repostHeader', { owner: owner?.name, originalOwner: item?.originalOwner.name })}
          </Trans>
        ) :
          <Text b2 bold marg='0 0 2px'>{owner?.name}</Text>}

        {item?.createdAt && <Text c1 med col={theme.GREY_60}>
          {normalizeDate(new Date(item?.createdAt), 'Default', t)}
        </Text>}
      </View>
    )
  }, [
    t,
    theme,
    item?.originalOwner,
    item?.originalOwner?.name,
    owner?.name,
    normalizeDate,
    item?.createdAt,
  ])

  const navigateToProfile = useCallback(() => {
    if(isFromPreview) return;
    navigation.push('StackProfile', owner)
  }, [
    navigation,
    owner,
  ])

  const header = useMemo(() => {
    return (
      <View style={{ flexDirection: 'row', flex: 1 }}>
        <TouchableOpacity
          style={{ flexDirection: 'row', marginHorizontal: 10, flex: 1 }}
          onPress={navigateToProfile}
        >
          {avatar}
          {postHeaderDetails}
        </TouchableOpacity>

        {(!isFromPreview && isLoggedIn) &&  <Menu item={item} />}
      </View>
    )
  }, [
    avatar,
    postHeaderDetails,
    item,
    navigateToProfile,
  ])

  const contentMedia = useMemo(() => {
    return (
      <Col noFlex minHt='200px'>
        <PinchableMedia
          item={item}
          hideReactions={hideReactions}
          deviceWidth={deviceWidth}
          position={position}
          scrollPosition={scrollPosition}
          index={index}
          screenFocused={screenFocused}
          isInView={isInView || isFromPreview}
          pageNum={pageNum}
          setPageNum={setPageNum}
          onDoubleTap={navigateToProfile}
          onPinch={isPinching => { setIsPinching(isPinching) }}
          isCurrentUser={isCurrentUser}
          didUpdateProfileCount={didUpdateProfileCount}
          displayPreview={true}
        />

        {!!item?.tagged?.length && !isPinching && <Tagged data={item} isMetaData={item?.metaData} isLoggedIn={isLoggedIn} />}
      </Col>
    )
  }, [
    item,
    hideReactions,
    deviceWidth,
    position,
    scrollPosition,
    index,
    screenFocused,
    isInView,
    pageNum,
    setPageNum,
    item?.tagged,
    item?.metaData,
    isPinching
  ])

  const content = useMemo(() => {
    return (
      <View style={{ zIndex: 999 }}>
        {isTextPost ?
          <ContentText
            item={item}
            onClickCaption={handleClickCaption}
            onDoubleTap={navigateToProfile}
            isInView={isIOS && showPreviewComment}
            isIOS={isIOS}
            navigation={navigation}
            isFromPreview={isFromPreview}
          /> :
          contentMedia
        }
      </View>
    )
  }, [
    isTextPost,
    contentMedia,
    item,
    handleClickCaption,
    navigateToProfile
  ])

  const onStartResponder = useCallback(() => {
    hideReactions()
  }, [
    hideReactions
  ])

  const animatedDots = useMemo(() => {
    return (
      item?.media?.length > 1 && (
        <Row centerAll ht='24px' marg='0 0 -24px'>
          <Row
            noFlex
            wid={`${verticalScale(
              // 14 is margin*2 + size, so the size of a normal dot,
              // 96 is the biggest size it can get if there's more than 5 items
              item?.media.length < 5
                ? item?.media.length * 14
                : 96
            )
              }px`}
          >
            <AnimatedDotsCarousel
              currentIndex={pageNum - 1}
              maxIndicators={4}
              activeIndicatorConfig={{
                color: theme.SECONDARY,
                margin: verticalScale(4),
                opacity: 1,
                size: verticalScale(6),
              }}
              inactiveIndicatorConfig={{
                color: theme.GREY_20,
                margin: verticalScale(4),
                opacity: 1,
                size: verticalScale(6),
              }}
              decreasingDots={[
                {
                  config: { color: theme.GREY_20, margin: verticalScale(3), opacity: 1, size: verticalScale(4) },
                  quantity: 1,
                }, {
                  config: { color: 'transparent', margin: verticalScale(5), opacity: 0, size: 0 },
                  quantity: 1,
                }
              ]}
              length={item?.media?.length}
            />
          </Row>
        </Row>
      )
    )
  }, [
    item?.media,
    verticalScale,
    pageNum,
    theme,
  ])

  const userDependentPostActions = useMemo(() => {
    return (

        <React.Fragment>
          <ReactToPost
            post
            reactionsVisible={isReacting}
            handleSubmit={(e) => react(e)}
            reaction={prevReaction}
            showReactions={() => showReactions(item?._id)}
          />

          <Clickable
            pad={`3px ${verticalScale(15)}px 0 ${verticalScale(14)}px`}
            onClick={() => {

              // last screen or post from media share, prevent navigating since we're already in "Comments" screen and show prompt modal when tapped
              if (currentRouteName === 'Comments' || currentRouteName === 'StackProfile') {
                handleTriggerPromptModal();
                return;
              }

              if(!isFromPreview){
                if (!isIOS && isTextPost) {
                  handleViewCount(item?.owner?._id, user?._id, 'text', item?._id)
                }

                navigation.navigate(
                  'Comments',
                  {
                    post: { ...item, owner },
                    autoFocus: true,
                    setOtherUser,
                    isCurrentUser,
                  }
                )
              }
            }}
          >
            <Icon type='addComment' dimensions={verticalScale(20)} col={theme.GREY_50} />
          </Clickable>
        </React.Fragment>
      
    )
  }, [
    user._id,
    isReacting,
    prevReaction,
    showReactions,
    item?._id,
    navigation,
    item,
    owner,
    theme,
    verticalScale
  ])

  const share = useMemo(() => {
    return (
      <Clickable pad={`2px ${verticalScale(14)}px`} onClick={() => setShowShare(true)}>
        <Icon type='share' dimensions={verticalScale(20)} col={theme.GREY_50} />
      </Clickable>
    )
  }, [
    verticalScale,
    setShowShare,
    theme,
  ])

  const followOrSubscribe = useMemo(() => {
    return (
      user._id && (
        <Row endAll pad='0 16px 0 0' center zIndex='-1'>
          {followDelay
            ? (
              <Row noFlex center wid='44px'>
                <Spinner />
              </Row>
            ) : (
              !(following || isUserOwner) &&
              <Clickable onClick={follow}>
                <Text bold b2>
                  {t('common.follow')}
                </Text>
              </Clickable>
            )
          }
        </Row>
      )
    )
  }, [
    user._id,
    followDelay,
    following,
    isUserOwner,
    item?.ownerType,
    t,
    follow,
  ])

  const shareModal = useMemo(() => {
    return (
      <ShareModal
        visible={showShare}
        setVisible={setShowShare}
        item={item}
        isLoggedIn={isLoggedIn}
        onHandleTriggerPromptModal={handleTriggerPromptModal}
      />
    )
  }, [
    showShare,
    item
  ])

  const details = useMemo(() => {
    return (
      <TouchableOpacity style={{ flex: 1 }} activeOpacity={1}>
        <Col marg='13px 0 0'>
          {animatedDots}

          <Row center noFlex ht='24px'>
            <Row noFlex>
              {userDependentPostActions}
              {share}
            </Row>
            {followOrSubscribe}
          </Row>
        </Col>

        <Caption
          isShared={isShared}
          post={item}
          handleClick={handleClickCaption}
          hideCaption={isTextPost}
          isInView={isIOS && showPreviewComment}
          isIOS={isIOS}
          isFromPreview={isFromPreview}
        />

        <PreviewComment
          commentsPreview={item?.commentPreview}
          show={showPreviewComment}
          post={item}
          isLoggedIn={isLoggedIn}
        />
      </TouchableOpacity>
    )
  }, [
    animatedDots,
    userDependentPostActions,
    share,
    followOrSubscribe,
    isShared,
    item,
    handleClickCaption,
    isTextPost,
    item?.commentPreview,
    showPreviewComment
  ])


  if (!item || !item?._id || !owner) return null

  return (
    <View
      onStartShouldSetResponder={onStartResponder}
      style={{
        flex: 1,
        marginBottom: 20,
        zIndex: (isPinching ? 999 : 1),
        opacity: (!isPinchingPost || isPinchingPost && pinchedPostId === item?._id) ? 1 : 0,
        transition: 'opacity 1s ease-out'
      }}
    >
      {header}
      {content}
      {details}
      {shareModal}
    </View>
  )
})

export default Post
