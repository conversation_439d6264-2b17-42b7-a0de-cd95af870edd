import React, { useState, useEffect } from 'react'
import { PixelRatio } from 'react-native'
import { useDispatch, useSelector } from 'react-redux'
import { theme } from 'lib'
import { useNavigation } from '@react-navigation/native'
import { Text, Col, Row, Button, Icon, Dismissable } from 'components'
import { useTranslation } from 'react-i18next'

const VerifyYourProfile = ({ isHome, marginTop = 0, marg }) => {
  const { verifyProfileDismissed } = useSelector(state => state.appState),
    user = useSelector(state => state.user),
    dispatch = useDispatch(),
    { t } = useTranslation(),
    navigation = useNavigation(),
    dismissed = verifyProfileDismissed
      ? verifyProfileDismissed[isHome ? 'home' : 'profile']
      : false

  useEffect(() => {
    dispatch({
      type: 'UPDATE_APP_STATE',
      payload: {
        verifyProfileDismissed: {
          home: verifyProfileDismissed.home,
          profile: false
        }
      }
    })
  }, [])

  if(user.verified?.id && user.verified?.email) return null
  if('pendingVerification' === user.verified?.idProgress) return null
  if(!verifyProfileDismissed) return null
  if(isHome && verifyProfileDismissed?.home) return null
  if(!isHome && verifyProfileDismissed?.profile) return null

  function handleClick() {
    if(!user.verified.email) return navigation.navigate('CheckInbox')
    navigation.navigate('VerifyIdPopup')
  }

  function dismiss() {
    dispatch({
      type: 'UPDATE_APP_STATE',
      payload: {
        verifyProfileDismissed: {
          ...verifyProfileDismissed,
          ...isHome ? { home: true } : { profile: true }
        }
      }
    })
  }

  return (
    <Col
      noFlex
      pad={user.type === 'fan' ? '16px 16px 0' : '24px 16px 0'}
      marg={marg}
    >
      <Col
        pad='16px'
        noFlex
        bg={theme.SECONDARY}
        // ht='154px'
      >
        <Row noFlex>
          <Icon
            col='#fff'
            type='videoCamera'
            marg='0 8px 0 0'
          />

          <Col>
            <Text
              bold
              col='#fff'
            >
              {t('verification.verifyProfile')}
            </Text>

            <Text
              marg='4px 0 0'
              col='#fff'
            >
              {t('verification.needVerification')}
            </Text>
          </Col>
        </Row>

        <Row noFlex marg='12px 0 0'>
          <Row marg='0 8px 0 0'>
            <Button
              text={t('verification.verifyProfile2')}
              onClick={handleClick}
            />
          </Row>

          <Row>
            <Button
              bordered
              reverse
              text={t('common.dismiss')}
              onClick={dismiss}
            />
          </Row>
        </Row>
      </Col>
    </Col>
  )
}

export default VerifyYourProfile
