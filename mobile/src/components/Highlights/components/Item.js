import React, { memo } from 'react'
import { useNavigation } from '@react-navigation/native'
import { theme } from 'lib'
import { Avatar, Clickable, Col, Text, Row } from 'components'

const Item = ({ item, index, data, isAdd }) => {
  const navigation = useNavigation()

  function handleNav() {
    if(isAdd) return navigation.push('Add', { initialRoute: 'Story' })
    if(item.streamKey) return navigation.navigate('Live', { data: item })

    const filteredData = data.filter(x => x.recent),
      idx = filteredData.findIndex((x, i) => {
        return x._id === item._id
      })


    navigation.navigate('Stories', { data: filteredData, startingIndex: idx })
  }

  return (
    <Clickable onClick={handleNav}>
      <Row noFlex wid='80px' centerAll>
        <Col noFlex center>
          <Avatar
            storiesSection
            unseenStory={!item.streamKey}
            streaming={!!item.streamKey}
            addStory={isAdd}
            user={item?.owner || {}}
          />

          <Text
            c1
            med
            marg='4px 0 0'
            col={theme.GREY_60}
            numberOfLines={1}
          >
            {isAdd
              ? 'Your story'
              : `@${item?.owner?.username || ':)'}`}
          </Text>
        </Col>
      </Row>
    </Clickable>
  )
}

export default memo(Item)
