import React, { useState, useEffect, memo } from 'react'
import { useSelector } from 'react-redux'
import { FlatList } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { addHours } from 'date-fns'
import { useTranslation } from 'react-i18next'
import { Col, Text } from 'components'
import { Item } from './components'

const Highlights = memo(({ type, noMarg }) => {
  const [filteredData, setFilteredData] = useState(null),
    [data, setData] = useState(null),
    stories = useSelector(state => state.home?.stories),
    user = useSelector(state => state.user),
    isPinchingPost = useSelector(state => state.appState.pinchedPost?.isPinchingPost),
    { t } = useTranslation(),
    navigation = useNavigation(),
    isLoggedIn = !!user?._id


  useEffect(() => {
    if(!stories?.length) return

    const mapped = stories
      ?.map(x => {
        if(x.streamKey) return x

        const filteredRecents = x.recent.filter(y => {
          const shouldDissapearAt = addHours(new Date(y.createdAt), 48)
          return new Date() < shouldDissapearAt
        })

        if(!filteredRecents.length) return null

        return {
          ...x,
          recents: filteredRecents
        }
      })
      // remove for now to combine all fan & athlete highlights
      // .filter(x => x && x.ownerType === type)
      .filter(x => x !== null)
      .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))

    setData(mapped)

  }, [stories])

  if(!data?.length) return null

  return (
    <Col noFlex marg={!noMarg && '0 0 24px'} style={{ opacity: isPinchingPost ? 0 : 1 }}>
      <Text h2 marg='0 16px' >
        {t(type === 'athlete'
          ? 'home.athleteHighlights'
          : 'home.fanHighlights'
        )}
      </Text>

      <FlatList
        horizontal
        data={data?.filter(x => x.owner._id !== user._id)}
        ListHeaderComponent={() => {
          const userStoryExists = data?.find((x) => x.owner._id === user._id)

          return (
            isLoggedIn && <Item
              item={userStoryExists || { owner: user }}
              index={0}
              isAdd={!userStoryExists}
              isUser
              data={data}
              navigation={navigation}
            />
          )
        }}
        contentContainerStyle={{
          paddingHorizontal: 16,
          paddingTop: 12
        }}
        keyExtractor={(item, index) => index.toString()}
        renderItem={(props) => (
          <Item
            {...props}
            data={data}
            navigation={navigation}
          />
        )}
        ItemSeparatorComponent={() => <Col wid='6px' noFlex/>}
      />
    </Col>
  )
})

export default Highlights
