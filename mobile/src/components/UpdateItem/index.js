import React, { useState, memo, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigation } from '@react-navigation/native'
import { Col, Row, Text, Button, Icon, Clickable } from 'components'
import { theme, capitalize } from 'lib'

const WinnerItem = memo(({ item, dismissed }) => {
  const { type, text, users, sport } = item,
    textRef = useRef(),
    navigation = useNavigation()

  function getIcon() {
    switch (item.type) {
      case 'win':
        return 'throphy'

      case 'achievement':
        return 'notificationOutline'

      default:
        return sport === 'soccer' ? 'football' : sport
    }
  }

  function handleClick() {
    navigation.navigate('StackProfile', users[0])
  }

  return (
    <Clickable
      onClick={handleClick}
    >
      <Row
        wid='320px'
        ht={!dismissed && '68px'}
        bg={theme.SECONDARY}
        centerAll
        noFlex
      >
        <Row pad='16px' noFlex between wid='100%' center>
          <Row noFlex center>
            <Icon
              type={getIcon()}
              dimensions={24}
              col='#fff'
            />

            <Text
              textRef={textRef}
              b2
              med
              col='#fff'
              marg='0 0 0 12px'
              wid='210px'
              numberOfLines={2}
            >
              {text}
            </Text>
          </Row>

          <Icon
            type='rightChevron'
            col='#fff'
            dimensions={20}
          />
        </Row>
      </Row>
    </Clickable>
  )
})

export default WinnerItem
