import React, { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { Calendar, LocaleConfig } from 'react-native-calendars'
import { Icon, Clickable, Row, Text } from 'components'
import { theme, formatLocale } from 'lib'

const CustomCalendar = (props) => {
  const language = useSelector(state => state.appState.language)
  const { minDate, maxDate, isDisabled, onClick, selected, markedDates } = props

  return (
    <Calendar
      {...props}
      minDate={formatLocale(minDate, 'yyyy-MM-dd')}
      maxDate={formatLocale(maxDate, 'yyyy-MM-dd')}
      renderArrow={direction => (
        <Icon
          type={`${direction}Chevron`}
          dimensions={12}
          col={theme.GREY_60}
        />
      )}
      hideExtraDays={true}
      firstDay={1}
      dayComponent={(props) => {
        const { date, state } = props
        const isMarked = markedDates
          && Object.keys(markedDates).includes(formatLocale(new Date(date.dateString), 'yyyy-LL-dd'))
        const disabled = state === 'disabled' || isDisabled(new Date(date.dateString))

        return (
          <Clickable
            disabled={disabled}
            onClick={(e) => onClick(e, new Date(date.dateString))}
          >
            <Row
              noFlex
              wid='40px'
              ht='40px'
              bg={
                selected && formatLocale(selected, 'yyyy-MM-dd') === date.dateString || isMarked
                  ? theme.SECONDARY
                  : theme.GREY_10
              }
              hasRadius='32px'
              centerAll
            >
              <Text
                b1
                med
                col={
                  selected && formatLocale(selected, 'yyyy-MM-dd') === date.dateString || isMarked
                  ? '#fff'
                  : disabled
                  ? theme.GREY_20
                  : '#000'
                }
              >
                {date.day}
              </Text>
            </Row>
          </Clickable>
        )
      }}
    />
  )
}

export default CustomCalendar
