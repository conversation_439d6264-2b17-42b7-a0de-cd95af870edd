import React, { useState, useEffect, memo, useMemo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigation } from '@react-navigation/native'
import styled from 'styled-components'
import { Col, Icon, Text, Clickable } from 'components'
import { theme } from 'lib'
import { subDays } from 'date-fns'

const Circle = styled.View`
  border-radius: 100px;
  border: 2px;
  border-color: ${theme.GREY_10};
  height: ${props => props.big ? '96px' : props.small ? '40px' : '72px'};
  width: ${props => props.big ? '96px' : props.small ? '40px' : '72px'};

  ${props => (props.unseenStory || props.addStory) && `
    border-color: ${theme.SECONDARY};
  `};

  ${props => props.online || props.streaming && `
    border-color: ${theme.GREEN};
  `};

  ${props => props.tiny && `
    height: 24px;
    width: 24px;
    border: 0px;
  `};
`

const Image = styled.ImageBackground`
  background-color: ${props => props.noAvatar ? theme.TALE : '#fff'};
  border-radius: 100px;
  margin: ${props => props.big ? '4px' : '2px'};
  height: ${props => props.big ? '84px' : props.small ? '32px' : '64px'};
  width: ${props => props.big ? '84px' : props.small ? '32px' : '64px'};
  overflow: hidden;
  align-items: center;
  justify-content: center;

  ${props => props.tiny && `
    height: 24px;
    width: 24px;
    margin: 0px;
  `};
`

const IconContainer = styled.View`
  position: absolute;
  align-items: center;
  justify-content: center;
  border-radius: 100px;
  background-color: #fff;
  border: 2px;
  border-color: #fff;

  ${props => props.play && `
    bottom: -10px;
    right: ${props.small ? '24%' : props.big ? '40%' : '36%'};
  `};

  ${props => props.addStory && `
    bottom: 0;
    right: 0;
  `};

  ${props => props.verified && `
    bottom: ${props.big ? -3 : 0}px;
    right: ${props.big ? -3 : 0}px;
    background-color: transparent;
    border: 0;
  `}
`

const Avatar = memo((props) => {
  const { user, streaming, badge, big, small, online, addStory, tiny, showVerified, noClickAndStory = false } = props
  const noAvatar = !props.avatar && !user?.avatar
  const hasStory = user?.lastStoryAt
    && new Date(user.lastStoryAt) >= subDays(new Date(), 1)
  const clickDisabled = props.storiesSection || !hasStory
  const navigation = useNavigation()
  const isVerifiedAthlete = user?.type === 'athlete' && user?.verified?.id

  function getText() {
    const splitted = user?.name?.split(' '),
      initials = splitted && `${splitted[0] && splitted[0][0].toUpperCase()}${splitted[1] && splitted[1][0].toUpperCase()}`.replace('undefined', '')

    switch (true) {
      case !!tiny:
        return <Text c2 bold>{initials}</Text>
      case !!small:
        return <Text c1 bold>{initials}</Text>
      case !!big:
        return <Text h2>{initials}</Text>
      default:
        return <Text h4 bold>{initials}</Text>
    }
  }

  function getVerifiedSize() {
    switch (true) {
      case big:
        return 40
      case tiny:
      case small:
        return 18
      default:
        return 22
    }
  }

  const image = useMemo(() => {
    return (
      <Image
        {...props}
        resizeMode='cover'
        source={{ uri: props.avatar || user?.avatar || null }}
        noAvatar={noAvatar}
        key={props.avatar || user?.avatar}
      >
        {noAvatar && getText()}
      </Image>
    );
  }, [
    props,
    user?.avatar,
    noAvatar,
    getText,
  ])

  if (noClickAndStory) return image;

  return (
    <Clickable
      onClick={() => navigation.push('Stories', { userId: user?._id })}
      ogOpacity={clickDisabled}
      disabled={clickDisabled}
    >
      <Circle
        {...props}
        unseenStory={hasStory || props.unseenStory}
      >
        {image}

        {streaming && (
          <IconContainer play {...props}>
            <Icon type='playCircle' col={theme.GREEN} dimensions={15}/>
          </IconContainer>
        )}

        {addStory && (
          <IconContainer addStory {...props}>
            <Icon type='addCircle' col={theme.SECONDARY} dimensions={15}/>
          </IconContainer>
        )}

        {showVerified && isVerifiedAthlete && (
          <IconContainer verified {...props}>
            <Icon type='verified' col={theme.PURPLE} dimensions={getVerifiedSize()}/>
          </IconContainer>
        )}
      </Circle>
    </Clickable>
  )
})

export default Avatar
