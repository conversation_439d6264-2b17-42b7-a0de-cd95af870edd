import React, { useMemo } from 'react';
import { useNetInfo } from "@react-native-community/netinfo";
import { useTranslation } from 'react-i18next'
import { Platform, SafeAreaView, StatusBar, View } from 'react-native';
import Text from './Text';

const NetworkInfo = () => {
    const { isConnected } = useNetInfo();
    const { t } = useTranslation();

    const styles = useMemo(() => ({
        networkInfoContainer: {
            ...Platform.select({
            android: {
                paddingTop: StatusBar.currentHeight,
            }
            }),
            position: 'absolute',
            zIndex: 1000,
            backgroundColor: '#FB3351',
            width: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            shadowColor: "#000",
            shadowOffset: {
                width: 0,
                height: 5,
            },
            shadowOpacity: 0.2,
            shadowRadius: 5,
            elevation: 8,
        },
        contentContainer: { paddingVertical: 15 },
        textStyle: { color: 'white' },
    }), [])

    if (isConnected) return null;
    return (
      <SafeAreaView style={styles.networkInfoContainer}>
        <View style={styles.contentContainer}>
          <Text style={styles.textStyle}>{t('error.noNetwork')}</Text>
        </View>
      </SafeAreaView>
    );
}

export default NetworkInfo;