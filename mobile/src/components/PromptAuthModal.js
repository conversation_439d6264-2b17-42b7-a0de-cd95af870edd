import { useNavigation } from '@react-navigation/native';

import { View, Modal, TouchableWithoutFeedback, Animated, Dimensions } from 'react-native';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { theme } from 'lib';
import { useDispatch, useSelector } from 'react-redux';
import { isNewIphone } from 'core';
import { useEffect, useRef } from 'react';

// Components
import { Col, Text, Button, Clickable } from 'components';

const Logo = styled.Image`
  height: 32px;
  width: 164px;
`;

const Backdrop = styled.View`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
`;

const ModalContainer = styled.View`
  flex: 1;
  justify-content: flex-end;
`;

const Container = styled.View`
  background-color: #fff;
  overflow: hidden;
`;

const ContentArea = styled.View`
  ${props => !props.ignorePadBottom && isNewIphone() && 'padding-bottom: 30px;'};
  background-color: #fff;
`;

const SwipeCloseContainer = styled.View`
  border-top-left-radius: 34px;
  border-top-right-radius: 34px;
  top: 1px;
  height: 34px;
  background-color: #fff;
  align-items: center;
  justify-content: center;
`;

const CloseBar = styled.View`
  width: 48px;
  height: 5px;
  margin-top: -17px;
  border-radius: 100px;
  background-color: ${theme.GREY_30};
`;

const PromptAuthModal = ({ isVisible = false, onClose, onSelect = () => { }, action = '' }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const isPromptModalVisible = useSelector(state => state.globalModalsState.isPromptModalVisible) || false;
  const navigation = useNavigation();
  const slideAnim = useRef(new Animated.Value(Dimensions.get('window').height)).current;

  const isOnCloseValid = onClose && typeof onClose === 'function';

  useEffect(() => {
    if (isPromptModalVisible || isVisible) {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 65,
        friction: 11,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: Dimensions.get('window').height,
        duration: 250,
        useNativeDriver: true,
      }).start();
    }
  }, [isPromptModalVisible, isVisible]);

  const handleClose = () => {
    dispatch({
      type: 'SHOW_PROMPT_MODAL',
      payload: {
        isPromptModalVisible: false,
      },
    });

    if (isOnCloseValid) {
      onClose();
    }
  };

  const handleNavigateTo = (screen, errorMsg) => {
    try {
      navigation.navigate(screen);
      dispatch({
        type: 'UPDATE_LINK_ACTION',
        payload: action,
      });
      handleClose();
      onSelect();

      if (isOnCloseValid) {
        onClose();
      }
    } catch (err) {
      console.error(errorMsg);
    }
  }

  return (
    <View style={{ position: 'absolute', flex: 1 }}>
      <Modal
        visible={isPromptModalVisible || isVisible}
        transparent={true}
        animationType="none"
        statusBarTranslucent={true}
        onRequestClose={handleClose}
      >
        <Backdrop />
        <TouchableWithoutFeedback onPress={handleClose}>
          <ModalContainer>
            <TouchableWithoutFeedback>
              <Animated.View style={{ transform: [{ translateY: slideAnim }] }}>
                <SwipeCloseContainer>
                  <CloseBar />
                </SwipeCloseContainer>

                <Container>
                  <ContentArea>
                    <Col noFlex pad="26px 32px 40px 32px">
                      <Text h4 align="center" marg="0 0 26px">
                        {t('auth.promptAuthMsg')}
                      </Text>

                      <Button big text={t('common.signUp')} onClick={() => handleNavigateTo('SignUp', 'Failed to navigate to signup screen.')} bg={theme.SECONDARY} />
                      <Button big bordered marg="12px 0 0 0" text={t('common.cancel')} onClick={handleClose} />

                      <View
                        style={{
                          margin: 16,
                          alignItems: 'center',
                        }}>
                        <Logo source={require('assets/branding/logo-black.png')} />
                      </View>

                      <Clickable onClick={() => handleNavigateTo('Login', 'Failed to navigate to login screen.')}>
                        <Text align="center">
                          {t('auth.alreadyHaveAccount')} <Text bold>{t('common.login')}</Text>
                        </Text>
                      </Clickable>
                    </Col>
                  </ContentArea>
                </Container>
              </Animated.View>
            </TouchableWithoutFeedback>
          </ModalContainer>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

export default PromptAuthModal;
