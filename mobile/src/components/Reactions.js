import React, { useState, useEffect } from 'react'
import styled from 'styled-components'
import { Icon, Row, Text } from 'components'
import { theme } from 'lib'
import { useTranslation } from 'react-i18next'

const Wrapper = styled.View`
  flex-direction: row;
  height: 24px;
  align-items: center;
  z-index: -1;
`

const IconWrapper = styled.View`
  border-style: solid;
  border-width: ${props => props.first ? '0' : '1px'};
  border-radius: 32px;
  border-color: ${props => props.col || 'white'};
  margin-right: -3px;
`

const Reactions = ({ entity, showNoLikesNotice, col, small }) => {
  const [sortedData, setSortedData] = useState([])
  const [totalLikes, setTotalLikes] = useState(0),
    { t } = useTranslation()

  useEffect(() => {
    if(!entity) return

    setSortedData(
      Object
      .entries(entity)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
    )

    setTotalLikes(Object.values(entity).reduce((a, b) => a + b))
  }, [entity, JSON.stringify(entity)])

  if(!entity) return null

  if(!totalLikes && showNoLikesNotice) {
    return (
      <Text c1 med col={theme.GREY_60} zIndex={-1}>
        {t('comments.beFirstReaction')}
      </Text>
    )
}

  if(!totalLikes) {
    return (
      <Text c1 med col={theme.GREY_60} zIndex={-1}>Likes 0</Text>
    )
  }

  return (
    <Wrapper>
      {sortedData.map((x, i) => {
        if(!x[1]) return null

        return (
          <IconWrapper col={col} key={i} first={i === 0} small={small}>
            <Icon type={`${x[0]}React`} dimensions={16} />
          </IconWrapper>
        )
      })}

      <Text size='12px' bold marg='0 0 0 8px'>
        {totalLikes}
      </Text>
    </Wrapper>
  )
}

export default Reactions
