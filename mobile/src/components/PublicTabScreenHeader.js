import { memo, useMemo } from 'react';
import styled from 'styled-components';
import { useDispatch } from 'react-redux';

// Components
import { View, StatusBar, TouchableOpacity, Platform, StyleSheet } from 'react-native';
import { Icon, Clickable } from 'components';
import { AddPost } from '../screens/Home/components';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

const Logo = styled.Image`
  height: 32px;
  width: 164px;
`;

const TabScreenHeader = memo(({ lightContent, isFeed = false }) => {
  const dispatch = useDispatch();

  const insets = useSafeAreaInsets();

  const handleTriggerPrompt = () => {
    dispatch({
      type: 'SHOW_PROMPT_MODAL',
      payload: {
        isPromptModalVisible: true,
      },
    });
  };

  const header = useMemo(
    () => (
      <View style={{ flexDirection: 'row', paddingHorizontal: 16 }}>
        <View style={{ flex: 1 }}>
          <Logo source={require('assets/branding/logo-black.png')} resizeMode="contain" />
        </View>
        <TouchableOpacity style={styles.avatarContainer} onPress={handleTriggerPrompt}>
          <Icon opacity={0.45} type="profileFilled" dimensions={26} />
        </TouchableOpacity>
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
          <Clickable style={{ opacity: 0.25 }} pad="0 10px 0 10px" onClick={handleTriggerPrompt}>
            <Icon type="notificationOutline" col={lightContent ? '#fff' : '#000'} dimensions={20} />
          </Clickable>

          <Clickable style={{ opacity: 0.25 }} pad="0 16px 0 10px" onClick={handleTriggerPrompt}>
            <Icon type="chat" col={lightContent ? '#fff' : '#000'} dimensions={20} />
          </Clickable>
        </View>
      </View>
    ),
    [lightContent],
  );

  return (
    <View style={styles.header} edges={['top', 'left', 'right']}>
      <View style={{ marginTop:  insets.top ,marginBottom: isFeed ? 0 : 16 }}>
        {header}

        {isFeed && <AddPost />}
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  avatarContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  header: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 5,
    },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 5,
    backgroundColor: 'white',
    borderColor: 'gray',
    borderBottomWidth: 0.3,
    zIndex: 10,
    marginBottom: 16,
  },
});

export default TabScreenHeader;
