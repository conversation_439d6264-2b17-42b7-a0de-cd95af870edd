import React, { useState } from 'react'
import { Clickable, Row, Icon } from 'components'

const Clear = ({ handleClear, show }) => {
  if(!show) return null

  return (
    <Clickable onClick={handleClear}>
      <Row
        hasBorder='2px solid #000'
        hasRadius='50px'
        centerAll
        wid='40px'
        ht='40px'
        marg='0 8px 0 0'
        noFlex
      >
        <Icon type='close' dimensions={14} />
      </Row>
    </Clickable>
  )
}

export default Clear