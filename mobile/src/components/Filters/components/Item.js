import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import Animated, { useSharedValue, useAnimatedStyle, withTiming } from 'react-native-reanimated'
import { Row, Clickable, Text } from 'components'
import { theme } from 'lib'

const Item = ({ onClick, item, isSelected, dontShow, isSub }) => {
  const { t } = useTranslation()
  const opacity = useSharedValue(isSub ? 0 : 1)
  const animatedStyle = useAnimatedStyle(() => {
    return { opacity: opacity.value }
  })

  useEffect(() => {
    if(dontShow) return fadeOut()
    
    fadeIn()
  }, [dontShow])

  function fadeIn() {
    opacity.value = withTiming(1, { duration: 300 })
  }

  function fadeOut() {
    opacity.value = 0
  }

  if(dontShow) return null

  return (
    <Clickable onClick={onClick}>
      <Animated.View
        style={[{}, animatedStyle]}
      >
        <Row
          noFlex
          pad='9px 24px'
          marg='0 8px 0 0'
          hasRadius='32px'
          bg={isSelected ? theme.SECONDARY_20 : theme.GREY_10}
          hasBorder={`1px solid ${isSelected ? theme.SECONDARY : 'transparent'}` }
        >
          <Text>{t(item.translatedValue)}</Text>
        </Row>
      </Animated.View>
    </Clickable>
  )
}

export default Item
