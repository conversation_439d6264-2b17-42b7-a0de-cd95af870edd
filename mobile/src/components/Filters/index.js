import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>View } from 'react-native'
import { Row } from 'components'
import { Clear, Item } from './components'

const Filters = ({ 
  marg, 
  options = [], 
  onChange = () => {},
  onSubChange = () => {},
  refreshing
}) => {
  const [rendered, setRendered] = useState(false)
  const [selected, setSelected] = useState(null)
  const [subSelected, setSubSelected] = useState([])
  const [subOptions, setSubOptions] = useState([])
  
  useEffect(() => {
    setRendered(true)
    if(!rendered || refreshing) return

    handleClear()
  }, [refreshing])
  
  function handleClear() {
    setSelected(null)
    setSubOptions([])
    setSubSelected([])
    onChange(null)
    onSubChange(null)
  }

  function handleSelect(item) {
    if(selected?.value === item.value) return handleClear()

    setSelected(item)
    onChange(item)
    if(item.subOptions?.length) setSubOptions(item.subOptions)
  }

  function handleSubSelect(item) {
    if(subSelected?.length && subSelected.includes(item?.value)) {
      const newSelected = subSelected.filter(x => x !== item.value)
      
      setSubSelected(newSelected)
      return  onChange({ type: selected.value, values: newSelected })
    }

    const newSelected = [...subSelected, item?.value]
    setSubSelected(newSelected)
    onSubChange({ type: selected.value, values: newSelected })
  }

  if(!options.length) return null

  return (
    <Row marg={marg} noFlex>
      <ScrollView 
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 16 }}
      >
        <Clear 
          handleClear={handleClear} 
          show={selected}
        />

        {options.map((x, i) => (
          <Item 
            key={i}
            item={x}
            isSelected={selected && selected.value === x.value}
            dontShow={selected && selected.value !== x.value}
            onClick={() => handleSelect(x)}
          />
        ))}

        {subOptions?.map((x, i) => (
          <Item 
            key={i}
            item={x}
            isSelected={subSelected?.length && subSelected?.includes(x.value)}
            onClick={() => handleSubSelect(x)}
            isSub
          />
        ))}
      </ScrollView>
    </Row>
  )
}

export default Filters