import React from 'react'
import { useSelector } from 'react-redux'
import styled from 'styled-components'
import { Text, Icon, Spinner } from 'components'
import { getScale } from 'core'
import { theme } from 'lib'

const StyledButton = styled.TouchableOpacity`
  width: ${props => props.wid || '100%'};
  height: ${props => getScale(props.small ? '32px' : props.big ? '44px' : '40px')};
  margin: ${props => props.marg || 0};
  border-radius: 50px;
  background-color: ${props => props.bg || '#000'};
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${props => props.pad || '0'}

  ${props => props.bordered && `
    height: ${props.small ? '30px' : props.big ? '42px' : '38px'};
    border: 2px;
    border-color: #000;
    background-color: transparent;
  `}

  ${props => props.reverse && `
    border-color: #fff;
    background-color: transparent;
  `}

  ${props => props.disabled && !props.loading && `background-color: ${theme.GREY_20};`};
  ${props => props.icon && !props.text && `
    height: 40px;
    width: ${props.wid || '40px'};
    border-radius: 40px;
  `}

  ${props => props.ht && `height: ${props.ht};`}

  ${props => props.greyedOut && `
    background-color: ${theme.GREY_20};
  `}
`

const InnerContainer = styled.View`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
`

const SocialContainer = styled.View`
  flex-direction: row;
  width: 100%;
  align-items: center;
  justify-content: center;
`

const PaypalImage = styled.Image`
  position: absolute;
  left: 12px;
  height: 28px;
  width: 23px;
`

const SocialImage = styled.Image`
  position: absolute;
  left: 8px;
  ${props => `
    height: ${props.dimensions || '32px'};
    width: ${props.dimensions || '32px'};
  `}
`

const StyledText = styled.Text`

`

const Button = (props) => {
  const { facebook, google, paypal, apple, fitText, isFetching=false } = props,
    loadingButton = useSelector(state => state?.appState?.loadingButton),
    isLoading = props.action && props.action === loadingButton

  function getTextColor() {
    if (props.reverse) return '#fff'
    if (props.bordered) return '#000'
    return props.textColor || '#fff'
  }

  if (facebook || google || paypal || apple) {
    const socialName = paypal ? 'Paypal' : facebook ? 'Facebook' : 'Google'

    return (
      <StyledButton bordered big marg={props.marg} onPress={props.onClick}>
        <SocialContainer>
          {paypal && (
            <PaypalImage
              resizeMode='contain'
              source={require(`../assets/icons/PaypalSignup.png`)}
            />
          )}

          {facebook && (
            <SocialImage
              resizeMode='contain'
              source={require(`../assets/icons/FacebookSignup.png`)}
            />
          )}

          {google && (
            <SocialImage
              resizeMode='contain'
              source={require(`../assets/icons/GoogleSignup.png`)}
            />
          )}

          {apple && (
            <SocialImage
              dimensions='30px'
              resizeMode='contain'
              source={require(`../assets/icons/AppleSignup.png`)}
            />
          )}

          <Text b1 bold>{props.text}</Text>
        </SocialContainer>
      </StyledButton>
    )
  }

  return (
    <StyledButton
      {...props}
      disabled={props.disabled || isLoading}
      greyedOut={props.greyedOut}
      onPress={props.onClick}
    >
      <InnerContainer>
        {isLoading || isFetching ?  (
          <Spinner col={props.col ? props.col : '#FA3F58'}/>
        ) : (
          <React.Fragment>
            {props.adding && (
              <Icon
                type='add'
                col={props.reverse ? '#fff' : props.bordered ? '#000' : '#fff'}
                dimensions={props.small ? 10.5 : 14}
                marg={props.small ? '0 7.75px' : '0 9px'}
              />
            )}

            {props.icon && (
              <Icon
                type={props.icon}
                col={props.reverse ? '#fff' : props.bordered ? '#000' : '#fff'}
                dimensions={props.iconDimensions || 18}
                marg={props.text && '0 7px 0 0'}
              />
            )}

            {props.text && !fitText && (
              <Text
                bold
                size={(props.small || !props.size) ? '14px' : '16px'}
                col={getTextColor()}
              >
                {props.text}
              </Text>
            )}

            {props.text && fitText && (
              <Text
                bold
                size={(props.small || !props.size) ? '14px' : '16px'}
                col={getTextColor()}
                adjustsFontSizeToFit
                numberOfLines={2}
              >
                {props.text}
              </Text>
            )}
          </React.Fragment>
        )}
      </InnerContainer>
    </StyledButton>
  )
}

export default Button
