import React, { useState, useEffect } from 'react'
import { ImageBackground, Image } from 'react-native'
import { theme } from 'lib'
import { getTopPadding } from 'core'
import { Col } from 'components'

const ProgressiveImage = ({
  thumbnail,
  uri,
  onThumbnailLoaded,
  style = {},
  show = true,
  bg
}) => {
  function handleThumbnailLoad() {
    if(!onThumbnailLoaded) return
    onThumbnailLoaded()
  }

  if(!show) return null

  return (
    <React.Fragment>
      <ImageBackground
        source={{ uri: thumbnail }}
        onLoad={handleThumbnailLoad}
        style={{
          position: 'absolute',
          left: 0,
          backgroundColor: bg || '#000',
          ...style
        }}
      />

      <ImageBackground
        style={style}
        source={{ uri: uri }}
        onLoad={handleThumbnailLoad}
      />
    </React.Fragment>
  )
}

export default ProgressiveImage
