import React, { useState, memo, useEffect } from 'react'
import styled from 'styled-components'
import { Col, Row, Text, Button, Clickable, LiveBadge } from 'components'
import { useNavigation } from '@react-navigation/native'
import { useDispatch, useSelector } from 'react-redux'
import { theme, normalizeDate, formatLocale } from 'lib'
import { useTranslation } from 'react-i18next'
import { addHours } from 'date-fns'

const Container = styled.View`
  height: ${props => props.isProfile ? '154px' : '159px'};
  width: ${props => props.small ? '164px' : '320px'};
  background-color: ${theme.SECONDARY};
`

const GameItem = memo(({ item, isProfile }) => {
  const {
    sport,
    type,
    homeId,
    league,
    division,
    score,
    home,
    away,
    progress,
    gameStatus
  } = item

  const [visible, setVisible] = useState(false),
    { t } = useTranslation(),
    navigation = useNavigation(),
    dispatch = useDispatch(),
    small = isProfile && type !== 'game',
    date = new Date(item.date),
    isLive = item.status === 'live',
    winner = item[item.winner]

  function handleVisible() {
    setVisible(!visible)
  }

  function handleNavigation() {
    dispatch({
      type: 'UPDATE_SHOUTOUT_DATA',
      payload: {
        eventId: item._id,
        player: undefined,
        type: 0,
        comment: ''
      }
    })

    navigation.navigate('AddShoutout', { eventObject: item })
  }

  return (
    <Clickable
      onClick={() => navigation.push('Game', { data: item })}
    >
      <Container small={small} isProfile={isProfile}>
        <Col pad='16px' useStart between>
          <Col noFlex>
            <Row between noFlex>
              <Text
                c1
                bold
                col='#fff'
                numberOfLines={1}
              >
                {league}
              </Text>

              {isLive && <LiveBadge progress={progress} gameStatus={gameStatus} sport={sport} />}
            </Row>

            <Text
              col='#fff'
              b1
              bold
              marg='9px 0 0'
              numberOfLines={1}
            >
              <Text
                col='#fff'
                b1
                bold={!winner || item.winner === 'home'}
              >
                {home?.name}
              </Text>

              <Text col='#fff' b1 bold> - </Text>

              <Text
                col='#fff'
                b1
                bold={!winner || item.winner === 'away'}
              >
                {away?.name}
              </Text>
            </Text>

            <Text
              col='#fff'
              b2
              marg='4px 0 0'
              numberOfLines={1}
            >
              {item.location}
            </Text>
          </Col>

          <Row noFlex between>
            {!isProfile && (
              <Button
                icon='shoutoutMegaphoneOutline'
                iconSize={22}
                wid='72px'
                onClick={handleNavigation}
                iconDimensions={22}
              />
            )}

            <Col
              wid={isProfile ? '100%' : '208px'}
              ht='40px'
              noFlex
              center
              bg={theme.SECONDARY_60}
              hasRadius='48px'
            >
              <Row centerAll marg='4px 0 0'>
                <Text med col='#fff'>
                  {isLive || item.status === 'finished'
                    ? (
                      <Row center>
                        <Text b2 med col='#fff'>{score.home}</Text>
                        <Text b2 med col='#fff'> : </Text>
                        <Text b2 med col='#fff'>{score.away}</Text>
                      </Row>
                    )
                    : item.status === 'postponed'
                      ? t('games.postponed')
                      : formatLocale(date, 'MMM d, k:mm a')
                  }
                </Text>
              </Row>
            </Col>
          </Row>
        </Col>
      </Container>
    </Clickable>
  )
})

export default GameItem
