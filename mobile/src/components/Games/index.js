import React, { useState, useEffect, useRef, memo } from 'react'
import styled from 'styled-components'
import { FlatList } from 'react-native'
import { useTranslation } from 'react-i18next'
import { startOfDay, endOfDay } from 'date-fns'
import { Col, Row, Text } from 'components'
import { GameItem } from './components'

const Background = styled.View`
  position: absolute;
  width: 100%;
  height: 159px;
  bottom: -16px;
`

const Games = ({ data, listRef }) => {
  const [filtered, setFiltered] = useState([]),
    { t } = useTranslation(),
    ref = listRef || useRef()

  useEffect(() => {
    if(!data) return

    const now = new Date(),
      dayStart = startOfDay(now),
      dayEnd = endOfDay(now)

    const todaysGames = data.filter(game => {
      return new Date(game.date) > dayStart && new Date(game.date) < dayEnd
        || game.status === 'live'
    })
    const liveGames = todaysGames.filter(game => game.status === 'live')
    const upcomingGames = todaysGames.filter(game =>  game.status == 'upcoming')
    const concludedGames = todaysGames.filter(game => game.status === 'finished')

    setFiltered([
      ...liveGames,
      ...upcomingGames,
      ...concludedGames
    ])
  }, [data])

  if(!filtered?.length) return null

  return (
    <Col marg='0 0 40px' noFlex>
      <Background />

      <Row between center pad='0 16px' marg='0 0 12px'>
        <Text h2>{t('home.todaysMatches')}</Text>

        {/*
          <Clickable>
            <Text b2 bold>{t('common.seeAll')}</Text>
          </Clickable>
        */}
      </Row>

      <FlatList
        horizontal
        ref={ref}
        contentContainerStyle={{ paddingRight: 16, paddingLeft: 16 }}
        renderItem={(props) => <GameItem {...props} />}
        keyExtractor={(item, i) => i.toString()}
        data={filtered}
        listEmptyComponent={(<Col noFlex ht='159px' />)}
        ItemSeparatorComponent={() => (<Row noFlex wid='12px'></Row>)}

         // Performance settings
         removeClippedSubviews={true}
         initialNumToRender={6}
         maxToRenderPerBatch={6}
         updateCellsBatchingPeriod={75}
         windowSize={3}
      />
    </Col>
  )
}

export default memo(Games)
