import React, { useState, useEffect } from 'react'
import { Platform } from 'react-native'
import styled from 'styled-components'
import ImagePicker from 'react-native-image-crop-picker'
import ImageResizer from 'react-native-image-resizer'
import { theme, nations } from 'lib'
import { Icon, Clickable, Text, Row, Col } from 'components'
import { handlePermission } from 'core'
import { useTranslation } from 'react-i18next'
import { useNavigation } from '@react-navigation/native'

const StyledInput = styled.TextInput`
  margin: 0 0 0 10px;
  max-width: 85%;
`

const ChatInput = (props) => {
  const { t } = useTranslation(),
    navigation = useNavigation();

  function handleBlocked(permission) {
    navigation.navigate('PermissionBlocked', { permission })
  }

  async function getPermission() {
    const perm = await handlePermission({
      checkedPermission: 'gallery',
      onBlocked: () => handleBlocked(t('permissions.gallery'))
    })
    return perm;
  }

  async function openPicker() {
    const isWithPermission = await getPermission();
    /*
      For some reason when the options are given,
      Crop picker returns a huge image
    */

    if (isWithPermission) {
      ImagePicker
      .openPicker({
        width: 500
      })
      .then(async image => {
        const resized = await ImageResizer.createResizedImage(
          Platform.OS === 'ios' ? `file://${image.path}` : image.path, // path
          1080, // maxWidth
          1080, // maxHeight
          'JPEG', // compressFormat
          30, // quality
          0, // rotation
          undefined, // outputPath
          false, //keepMeta
          {} //options
        )

        props.handleSelect({
          uri: Platform.OS === 'ios' ? resized.path : resized.uri,
          type: image.mime
        })
      }).catch(() => {})
    }
  }

  return (
    <Row noFlex ht='48px'>
      <Row pad='0 14px' center hasRadius='44px' bg={theme.GREY_10}>
        <Row center>

          <StyledInput
            {...props}
            value={props.value}
            onChangeText={(e) => props.onChangeText(e)}
            placeholder='Message...'
            ref={props.inputRef}
            placeholderTextColor={theme.GREY_60}
            placeholderStyle={{ fontSize: 16 }}
          />
        </Row>

        {props.value !== ''
          ? (
            <Clickable onClick={props.onClick} hitSlop={25}>
              <Icon
                col={theme.TALE}
                dimensions={20}
                type='send'
              />
            </Clickable>
          ) : props.handleSelect && (
            <Clickable onClick={openPicker} hitSlop={25}>
              <Icon
                col={theme.GREY_60}
                dimensions={20}
                type='attach'
              />
            </Clickable>
          )
        }
      </Row>
    </Row>
  )
}

export default ChatInput
