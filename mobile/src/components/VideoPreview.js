import React, { useState, useCallback } from 'react'
import Video from 'react-native-video'
import { <PERSON>, <PERSON><PERSON>, Spin<PERSON>, Clickable, Icon } from 'components'
import { View, Text } from 'react-native'
// import convertToProxyURL from 'react-native-video-cache'
import { useFocusEffect } from '@react-navigation/native';


const VideoPreview = ({route}) => {
  const { item } = route.params,
  [isVideoLoading, setIsVideoLoading] = useState(true),
  [isPause, setIsPause] = useState(false)

  let videoPlayerRef = null

  const HeaderRightContent = () => {
    return (
      <View style={{ marginTop: 4}}>
        <Clickable onClick={() => {}}>
          <Icon
            type={'share'}
            dimensions={18}
          />
        </Clickable>
      </View>
    )
  }

  useFocusEffect( 
    useCallback(() => {
      return () => {
        setIsPause(true)
      }
    }, []))


  return (
    <Col bg='#000'>
      <Header 
        hasBack 
        isModal 
        bg='#000' 
        lightContent
        transparent
        // headerRightContent={<HeaderRightContent />} //Enable this when implementing share user story
      />
        {
          isVideoLoading && (
            <Col centerAll ht='100%' noFlex>
              <Spinner size={40} />
            </Col>
          )
        }
        {
          item?.media[0]?.uri && 
            <Video
              ref={ref => { videoPlayerRef = ref }}
              resizeMode='contain'
              source={{ uri: item?.media[0]?.uri }} 
              style={{ flexGrow: 1, aspectRatio: item?.aspectRatio || 1, maxWidth: '100%' }}
              controls={true}
              onReadyForDisplay={() => setIsVideoLoading(false)}
              repeat
              paused={isPause}
            />
        }
    </Col>
  )
}

export default VideoPreview