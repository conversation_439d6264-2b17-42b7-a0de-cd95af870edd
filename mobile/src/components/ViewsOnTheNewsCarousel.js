import { useRef, useState, useMemo, memo } from 'react'
import { View, Animated, Dimensions, Platform } from 'react-native'
import { useTranslation } from 'react-i18next'
import { Text, Col, Clickable, Icon } from 'components'
import { theme, formatLocale, formatText, Api } from 'lib'
import styled from 'styled-components'
import DeviceInfo from 'react-native-device-info'
import { useNavigation } from '@react-navigation/native'
import { useDispatch, useSelector } from 'react-redux'

const Container = styled.View`
  height: 235px;
  width: 167px;
  background-color: ${theme.SECONDARY};
  flex-direction: column;
  justify-content: space-between;
  margin-bottom: 16px;
`

const ImageBackground = styled.ImageBackground`
  width: 72px;
  height: 97px;
  align-items: center;
  justify-content: center;
  margin: 0 12px 0 0;
`

let isTablet = DeviceInfo.isTablet()

const ViewsOnTheNewsCarousel = ({ letsChatSportsData, listRef }) => {
  const { t } = useTranslation(),
    currentUser = useSelector(state => state.user) || {},
    ref = listRef || useRef(),
    scrollX = useRef(new Animated.Value(0)).current,
    [layout, setLayout] = useState({ width: null }),
    displayedData = letsChatSportsData?.slice(0, 10),
    deviceWidth = Dimensions.get('window').width,
    iphone15Plus = DeviceInfo.getModel() === 'iPhone 15 Plus',
    ipad6thGen = DeviceInfo.getModel() === 'iPad (6th generation)',
    ipadPro6thGen = DeviceInfo.getModel() === 'iPad',
    navigation = useNavigation(),
    dispatch = useDispatch(),
    isAndroid = Platform.OS === 'android',
    isLoggedIn = !!currentUser?._id

  const styles = useMemo(() => (
    {
      reactComments: {
        flexDirection: 'row'
      },
      reactCommentsRow: {
        width: 56,
        flexDirection: 'row',
        paddingLeft: 10
      },
      reactIcons: {
        marginRight: 4,
        marginTop: isAndroid ? 0 : -2
      },
      carouselItem: {
        width: 167,
        backgroundColor: theme.SECONDARY,
        flexDirection: 'column',
        justifyContent: 'space-between',
        marginBottom: 16
      }
    }
  ), [])

  const setInitialLayout = (e) => {
    if (!layout.width) {
      let tabletPad

      const modelException = iphone15Plus ? 90 : 100
      tabletPad = isTablet && displayedData.length === 6 ? 10 : isTablet && displayedData.length === 5 ? 25 : 0
      tabletPadException = ipad6thGen ? tabletPad - 10 : ipadPro6thGen ? tabletPad + 50 : tabletPad
      tabletPad = (displayedData.length + (deviceWidth / e.width) - tabletPadException)

      const mobilePad = displayedData.length < 5 ? (displayedData.length + (deviceWidth / e.width) + 80) : (displayedData.length + (deviceWidth / e.width) + modelException)
      const devicePad = isTablet ? tabletPad : mobilePad
      const pad = ((deviceWidth / e.width) * displayedData.length) + devicePad
      const adjustWidth = (e.width * (deviceWidth / e.width)) - (((deviceWidth / e.width) * displayedData.length) + pad)

      setLayout({ ...e, width: deviceWidth - adjustWidth })
    }
  }

  const Indicator = ({ scrollX }) => {
    const dt = displayedData
    return <View style={{ flexDirection: 'row', display: 'flex', justifyContent: 'center', marginBottom: 10 }}>
      {
        dt.map((_, i) => {
          const contentWidth = layout.width
          const inputRange = [(i - 1) * contentWidth, i * contentWidth, (i + 1) * contentWidth]
          const scale = scrollX.interpolate({
            inputRange,
            outputRange: [0.8, 1.4, 0.8],
            extrapolate: 'clamp'
          })
          const opacity = scrollX.interpolate({
            inputRange,
            outputRange: [0.2, 0.9, 0.2],
            extrapolate: 'clamp'
          })

          return <Animated.View
            key={`indicator-${i}`}
            style={{
              height: 8,
              width: 8,
              borderRadius: 5,
              backgroundColor: '#FB3351',
              margin: 5,
              opacity,
              transform: [{
                scale
              }]
            }}
          />
        })
      }
    </View>
  }

  function isText(text, mentioned) {
    if (text.type === 'mention') {
      const isMentioned = mentioned?.find(x => x === text.text)

      return !isMentioned
    }

    return ['text'].includes(text.type)
  }

  const Media = ({ item }) => {
    const getImageUri = (item) => {
      if (item?.metaData) {
        return item.metaData.image || item.metaData.icon
      } else if (item?.media && item.media.length) {
        return item.media[0].thumbnail
      } else if (item.youtubeId) {
        return `https://img.youtube.com/vi/${item.youtubeId}/0.jpg`
      } else {
        return null
      }
    }

    return (
      <ImageBackground
        source={{ uri: getImageUri(item) }}
        style={{ width: '100%', height: 90, backgroundColor: theme.PRIMARY }}>
      </ImageBackground>
    )
  }

  const Caption = ({ item }) => {
    const text = item.metaData?.title || item.metaData?.description || item.caption || ''

    return (
      <Text numberOfLines={3} marg='4px' pad='4px' style={{ height: 65 }}>
        {
          formatText(text).map((x, i) => (
            isText(x, item.mentioned?.map(x => x ? `@${x?.username}` : null))
              ? (
                <Text key={i} bold b2 med col='#fff'>
                  {x.text}
                </Text>
              ) : (
                <Text key={i} bold b2 med col={'#fff'} >
                  {x.text}
                </Text>
              )
          ))
        }
      </Text>
    )
  }

  const ViewsOnTheNewsItem = memo(({
    item,
    handleClick = () => null,
    setLayout,
    index
  }) => {
    const prevReaction = currentUser.reactions?.find(x => x.entityId === item?._id)?.reaction,
      [reactionLoading, setReactionLoading] = useState(false)

    async function react(reaction) {
      if (!isLoggedIn) {
        dispatch({
          type: 'SHOW_PROMPT_MODAL',
          payload: {
            isPromptModalVisible: true,
          },
        });
        return
      }
      if (reactionLoading) return
      setReactionLoading(true)

      const data = {
        entityType: 'newsview',
        reaction,
        entityId: item?._id,
        userId: currentUser?._id
      }

      const res = await Api.post('/users/react', data)

      setReactionLoading(false)

      if (!res) {
        return
      }

      dispatch({
        type: 'ADD_REACTION_TO_POST',
        payload: data
      })

      let currentReactions = item.reactionCounts
      const isRemoved = res.includes('removed')

      if (isRemoved) {
        currentReactions[reaction] = currentReactions[reaction] > 0 ? currentReactions[reaction] - 1 : currentReactions[reaction]
      } else {
        currentReactions[reaction] = currentReactions[reaction] + 1

        if (prevReaction) {
          currentReactions[prevReaction] = currentReactions[prevReaction] - 1
        }
      }

      dispatch({
        type: 'UPDATE_VIEWS_ON_THE_NEWS',
        payload: {
          ...item,
          reactionCounts: currentReactions,
          reactionCount: Object.values(currentReactions).reduce((a, b) => a + b, 0)
        }
      })
    }

    const Reactions = () => {
      return (<View style={{ flexDirection: 'row', padding: 10, paddingLeft: 0 }}>
        <Clickable style={styles.reactCommentsRow} onClick={() => react('like')}>
          <Icon
            type={prevReaction === 'like' ? 'likeReact' : 'roundedLike'}
            dimensions={prevReaction === 'like' ? 20 : '24'}
            style={prevReaction === 'like' ? styles.reactIcons : {}}
            col={theme.PRIMARY_WHITE}
          />
          <Text
            b2
            adjustsFontSizeToFit
            wid='28px'
            numberOfLines={1}
            col={theme.PRIMARY_WHITE}
          >
            {item.reactionCounts.like < 0 ? 0 : item.reactionCounts.like}
          </Text>
        </Clickable>

        <Clickable style={styles.reactCommentsRow} onClick={() => react('unlike')}>
          <Icon
            type={prevReaction === 'unlike' ? 'unlikeReactWhiteBG' : 'roundedLike'}
            dimensions={prevReaction === 'unlike' ? 20 : '24'}
            style={
              prevReaction === 'unlike' ?
                styles.reactIcons :
                {
                  transform: [{ scaleX: -1 }, { rotateX: '180deg' }],
                  marginTop: -7,
                  marginRight: 6,
                  marginLeft: -7
                }
            }
            col={theme.PRIMARY_WHITE}
          />
          <Text
            b2
            adjustsFontSizeToFit
            wid='28px'
            numberOfLines={1}
            col={theme.PRIMARY_WHITE}
          >
            {item.reactionCounts.unlike < 0 ? 0 : item.reactionCounts.unlike}
          </Text>
        </Clickable>

        <Clickable style={styles.reactCommentsRow} onClick={() =>
          navigation.navigate('ViewsOnTheNewsComments', { item: item, autoFocus: true })
        }>
          <Icon type='roundedHome' dimensions={24} col={theme.PRIMARY_WHITE} />
          <Text
            b2
            adjustsFontSizeToFit
            wid='23px'
            numberOfLines={1}
            col={theme.PRIMARY_WHITE}
          >
            {item.commentsCount}
          </Text>
        </Clickable>
      </View>)
    }

    return (
      <View
        onLayout={(event) => setLayout(event.nativeEvent.layout)}
        style={{
          ...styles.carouselItem,
          marginRight: (index === (displayedData.length - 1)) && 12
        }}
        activeOpacity={0.8}
      >
        <Clickable onClick={handleClick}>
          <Media item={item} />
          <Text b2 col={theme.PRIMARY_WHITE} pad='8px'>
            {formatLocale(new Date(item.createdAt), 'dd MMM yyy')}
          </Text>
          <Caption item={item} />
        </Clickable>

        <View style={{ zIndex: 9999, position: 'relative' }}>
          <Reactions item={item} />
        </View>
      </View>
    )
  })

  function handleClick(props) {
    navigation.navigate('ViewsOnTheNewsComments', { item: props.item })
  }

  return (
    <View>
      <Animated.FlatList
        horizontal
        showsHorizontalScrollIndicator={false}
        ref={ref}
        data={displayedData}
        onScroll={Animated.event([{ nativeEvent: { contentOffset: { x: scrollX } } }], { useNativeDriver: false })}
        scrollEventThrottle={16}
        contentContainerStyle={{ paddingLeft: 16, paddingRight: 16, paddingBottom: 20 }}
        ItemSeparatorComponent={() => <Col noFlex wid='12px' />}
        keyExtractor={(_item, index) => index}
        renderItem={(props) => (
          <ViewsOnTheNewsItem
            {...props}
            handleClick={() => handleClick(props)}
            setLayout={setInitialLayout}
          />
        )}
      />

      <Indicator scrollX={scrollX} />
    </View>
  )
}

export default memo(ViewsOnTheNewsCarousel)