import { useRef, useState } from 'react'
import { View, Animated } from 'react-native'
import { theme } from 'lib'
import { useTranslation } from 'react-i18next'
import { useNavigation } from '@react-navigation/native'
import Row from '../Row'
import Text from '../Text'
import Col from '../Col'
import Shoutout from '../Shoutout'
import Item from '../../screens/ShoutoutsHome/components/Item'
import DeviceInfo from 'react-native-device-info'

let isTablet = DeviceInfo.isTablet()

const Shoutouts = ({ listRef, shoutoutsList, shoutoutsAthlete }) => {
  const { t } = useTranslation(),
    navigation = useNavigation(),
    ref = listRef || useRef(),
    scrollX = useRef(new Animated.Value(0)).current,
    [layout, setLayout] = useState({ width: null })

  const combinedShoutouts = [
    ...shoutoutsAthlete
      .filter(item => item?.media && item.media?.length > 0)
      .slice(0, 10)
      .map(item => ({ ...item, shoutoutType: 'fromAthlete' })),
    ...shoutoutsList
      .slice(0, 10)
      .map(item => ({ ...item, shoutoutType: 'fromFan' }))
  ]

  const sortedShoutouts = combinedShoutouts.sort((a, b) => {
    const dateA = new Date(a.createdAt)
    const dateB = new Date(b.createdAt)
    return dateB - dateA
  })

  if (!sortedShoutouts.length) {
    return (
      <View style={{ marginBottom: 16 }}>
        <Row pad='0 16px' noFlex>
          <Text h2>{t('common.shoutouts')}</Text>
        </Row>
        <Col noFlex center marg='0 16px 16px'>
          <Text col={theme.GREY_60}>{t('chat.noResults')}</Text>
        </Col>
      </View>
    )
  }

  const setInitialLayout = (e) => {
    if (!layout.width) {
      const widthPadding = (e.width / sortedShoutouts.length) / sortedShoutouts.length
      const adjustWidth = isTablet ? (e.width / sortedShoutouts.length) + widthPadding : 5
      setLayout({ ...e, width: (e.width - adjustWidth) })
    }
  }

  function handleClick(item) {  
    navigation.navigate(
      'Shoutouts',
      {
        data: shoutoutsList,
        title: t('shoutout.shoutoutsFromFans')
      }
    )
  }

  const Indicator = ({ scrollX }) => {
    return (
      <View style={{ flexDirection: 'row', display: 'flex', justifyContent: 'center', marginBottom: 10 }}>
        {sortedShoutouts.map((_, i) => {
          const shoutoutContentWidth = layout.width || 320
          const inputRange = [(i - 1) * shoutoutContentWidth, i * shoutoutContentWidth, (i + 1) * shoutoutContentWidth]
          const scale = scrollX.interpolate({
            inputRange,
            outputRange: [0.8, 1.4, 0.8],
            extrapolate: 'clamp'
          })
          const opacity = scrollX.interpolate({
            inputRange,
            outputRange: [0.2, 0.9, 0.2],
            extrapolate: 'clamp'
          })

          return (
            <Animated.View
              key={`indicator-${i}`}
              style={{
                height: 8,
                width: 8,
                borderRadius: 5,
                backgroundColor: '#FB3351',
                margin: 5,
                opacity,
                transform: [{ scale }]
              }}
            />
          )
        })}
      </View>
    )
  }

  return (
    <View style={{ marginBottom: 16 }}>
      <Row pad='0 16px' noFlex>
        <Text h2>{t('common.shoutouts')}</Text>
      </Row>
      
      <Animated.FlatList
        horizontal
        showsHorizontalScrollIndicator={false}
        ref={ref}
        data={sortedShoutouts}
        onScroll={Animated.event([{ nativeEvent: { contentOffset: { x: scrollX } } }], { useNativeDriver: false })}
        scrollEventThrottle={16}
        contentContainerStyle={{ paddingLeft: 16, paddingRight: 16, paddingBottom: 20 }}
        ItemSeparatorComponent={() => <Col noFlex wid='12px' />}
        keyExtractor={(item, index) => item._id || item.id || `shoutout-${index}`}
        renderItem={({ item, index }) => {
          if (item.shoutoutType === 'fromAthlete') {
            return <Item item={item} index={index} setLayout={setInitialLayout}/>
          } else {
            return <Shoutout item={item} index={index} setLayout={setInitialLayout} handleClick={() => handleClick(item)} fromHome={true} />
          }
        }}
      />

      <Indicator scrollX={scrollX} />
    </View>
  )
}

export default Shoutouts