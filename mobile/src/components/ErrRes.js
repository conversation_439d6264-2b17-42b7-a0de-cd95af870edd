import React, { useState, useRef, useEffect } from 'react'
import { Dimensions } from 'react-native'
import { Animated } from 'react-native'
import { useSelector, useDispatch } from 'react-redux'
import styled from 'styled-components'
import { theme, handleRes } from 'lib'
import { Text, Icon, Clickable } from 'components'

const ErrWrap = styled.View`
  min-height: 34px;
  background-color: ${props => props.isErr ? theme.SECONDARY : theme.GREEN};
  justify-content: center;
  width: ${Dimensions.get('window').width - 32}px;
  left: 16px;
  border-radius: 5px;
  padding: 8px 16px;
  justify-content: center;
`
const IconContainer = styled.TouchableOpacity`
  position: absolute;
  right: 0;
  top: 0;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  height: 40px;
  padding: 0 10px;
  z-index: 4;
`

let timer
const ErrRes = ({  }) => {
  const [lastErrRes, setLastErrRes] = useState(''),
    [progress, setProgress] = useState(new Animated.Value(-100)),
    errRes = useSelector(state => state.appState.errRes || {}),
    { err, res } = errRes,
    animRef = new Animated.Value(-100),
    dispatch = useDispatch()

  useEffect(() => {
    setLastErrRes('')
  }, [])

  useEffect(() => {
    const val = err || res
    if(lastErrRes === val || (!err && !res)) return

    show()
  }, [errRes])

  function show() {
    clearTimeout(timer)
    setLastErrRes(err || res)

    Animated
      .timing(progress, {
        toValue: 70,
        duration: 300,
        useNativeDriver: false
      })
      .start()

    timer = setTimeout(() => {
      hide()
    }, 3000)
  }

  function hide() {
    clearTimeout(timer)
    setLastErrRes('')

    Animated
      .timing(progress, {
        toValue: -100,
        duration: 300,
        useNativeDriver: false
      })
      .start()

    setTimeout(() => {
      dispatch({
        type: 'HANDLE_RES'
      })
    }, 400)
  }

  if(typeof err !== 'string' && typeof res !== 'string') return null

  return (
    <Animated.View
      style={{
        position: 'absolute',
        zIndex: 1,
        width: '80%',
        backgroundColor: 'transparent',
        top: progress
      }}
    >
      <ErrWrap isErr={err}>
        {/*
          <IconContainer onPress={hide}>
            <Icon type='close' dimensions={13} col='#fff'/>
          </IconContainer>
        */}

        <Text med col='white'>{err || res || ''}</Text>
      </ErrWrap>
    </Animated.View>

  )
}

export default ErrRes
