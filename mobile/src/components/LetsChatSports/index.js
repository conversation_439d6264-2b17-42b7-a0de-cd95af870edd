import React, { useMemo } from 'react'
import { View } from 'react-native'
import { Text, Col, Clickable } from 'components'
import { theme } from 'lib'
import { useTranslation } from 'react-i18next'
import LetsChatSportsCarousel from '../LetsChatSportsCarousel'
import { useNavigation } from '@react-navigation/native'

function LetsChatSports({ letsChatSportsData }) {
  const { t } = useTranslation(),
    navigation = useNavigation()

  const styles = useMemo(() => ({
    headerStyles: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingVertical: 12
    }
  }))

  function handleClick() {
    navigation.navigate('LetsChatSports')
  }

  return (
    <View>
      <View style={styles.headerStyles}>
        <View>
          <Text h2>Let's Chat Sports</Text>
        </View>
        <View>
          <Clickable onClick={handleClick}>
            <Text b2 bold>{t('common.seeAll')}</Text>
          </Clickable>
        </View>
      </View>

      {
        letsChatSportsData?.length ?
          <LetsChatSportsCarousel
            letsChatSportsData={letsChatSportsData}
          /> :
          <Col noFlex center marg='16px'>
            <Text col={theme.GREY_60}>{t('chat.noResults')}</Text>
          </Col>
      }
    </View>
  )
}

export default LetsChatSports