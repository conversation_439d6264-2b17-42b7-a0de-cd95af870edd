import React, { useCallback, useEffect, useMemo, useRef, useState, memo } from 'react';
import { useNavigation, useRoute } from '@react-navigation/native';
import { KeyboardAvoidingView, SafeAreaView, TextInput, TouchableOpacity, View, Keyboard, Platform, ScrollView } from 'react-native';
import { Icon, Header, Text } from 'components'
import { isTablet } from 'react-native-device-info'
import { useDispatch } from 'react-redux'

import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { getScale, handlePermission } from 'core';
import { theme, useIsMounted, getAspectRatio } from 'lib'
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { handleChangeText, submit } from '../lib';
import { MediaItem } from '../../../screens/Add/Post/components';
import { MediaPicker } from '../../../screens/Add/components';
import { TagModal } from '../../../components'
import ObjectId from 'bson-objectid'

let timer;
const StartChat = ({ route }) => {
  const { t } = useTranslation();
  const navigation = useNavigation();
  const [media, setMedia] = useState([]);
  const [caption, setCaption] = useState('')
  const isDisabled = !media?.length && !caption?.length;
  const insets = useSafeAreaInsets();
  const textInputRef = useRef(TextInput.prototype);
  const [metaData, setMetaData] = useState(null);
  const [youtubeId, setYoutubeId] = useState(null);
  const [loading, setLoading] = useState(false);
  const isPhone = !isTablet();
  const [showMediaPicker, setShowMediaPicker] = useState(false);
  const [showTagModal, setShowTagModal] = useState(false);
  const [permissionGiven, setPermissionGiven] = useState(false);
  const mounted = useIsMounted();
  const { cameraMedia, } = route.params || {};
  const [tagged, setTagged] = useState([]);
  const dimensions = getAspectRatio.toPixels(aspectRatio, 1080)
  const documentId = ObjectId().toString()
  const isAndroid = Platform.OS === 'android'
  const video = media.find(x => x.isVideo);
  const dispatch = useDispatch();
  const user = useSelector(state => state.user);
  const { navigateAfterPost } = useRoute().params || {}

  const { aspectRatio } =
    video
    || metaData
    || (!!media.length && media[0])
    || {}

  useEffect(() => {
    if (!cameraMedia) return

    setMedia(cameraMedia?.length ? cameraMedia : [cameraMedia])
  }, [cameraMedia])

  useEffect(() => {
    if (permissionGiven) {
      showPicker(permissionGiven)
    }
  }, [permissionGiven])

  const styles = useMemo(() => ({
    mainHeaderContainer: {
      flexDirection: 'row',
      paddingHorizontal: 20,
      paddingVertical: 20,
      alignItems: 'center',

    },
    headerContainer: {
      flexDirection: 'row',
      borderBottomWidth: 1,
      borderBottomColor: '#E4E7EC',
      paddingVertical: 10,
      paddingHorizontal: 20,
      alignItems: 'center',
    },
    headerContentContainer: {
      paddingHorizontal: 10,
    },
    downIconContainer: {
      marginHorizontal: 5,
    },
    closeContainer: {
      flex: 1,
      zIndex: 1
    },
    textInputContainerStyle: {
      flex: 1,
      padding: 20,
    },
    textInputStyle: {
      fontSize: getScale(16),
      fontFamily: 'CeraPro-Regular',

    },
    commandsContainer: {
      flexDirection: 'row',
      borderTopColor: '#E4E7EC',
      borderTopWidth: 1,
      paddingTop: 10,
      marginVertical: 8,
      paddingHorizontal: 16,
      justifyContent: 'space-between'
    },
    photoTagContainer: {
      flexDirection: 'row',
      flex: isTablet() ? 1 : 0,
    },
    photoContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderColor: 'rgba(0, 0, 0, 1)',
      borderRadius: 36,
      borderWidth: 2,
      paddingHorizontal: 10,
      paddingVertical: 2,
    },
    tagContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderColor: 'rgba(0, 0, 0, 1)',
      borderRadius: 36,
      borderWidth: 2,
      paddingHorizontal: 10,
      paddingVertical: 2,
      marginLeft: 5,
    },
    createPostContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: isDisabled ? theme.GREY_20 : 'black',
      borderRadius: 36,
      paddingHorizontal: 10,
      paddingVertical: 2
    }
  }), [isDisabled])

  const showPicker = (showPicker = false) => {
    if (permissionGiven) {
      setShowMediaPicker(showPicker)
    } else {
      getPermission()
    }
  }

  const getPermission = useCallback(async () => {
    const perm = await handlePermission({
      checkedPermission: 'gallery',
      onBlocked: () => handleBlocked(t('permissions.gallery'))
    })
    if (!mounted.current) return
    setPermissionGiven(perm)
  }, [handleBlocked, setPermissionGiven, handlePermission])

  const handleBlocked = useCallback((permission) => {
    navigation.navigate('PermissionBlocked', { permission })
  }, [navigation])

  const handleRemove = useCallback((idx) => {
    setMedia(media.filter((x, i) => i !== idx))
  }, [media])

  const header = useMemo(() => {
    return (
      <Header hasBack isModal title={t('letsChatSports.startChat')} paddingTop={Platform.OS === 'android' ? 0 : 1} />
    )
  }, [])

  const textInput = useMemo(() => {
    return (
      <TouchableOpacity
        activeOpacity={1}
        style={styles.textInputContainerStyle}
        onPress={() => textInputRef.current.focus()}
      >
        <TextInput
          ref={textInputRef}
          placeholder={t('letsChatSports.writeTopic')}
          placeholderTextColor={'rgba(0, 0, 0, 0.5)'}
          style={styles.textInputStyle}
          multiline
          keyboardType='ascii-capable'
          onChangeText={(e) => { handleChangeCaption(e) }}
        />
      </TouchableOpacity>
    )
  }, [
    styles.textInputContainerStyle,
    textInputRef,
    t,
    styles.textInputStyle,
    handleChangeText
  ])

  const handleChangeCaption = async (val) => {
    await handleChangeText({
      setCaption,
      timer,
      setMetaData,
      setMedia,
      setYoutubeId,
      caption,
      setLoading,
      val
    })
  }

  const handleSubmit = async () => {
    await submit({
      dispatch,
      caption,
      metaData,
      video,
      youtubeId,
      setLoading,
      tagged,
      navigation,
      media,
      user,
      isAndroid,
      documentId,
      dimensions,
      navigateAfterPost
    })
  }

  const photoVideoCommand = useMemo(() => (
    <TouchableOpacity
      style={[
        styles.photoContainer,
        isPhone ? { maxWidth: 150 } : {}
      ]}
      onPress={() => {
        showPicker(!showMediaPicker)
      }}
    >
      <Icon type='image' dimensions={12} />
      <Text
        bold
        size="14px"
        marg='0 5px'
        numberOfLines={1}
        adjustsFontSizeToFit
      >{t('add.photoVideo')}
      </Text>
    </TouchableOpacity>
  ), [isPhone, showMediaPicker, showPicker, t])

  const tagCommand = useMemo(() => (
    <TouchableOpacity
      style={[
        styles.tagContainer,
        isPhone ? { maxWidth: 80 } : {}
      ]}
      onPress={() => setShowTagModal(true)}
    >
      <Icon type='people' dimensions={15} />
      <Text
        bold
        size="14px"
        marg='0 5px'
        numberOfLines={1}
        adjustsFontSizeToFit
      >{t('add.tag')}</Text>
    </TouchableOpacity>
  ), [
    styles.tagContainer,
    t
  ])

  const postCommand = (
    <TouchableOpacity
      style={[
        styles.createPostContainer,
        isPhone ? { maxWidth: 120 } : {}
      ]}
      onPress={() => { handleSubmit() }}
      disabled={isDisabled}
    >
      <Text
        bold
        size="14px"
        marg='0 2px'
        numberOfLines={1}
        adjustsFontSizeToFit
        col="white"
      >{t('common.post')}</Text>
    </TouchableOpacity>
  )

  const commands = useMemo(() => {
    return (
      <View style={styles.commandsContainer}>
        <View style={styles.photoTagContainer}>
          {photoVideoCommand}
          {tagCommand}
        </View>
        <View>
          {postCommand}
        </View>
      </View>
    )
  }, [
    styles.commandsContainer,
    styles.photoTagContainer,
    photoVideoCommand,
    tagCommand,
    postCommand,
  ])

  const mediaItems = useMemo(() => (
    <View>
      <ScrollView horizontal>
        {media.map((item, index) =>
          <View key={index}>
            <MediaItem
              index={index}
              item={item}
              handleRemove={handleRemove}
            />
          </View>
        )}
      </ScrollView>
    </View>
  ), [media])

  const mediaPicker = useMemo(() => (
    <MediaPicker
      entityType='startChat'
      media={media}
      setMedia={(media) => {
        setMedia(media)
      }}
      notAddScreen
      visible={showMediaPicker}
      closeModal={() => showPicker(false)}
    />
  ), [showMediaPicker, setMedia, media, showPicker])

  const tagModal = useMemo(() => (
    <TagModal
      visible={showTagModal}
      closeModal={() => setShowTagModal(false)}
      tagged={tagged}
      handleSelect={setTagged}
    />
  ), [tagged, showTagModal, setTagged, setShowTagModal])

  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: 'white', paddingTop: insets.top }}
      onStartShouldSetResponder={() => Keyboard.dismiss()}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'android' ? 'height' : 'padding'}
        bounces={false}
        style={{ flex: 1 }}
      >
        <View style={{ flex: 1 }}>
          {header}
          <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
            {textInput}
            <View style={{ flex: 1 }}></View>
          </ScrollView>
          {mediaItems}
          {commands}
        </View>
      </KeyboardAvoidingView>
      {mediaPicker}
      {tagModal}
    </SafeAreaView>
  )
}

export default memo(StartChat)