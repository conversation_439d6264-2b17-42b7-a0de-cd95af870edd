import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'react-redux'
import { useNavigation } from '@react-navigation/native'
import { Row, Text, Modal, Icon, Clickable } from 'components'
import { theme } from 'lib'

const Item = ({ title, icon, onClick }) => {
  function handleClick() {
    setTimeout(() => {
      onClick()
    }, 350)
  }

  return (
    <Clickable
      marg='0 0 4px'
      onClick={handleClick}
    >
      <Row noFlex pad='12px 16px' center>
        <Row noFlex ht='24px' wid='24px' centerAll>
          <Icon type={icon} col={theme.GREY_60} dimensions={18} />
        </Row>

        <Text marg='0 0 0 8px' med>
          {title}
        </Text>
      </Row>
    </Clickable>
  )
}

const Menu = ({ item }) => {
  const [menuVisible, setMenuVisible] = useState(false),
    user = useSelector(state => state.user),
    navigation = useNavigation(),
    { t } = useTranslation()

  const ownerOptions = [
    {
      title: 'Edit chat',
      onClick: showEdit,
      icon: 'edit'
    }
  ]

  function closeModal() {
    setMenuVisible(false)
  }

  function showEdit() {
    closeModal()
    navigation.navigate('EditSportChat', { item })
  }

  if (!user) return null

  return (
    <React.Fragment>
      <Clickable
        pad='0 12px'
        hitSlop={20}
        onClick={() => setMenuVisible(!menuVisible)}
      >
        <Icon
          type='menu'
          col={theme.GREY_50}
          dimensions={16}
        />
      </Clickable>

      <Modal
        visible={menuVisible}
        closeModal={closeModal}
      >
        {item.owner._id === user._id && ownerOptions.map((x, i) => (
          <Item
            {...x}
            key={i}
            closeModal={closeModal}
          />
        ))}
      </Modal>
    </React.Fragment>
  )
}

export default Menu
