import React, { useRef } from 'react'
import { FlatList, View } from 'react-native'
import { useTranslation } from 'react-i18next'
import { Item } from '../../../screens/LetsChatSportsComments/components'

const PreviewComment = ({ commentsPreview, show, post, isLoggedIn = true }) => {
  const { commentsCount } = post,
    { t } = useTranslation(),
    listRef = useRef()

  if (!show || commentsCount < 1) return null

  return (
    <View style={{ marginVertical: 4 }}>
      <FlatList
        ref={listRef}
        contentContainerStyle={{ flexGrow: 0, flex: 0 }}
        data={commentsPreview}
        keyExtractor={(item) => item._id}
        renderItem={(props) => (
          <Item
            {...props}
            previewComment
            post={post}
            isLoggedIn={isLoggedIn}
          />
        )}
      />
    </View>
  )
}

export default PreviewComment
