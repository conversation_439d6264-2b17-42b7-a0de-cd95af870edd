import React, { useCallback, useEffect, useState, useMemo, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { ScrollView, KeyboardAvoidingView, Keyboard, TextInput, TouchableOpacity, Platform, View, SafeAreaView } from 'react-native'
import { useDispatch } from 'react-redux'
import { isTablet } from 'react-native-device-info'
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Header, Text, Icon, Spinner } from 'components'
import { Api, formatText, theme } from 'lib'
import { TagModal } from 'screens/Create/components'
import { handleChangeText } from '../lib'
import { getScale } from 'core';
import { MediaItem } from '../../../screens/Add/Post/components'
import { useNavigation, useRoute } from '@react-navigation/native'

let timer

const EditChat = () => {
  const { t } = useTranslation(),
    { item } = useRoute().params,
    { youtubeId, video } = item,
    [caption, setCaption] = useState(item.caption || ''),
    [tagged, setTagged] = useState(item.tagged || []),
    [loading, setLoading] = useState(false),
    [tagVisible, setTagVisible] = useState(false),
    [metaData, setMetaData] = useState(null),
    [media, setMedia] = useState([]),
    [updatedYoutubeId, setYoutubeId] = useState(null),
    [isCaptionChanged, setIsCaptionChanged] = useState(false),
    textInputRef = useRef(TextInput.prototype),
    isDisabled = !caption && !item.media?.length,
    navigation = useNavigation(),
    insets = useSafeAreaInsets(),
    isPhone = !isTablet(),
    dispatch = useDispatch()

  useEffect(() => {
    handleMediaAttachments(item.caption)
    getTagged()
  }, [])

  const styles = useMemo(() => ({
    textInputContainerStyle: {
      flex: 1,
      padding: 20,
    },
    textInputStyle: {
      fontSize: getScale(16),
      fontFamily: 'CeraPro-Regular',
    },
    commandsContainer: {
      flexDirection: 'row',
      borderTopColor: '#E4E7EC',
      borderTopWidth: 1,
      paddingTop: 10,
      marginVertical: 8,
      paddingHorizontal: 16,
      justifyContent: 'space-between'
    },
    photoTagContainer: {
      flexDirection: 'row',
      flex: isTablet() ? 1 : 0,
    },
    tagContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      borderColor: 'rgba(0, 0, 0, 1)',
      borderRadius: 36,
      borderWidth: 2,
      paddingHorizontal: 10,
      paddingVertical: 2,
      marginLeft: 5,
    },
    createPostContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: isDisabled ? theme.GREY_20 : 'black',
      borderRadius: 36,
      paddingHorizontal: 10,
      minHeight: 30,
      borderColor: isDisabled ? theme.GREY_20 : 'black',
      borderRadius: 36,
      borderWidth: 2,
    }
  }), [isDisabled])


  const getTagged = useCallback(async () => {
    if (item._id) {
      const res = await Api.get(`/sportChat/getTagged/${item._id}`)
      if (!res) return
      setTagged(res)
    }
  }, [item._id, Api, setTagged])


  const handleMediaAttachments = () => {
    if (item.youtubeId) {
      setMedia([
        {
          isMetaData: true,
          uri: `https://img.youtube.com/vi/${item.youtubeId}/0.jpg`,
          index: 0
        }
      ])
    }

    if (item.media.length) {
      setMedia(item.media)
    }
  }

  async function handleSubmit() {
    if (loading) return

    setLoading(true)

    dispatch({
      type: 'LOADING_BUTTON',
      payload: 'navigator'
    })

    const formatted = formatText(caption)
    const tags = formatted.filter(x => x.type === 'tag').map(x => x.text)
    const mentioned = formatted.filter(x => x.type === 'mention').map(x => x.text)

    const payload = {
      ...item,
      sportChatId: item._id,
      caption,
      mentioned,
      tags,
      tagged: tagged.map(x => x._id),
      youtubeId: isCaptionChanged ? updatedYoutubeId : youtubeId
    }

    const res = await Api.put(
      '/sportChat/update',
      payload,
      'navigator'
    )

    setLoading(false)

    dispatch({
      type: 'LOADING_BUTTON',
      payload: null
    })

    if (!res) return

    dispatch({
      type: 'UPDATE_LETS_CHAT_SPORTS_ITEM',
      payload: {
        ...payload,
        mentioned: res.mentioned
      }
    })

    navigation.goBack()
  }

  const handleChangeCaption = async (val) => {
    setIsCaptionChanged(true)

    await handleChangeText({
      setCaption,
      timer,
      setMetaData,
      setMedia,
      setYoutubeId,
      caption,
      setLoading,
      val,
      updateMedia: !item.media.length
    })
  }

  const header = useMemo(() => {
    return (
      <Header hasBack isModal title='Edit Chat' paddingTop={Platform.OS === 'android' ? 0 : 1} />
    )
  }, [])

  const textInput = useMemo(() => {
    return (
      <TouchableOpacity
        activeOpacity={1}
        style={styles.textInputContainerStyle}
        onPress={() => textInputRef.current.focus()}
      >
        <TextInput
          ref={textInputRef}
          placeholder={t('letsChatSports.writeTopic')}
          placeholderTextColor={'rgba(0, 0, 0, 0.5)'}
          style={styles.textInputStyle}
          value={caption}
          multiline
          keyboardType='ascii-capable'
          onChangeText={(e) => { handleChangeCaption(e) }}
        />
      </TouchableOpacity>
    )
  }, [
    styles.textInputContainerStyle,
    textInputRef,
    t,
    styles.textInputStyle,
    handleChangeText,
    caption
  ])

  const mediaItems = useMemo(() => {
    return (
      <View>
        <ScrollView horizontal>
          {media.map((mediaItem, index) =>
            <View key={index}>
              <MediaItem
                index={index}
                item={mediaItem}
                noRemove
              />
            </View>
          )}
        </ScrollView>
      </View>
    )
  }, [media])

  const tagCommand = useMemo(() => (
    <TouchableOpacity
      style={[
        styles.tagContainer,
        isPhone ? { maxWidth: 80 } : {}
      ]}
      onPress={() => setTagVisible(true)}
    >
      <Icon type='people' dimensions={15} />
      <Text
        bold
        size='14px'
        marg='0 5px'
        numberOfLines={1}
        adjustsFontSizeToFit
      >{t('add.tag')}</Text>
    </TouchableOpacity>
  ), [
    styles.tagContainer,
    t
  ])

  const postCommand = (
    <TouchableOpacity
      style={[
        styles.createPostContainer,
        isPhone ? { maxWidth: 120 } : {}
      ]}
      onPress={() => { handleSubmit() }}
      disabled={isDisabled || loading}
    >
      <Text
        bold
        size='14px'
        marg='0 2px'
        numberOfLines={1}
        adjustsFontSizeToFit
        col='white'
      >{t('common.save')}</Text>
    </TouchableOpacity>
  )

  const commands = useMemo(() => {
    return (
      <View style={styles.commandsContainer}>
        <View style={styles.photoTagContainer}>
          {tagCommand}
        </View>
        <View>
          {postCommand}
        </View>
      </View>
    )
  }, [
    styles.commandsContainer,
    styles.photoTagContainer,
    tagCommand,
    postCommand
  ])

  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: 'white', paddingTop: insets.top }}
      onStartShouldSetResponder={() => Keyboard.dismiss()}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'android' ? 'height' : 'padding'}
        bounces={false}
        style={{ flex: 1 }}
      >
        <View style={{ flex: 1 }}>
          {header}
          <ScrollView>
            {textInput}
          </ScrollView>
          {mediaItems}
          {commands}
        </View>
      </KeyboardAvoidingView>
      <TagModal
        visible={tagVisible}
        closeModal={() => setTagVisible(false)}
        tagged={tagged}
        handleSelect={setTagged}
        postId={item._id}
      />
    </SafeAreaView>
  )
}

export default EditChat
