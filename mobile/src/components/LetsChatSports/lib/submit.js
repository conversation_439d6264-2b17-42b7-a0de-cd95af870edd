import { CommonActions } from '@react-navigation/native'
import { mediaCompressor } from 'core';
import { formatText, Api, uploadWithSignedUrl } from 'lib'
import mime from 'mime'
import { createThumbnail } from 'react-native-create-thumbnail'

let dispatch,
  caption,
  metaData,
  video,
  youtubeId,
  setLoading,
  tagged,
  aspectRatio,
  subscriptionOnly,
  navigation,
  media,
  user,
  isAndroid,
  documentId,
  dimensions

const submit = async (params) => {
  ({
    dispatch,
    caption,
    metaData,
    video,
    youtubeId,
    setLoading,
    tagged,
    saveShareWith,
    aspectRatio,
    subscriptionOnly,
    navigation,
    media,
    user,
    isAndroid,
    documentId,
    dimensions,
    navigateAfterPost
  } = params
  );

  dispatch({
    type: 'LOADING_BUTTON',
    payload: 'navigator'
  })

  const formatted = formatText(caption)
  const tags = formatted.filter(x => x.type === 'tag').map(x => x.text)
  const mentioned = formatted.filter(x => x.type === 'mention').map(x => x.text)

  let res

  //upload metadata post
  if (metaData) {
    res = await postMetaData(tags, mentioned)
  }

  // Upload image post
  else if (!youtubeId && !video && !metaData) {
    res = await postPhoto(tags, mentioned)
  }

  // Upload youtube post
  else if (youtubeId && !video) {
    res = await postYoutubeLink(tags, mentioned)
  }

  // Upload video post
  else if (video && !youtubeId) {
    res = await videoPost(tags, mentioned)
  }

  setLoading(false)

  dispatch({
    type: 'LOADING_BUTTON',
    payload: null
  })

  if (!res) return

  dispatch({
    type: 'ADD_LETS_CHAT_SPORTS_ITEM',
    payload: {
      ...res,
      owner: user
    }
  })

  navigation.dispatch(
    CommonActions.navigate({
      name: navigateAfterPost || 'Home'
    })
  )
}

const postMetaData = async (tags, mentioned) => {
  const data = {
    tagged: tagged.map(x => x._id),
    caption,
    tags,
    mentioned,
    metaData,
    aspectRatio,
    subscriptionOnly
  }

  const res = await Api.post('/sportChat/create', data)
  return res
}

const postYoutubeLink = async (tags, mentioned) => {
  const res = await Api.post(
    '/sportChat/create',
    {
      youtubeId,
      caption,
      tags,
      tagged: tagged.map(x => x._id),
      subscriptionOnly
    }
  )
  return res;
}

const postPhoto = async (tags, mentioned) => {
  const mapped = await processMediaPhotos()
  const newMedia = await generateUploadedPhoto(mapped);

  // Data object for API post request
  const data = {
    media: newMedia,
    tagged: tagged.map(x => x._id),
    caption,
    aspectRatio,
    tags,
    mentioned,
    subscriptionOnly
  }

  res = await Api.post('/sportChat/create', data)
  return res
}

const processMediaPhotos = async () => {
  return await Promise.all(media.map(async x => {
    if (x.originalPath) return x

    const imgPath = await mediaCompressor.getPath(x.uri || x, 'image');
    const compressed = await mediaCompressor.image(
      imgPath,
      dimensions.width,
      dimensions.height,
      0.3
    );

    return {
      uri: compressed,
      type: mime.getType(compressed)
    }
  }))
}

const generateUploadedPhoto = async (mapped) => {
  let newMedia = []

  // Iterate over each item in the mapped array to upload file and get its location
  for (let i = 0; i < mapped.length; i++) {
    const x = mapped[i]

    x.type = mime.getType(x.uri)

    // Generate thumbnail and filename
    const thumbnail = await mediaCompressor.image(x.uri, 216, 216, 1);
    const thumbnailType = mime.getType(thumbnail);
    const fileName = `${documentId}-${i + 1}.${x.type.split('/')[1]}`;
    const thumbnailName = `${documentId}-${i + 1}-thumbnail.${thumbnailType.split('/')[1]}`

    // Prepare data for upload
    let generatedData = {
      uri: fileName,
      thumbnail: thumbnailName
    }

    // Iterage again to create file object for upload
    for await (let key of Object.keys(generatedData)) {
      const name = generatedData[key]

      const file = {
        uri: key === 'uri' ? x.uri : thumbnail,
        type: key === 'uri' ? x.type : thumbnailType,
        name: name
      }

      // Upload file and get location
      const location = await uploadWithSignedUrl(name, file.type, file)
      generatedData[key] = location
    }

    // Push data to newMedia array
    newMedia.push(generatedData)
  }
  return newMedia;
}



const videoPost = async (tags, mentioned) => {
  let realPath = video.realPath

  // Correct video path in android
  if (isAndroid) {
    realPath = realPath.match(/file:\/\//g)?.length > 1 ? realPath.replace('file://', '') : realPath
  }

  // Compress video
  const compressed = await mediaCompressor.video(realPath || video)

  // Generate thumbnail
  const thumbnail = await createThumbnail({
    url: compressed,
    timeStamp: 0,
  })
    .then(res => res)
    .catch(err => { err })

  // Handle error when thumbnail generation fails
  if (thumbnail?.err) return alert(thumbnail.err)

  // Get types for video & thumbnail and generate filenames
  const videoType = mime.getType(compressed),
    thumbnailType = isAndroid ? thumbnail.mime : mime.getType(thumbnail.path),
    fileName = `${documentId}-1.${videoType.split('/')[1]}`,
    thumbnailName = `${documentId}-1-thumbnail.${thumbnailType.split('/')[1]}`

  // Initialize media array with vide and thumbnail data

  const media = await uploadVideoFiles(fileName, thumbnailName, compressed, videoType, thumbnail, thumbnailType)
  // Data object for API post request
  data = {
    media,
    caption,
    tags,
    tagged: tagged.map(x => x._id),
    aspectRatio: video.aspectRatio,
    mentioned,
    subscriptionOnly
  }

  res = await Api.post('/sportChat/create', data)
  return res;
}

const uploadVideoFiles = async (fileName, thumbnailName, compressed, videoType, thumbnail, thumbnailType) => {
  let media = [{
    uri: fileName,
    thumbnail: thumbnailName,
    aspectRatio: video.aspectRatio
  }]

  // Iterate over each file in media array to upload the file and get its location
  for await (let f of media) {
    const keys = Object.keys(f)

    for await (let key of keys) {
      const name = f[key]
      if (key === 'aspectRatio') {
        media[0][key] = video.aspectRatio
        continue
      }

      // Prepare file object for upload
      const file = {
        uri: key === 'uri'
          ? compressed.replace('file://', 'file:///')
          : thumbnail.path,
        type: key === 'uri' ? videoType : thumbnailType,
        name: name,
      }

      // Upload file and get location
      const location = await uploadWithSignedUrl(name, file.type, file)
      media[0][key] = location
    }
  }

  return media;
}

export default submit