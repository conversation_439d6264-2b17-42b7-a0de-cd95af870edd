import { Api, getYtId } from 'lib'

let setCaption,
  timer,
  setMetaData,
  setMedia,
  setYoutubeId,
  caption,
  setLoading,
  val

const handleChangeText = async (params) => {
  ({
    setCaption,
    timer,
    setMetaData,
    setMedia,
    setYoutubeId,
    caption,
    setLoading,
    val,
    updateMedia = true
  } = params)

  clearTimeout(timer)
  setCaption(val)
  setMetaData('')
  setYoutubeId('')

  if (updateMedia) {
    timer = setTimeout(async () => {
      setMedia(media => media.filter(x => !x.isMetaData))
      await processTextChange(val);
    }, 300)
  }
  
}

const processTextChange = async (val) => {
  //check if url
  const regexp = /(ftp|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/
  const isURL = regexp.test(val)

  if (!isURL) return null

  // Guard for initial delete button press while deleting and holding.
  // Maybe we can extend this for some edgecases?
  if (caption && caption.includes(val)) return

  setLoading(true)

  //check if youtube url
  const isYoutube = getYtId(val)

  if (isYoutube) {
    handleYoutubeUrl(isYoutube)
  } else {
    // disabled in sport chat for now
    // await fetchMetaData(val)
  }
  setLoading(false)
}

const handleYoutubeUrl = (isYoutube) => {
  setMetaData(null)
  setYoutubeId(isYoutube)
  setMedia([{
    isMetaData: true,
    uri: `https://img.youtube.com/vi/${isYoutube}/0.jpg`,
    index: 0
  }])
}

const fetchMetaData = async (val) => {
  const fetchedMetaData = await Api.post('/posts/getMetaData', { link: val })

  if (!fetchedMetaData) return null

  setYoutubeId(null)
  setMedia([{
    isMetaData: true,
    uri: fetchedMetaData.image || fetchedMetaData.icon,
    index: 0
  }])
  setMetaData(fetchedMetaData)
}


export default handleChangeText