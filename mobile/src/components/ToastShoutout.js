import React, {forwardRef, useRef, useState, useImperativeHandle, useEffect, memo} from 'react'
import {View, Animated, StyleSheet, Platform} from 'react-native';
import { Button, Text, Icon } from 'components'
import styled from 'styled-components'
import { useNavigation } from '@react-navigation/native'
import { useSelector } from 'react-redux'
import { Swipeable } from 'react-native-gesture-handler';

const ToastShoutout = forwardRef((props, ref) => {
    const animatedValue = useRef(new Animated.Value(0)).current,
     [displayToast, setDisplayToast] = useState(false),
     [message, setMessage] = useState('Success!'),
     navigation = useNavigation(),
     user = useSelector(state => state.user),
     isAthlete = user.type === 'athlete',
     [type, setType] = useState('completed'),
     updateRef = useRef()

    const closeToast = () => {
        setTimeout(() => {
            Animated.timing(animatedValue, {
                toValue: 0,
                duration: 350,
                useNativeDriver: false,
            }).start(() => {
                setDisplayToast(false);
            });
        }, 5000);
    };

    const showToast = (message, type) => {
        if (displayToast) return;
        // setToastType(message, type);
        setDisplayToast(true);
        Animated.timing(animatedValue, {
            toValue: 1,
            duration: 350,
            useNativeDriver: false,
        }).start(closeToast);
    };

    let animation = animatedValue.interpolate({
        inputRange: [0, 0.3, 1],
        outputRange: [-100, -10, 0],
    });

    //Commented out for future use if needed
    // useImperativeHandle(ref, () => ({
    //     success(message) {
    //         showToast(message, 'success');
    //     },
    //     error(message) {
    //         showToast(message, 'error');
    //     },
    // }));

    // const setToastType = (message = 'Success!', type = 'success') => {
    //     let color;
    //     let textColorValue;
    //     if (type == 'error') {
    //         color = 'red';
    //         textColorValue = 'white';
    //     }
    //     if (type == 'success') {
    //         color = 'green';
    //         textColorValue = 'black';
    //     }
    //     setMessage(message);
    //     setToastColor(color);
    //     setTextColor(textColorValue);
    // };

    useEffect(() => {
        showToast()
    },[])

    const Circle = styled.View`
        width: 40px;
        height: 40px;
        border-radius: 60px;
        align-items: center;
        justify-content: center;
        background-color: #fff;
    `

    const redirect = () => {
        setDisplayToast(false)
        switch (user.type) {
            case 'athlete':
                navigation.navigate('RequestList', { isYourRequest: false, isListDisplayed: true, fromRecording: false, fromNotif: true })
                break;
        
            default:
                if(type === 'completed'){
                    navigation.navigate('RequestVideoGallery')
                } else {
                    navigation.navigate('RequestList', { isYourRequest: true, isListDisplayed: true, fromRecording: false, fromNotif: true })
                }
                break;
        }
    }

    const athleteContent = () => {
        return (
            <View style={{ marginHorizontal:20 }}>
                <Text b1 bold col='white'>P4P is Monetizing Your Brand!</Text>
                <Text b2 col='white'>A Fan is Requesting a Shoutout.</Text>
            </View>
        )
    }

    const fanContent = () => {
        return (
            <View style={{ marginHorizontal:40 }}>
                {type === 'completed' ? 
                <>
                    <Text b1 bold col='white'>Congratulations!</Text>
                    <Text b2 col='white'>Your favorite athlete has completed your shoutout.</Text>
                </>
                :
                <Text b2 col='white'>Sorry your favorite athlete is currently unavailable. Please try again later or select another player.</Text>
                }
            </View>
        )
    }

    const renderAction = () => {
        return (
          <Animated.View style={{ flex: 1, transform: [{ translateX: 0 }] }}>
          </Animated.View>
        );
      };

    return displayToast ? (
        <Animated.View style={[styles.container, {backgroundColor: type === 'completed' ? '#FB3351' : '#7989A0' , transform: [{translateY: animation}]}]}>
            <Swipeable
                ref={updateRef}
                friction={2}
                leftThreshold={30}
                rightThreshold={40}
                renderLeftActions={() => renderAction()}
                renderRightActions={() => renderAction()}
                onSwipeableWillOpen={() => setDisplayToast(false)}
            >
                <View style={styles.row}>
                    <View>
                        <Circle>
                            <Icon type={'megaphone'} col={type === 'completed' ? '#FB3351' : '#7989A0'}/>
                        </Circle>
                    </View>
                    {isAthlete && athleteContent()}
                    {!isAthlete && fanContent()}
                    <View style={{ width:60 }}>
                        <Button bg='#fff' small col='white' textColor='#000' text='View' onClick={() => redirect()} />
                    </View>
                </View>
            </Swipeable>
        </Animated.View>
    ) : null;
});

export const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        top: Platform.OS === 'ios' ? 60 : 35,
        minHeight: 75,
        width:'100%',
        zIndex: 1,
        padding: 14,
        shadowColor: '#000',
        shadowOffset: {
            width: 4,
            height: 5,
        },
        shadowOpacity: 0.1,
        shadowRadius: 6.27,
        elevation: 10,
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-around',
        marginHorizontal: 15
    },
});

export default memo(ToastShoutout)