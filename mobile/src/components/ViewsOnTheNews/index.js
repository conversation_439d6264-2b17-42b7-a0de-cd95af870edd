import React, { useMemo } from 'react'
import { View, Dimensions } from 'react-native'
import { Text, Col, Clickable } from 'components'
import { theme } from 'lib'
import { useTranslation } from 'react-i18next'
import { useNavigation } from '@react-navigation/native'
import ViewsOnTheNewsCarousel from '../ViewsOnTheNewsCarousel'

function ViewsOnTheNews({ data }) {
  const { t } = useTranslation(),
    navigation = useNavigation(),
    windowWidth = Dimensions.get('window').width,
    widthThreshold = 370

  const styles = useMemo(() => ({
    headerStyles: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingVertical: 12,
      marginTop: 12
    }
  }))

  function handleClick() {
    navigation.navigate('ViewsOnTheNews')
  }

  return (
    <View>
      <View style={styles.headerStyles}>
        <View style={{ flex: 1, paddingRight: 8 }}>
          <Text
            h2={windowWidth >= widthThreshold}
            h3={windowWidth < widthThreshold}
            adjustsFontSizeToFit
            numberOfLines={1}
          >
            Your Views on the News
          </Text>
        </View>
        <View>
          <Clickable onClick={handleClick}>
            <Text b2 bold>{t('common.seeAll')}</Text>
          </Clickable>
        </View>
      </View>

      {
        data?.length ?
          <ViewsOnTheNewsCarousel
            letsChatSportsData={data}
          /> :
          <Col noFlex center marg='16px'>
            <Text col={theme.GREY_60}>{t('chat.noResults')}</Text>
          </Col>
      }
    </View>
  )
}

export default ViewsOnTheNews