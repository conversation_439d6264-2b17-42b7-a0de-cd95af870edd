import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs'
import Home from 'screens/Home'
import Profile from 'screens/Profile';

const Stack = createNativeStackNavigator();


const FeedStackNavigator = () => {
  const tabBarHt = useBottomTabBarHeight();

  return (
    <Stack.Navigator
      screenOptions={{ headerShown: false }}
    >
      <Stack.Screen
        name="HomeFeed"
        children={props => <Home {...props} tabBarHt={tabBarHt} />}
      />
      <Stack.Screen name="AppProfile" component={Profile} />
    </Stack.Navigator>
  );
};

export default FeedStackNavigator;