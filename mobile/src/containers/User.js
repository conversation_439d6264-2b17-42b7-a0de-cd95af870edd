import React, { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigation } from '@react-navigation/native'
import messaging from '@react-native-firebase/messaging'
import { createStackNavigator, TransitionPresets } from '@react-navigation/stack'
import OnboardingHandler from './OnboardingHandler'
import { ws, loginWithToken } from 'lib'
import { redirectPushNotif, registerDevice } from 'core'
import { Spinner, Col, NetworkInfo, VideoPreview, ToastShoutout } from 'components'

import AddNavigator from './AddNavigator'
import Profile from 'screens/Profile'
import Add from 'screens/Add'
import Comments from 'screens/Comments'
import Notifications from 'screens/Notifications'
import PostsFeed from 'screens/PostsFeed'
import Chat from 'screens/Chat'
import Shoutouts from 'screens/Shoutouts'
import Stories from 'screens/Stories'
import FollowersAndFollowing from 'screens/FollowersAndFollowing'
import ConnectsDashboard from 'screens/ConnectsDashboard'
import Connect from 'screens/Connect'
import Repost from 'screens/Repost'
import AddShoutout from 'screens/AddShoutout'
import Settings from 'screens/Settings'
import { NewApp, AllUsers } from 'screens/Browse/screens'
import { UpdatePaymentWarning } from 'screens/Billing/screens'
import { VerifyEmail, CheckInbox } from 'screens/ValidateEmail/screens'
import IdVerification from 'screens/IdVerification'
import Game from 'screens/Game'
import SubscriptionStarted from 'screens/SubscriptionStarted'
import { Capture, CapturedMediaView } from 'screens/Capture/screens'
import EditStory from 'screens/Add/Story/EditStory'
import PermissionBlocked from 'screens/PermissionBlocked'
import Billing from 'screens/Billing'
import ShoutoutConfirmation from 'screens/Add/Shoutout/screens/ShoutoutConfirmation'
import CreateMatchDayShoutout from '../screens/CreateMatchDayShoutout'
import ShoutoutsHome from '../screens/ShoutoutsHome'
import ShoutoutsFromAthlete from '../screens/ShoutoutsFromAthletes'
import { SharePostsList } from '../screens/MediaShare/screens'
import MediaShare from '../screens/MediaShare'
import UpdateApp from '../screens/UpdateApp'

import Live from 'screens/Live'
import WatchTest from 'screens/WatchTest'
import LetsChatSports from 'screens/LetsChatSports'

import ViewsOnTheNews from 'screens/ViewsOnTheNews'

import {
  Privacy,
  ScheduleSettings,
  ChangePassword,
  LanguageAndCurrency,
  Subscriptions,
  ContactUs
} from 'screens/Settings/screens'
import {
  ScheduleConnect,
  Checkout,
  Confirmation,
  SelectAthlete
} from 'screens/ScheduleConnect'
import {
  Messages,
  AddChat,
  GroupInitialize
} from 'screens/Messages'
import {
  CancelConnect,
  RefuseConnect,
  EndedConnect,
  ConfirmConnect
} from 'screens/ConnectConfirmations'
import {
  VerifyIdPopup,
  RequestedPopup,
  IdVerifiedPopup
} from 'screens/IdVerification/screens'
import {
  ProductDetails,
  Cart,
  OrderSuccessful,
  ProductConfirmation,
  Products
} from 'screens/Store/screens'
import {
  SelectAthleteRequest,
  RequestShoutout,
  RequestFor,
  RequestInput,
  BookNow,
  BackToShoutout,
  RequestReview

} from 'screens/RequestShoutout/screens'
import { UserList, Summary, SubscribeModal } from 'screens/Subscribe/screens'
import { ComingSoon } from '../screens/Home/components'
import Create from '../screens/Create'
import { RequestVideoGallery, RequestList, RequestSelection, Record } from '../screens/Requests/components'
import { EditChat, StartChat } from '../components/LetsChatSports/components'
import LetsChatSportsComments from '../screens/LetsChatSportsComments'
import ViewsOnTheNewsComments from '../screens/ViewsOnTheNewsComments'

const Stack = createStackNavigator()

const mainStackOptions = {
  headerShown: false,
  ...TransitionPresets.SlideFromRightIOS
}

const mainStackOptionsSlideInBottom = {
  headerShown: false,
  ...TransitionPresets.ModalSlideFromBottomIOS
}

const User = ({ }) => {
  const [loginDone, setLoginDone] = useState(false),
    [renderConnect, setRenderConnect] = useState(false),
    user = useSelector(state => state.user),
    homeData = useSelector(state => state.home),
    { loadingButton, startingConnectId, activeConnect } = useSelector(state => state.appState),
    connects = useSelector(state => state.connects),
    navigation = useNavigation(),
    dispatch = useDispatch(),
    showToast = useSelector(state => state.shoutoutRequest?.showShoutoutRequestToast)

  /*
    Remove any connect related data
  */
  useEffect(() => {
    dispatch({
      type: 'UPDATE_APP_STATE',
      payload: { startingConnectId: null, activeConnect: null }
    })

    dispatch({ type: 'INITIAL_STREAMING_STATE' })
    dispatch({ type: 'SET_BROWSE', payload: {} })
    dispatch({
      type: 'UPDATE_APP_STATE',
      payload: { startingConnectId: null, reactNative: true }
    })

    ws.connect()
    loginWithToken(true)

    registerDevice()

    messaging()
      .getInitialNotification()
      .then(data => redirectPushNotif(data, navigation, user))

    messaging().onNotificationOpenedApp(data => {
      setTimeout(() => {
        redirectPushNotif(data, navigation, user)
      }, 1000)
    })
  }, [])

  useEffect(() => {
    setTimeout(() => setRenderConnect(true), 500)
  }, [activeConnect])

  /*
    If a connect is starting, redirects user to confirmation screen
  */
  useEffect(() => {
    setTimeout(() => {
      if (!user.onboarded && user.type !== 'team' || !homeData) return

      const connect = connects?.find(x => x._id === startingConnectId)
      if (!connect) return

      navigation.navigate('ConfirmConnect', { user, connect })
      dispatch({
        type: 'UPDATE_APP_STATE',
        payload: { startingConnectId: null }
      })
    }, 300)

  }, [startingConnectId])

  if (!user.onboarded && user.type !== 'team') {
    return <OnboardingHandler user={user} />
  }

  if (!homeData) {
    return (
      <Col bg='#fff'>
        <Spinner fullScreen />
      </Col>
    )
  }

  return (
    <React.Fragment>
      <NetworkInfo />
      {activeConnect && <Connect />}

      <Stack.Navigator
        initialRouteName='Tab'
      >
        <Stack.Screen
          name='AddNavigator'
          component={AddNavigator}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='StackProfile'
          component={Profile}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='MediaShare'
          component={MediaShare}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='SharePostsList'
          component={SharePostsList}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='Comments'
          component={Comments}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='PostsFeed'
          component={PostsFeed}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='Add'
          component={Add}
          options={mainStackOptionsSlideInBottom}
        />
        {
          /*
          <Stack.Screen
            name='EditMedia'
            component={EditMedia}
            options={mainStackOptions}
          />
          */
        }
        <Stack.Screen
          name='Create'
          component={Create}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='CreateMatchDayShoutout'
          component={CreateMatchDayShoutout}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='EditStory'
          component={EditStory}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='Notifications'
          component={Notifications}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='Messages'
          component={Messages}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='Chat'
          component={Chat}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='AddChat'
          component={AddChat}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='Shoutouts'
          component={Shoutouts}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='GroupInitialize'
          component={GroupInitialize}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='ScheduleSettings'
          component={ScheduleSettings}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='ScheduleConnect'
          component={ScheduleConnect}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='Checkout'
          component={Checkout}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='Confirmation'
          component={Confirmation}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='SelectAthlete'
          component={SelectAthlete}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='ConnectsDashboard'
          component={ConnectsDashboard}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='CancelConnect'
          component={CancelConnect}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='RefuseConnect'
          component={RefuseConnect}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='ConfirmConnect'
          component={ConfirmConnect}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='EndedConnect'
          component={EndedConnect}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='FollowersAndFollowing'
          component={FollowersAndFollowing}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='Repost'
          component={Repost}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='Privacy'
          component={Privacy}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='ChangePassword'
          component={ChangePassword}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='ContactUs'
          component={ContactUs}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='AddShoutout'
          component={AddShoutout}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='ShoutoutConfirmation'
          component={ShoutoutConfirmation}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='Settings'
          component={Settings}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='LanguageAndCurrency'
          component={LanguageAndCurrency}
          options={mainStackOptions}
        />

        {/*
          <Stack.Screen
            name='ShoutoutConfirmation'
            component={ShoutoutConfirmation}
            options={mainStackOptionsSlideInBottom}
          />
        */}

        <Stack.Screen
          name='Subscriptions'
          component={Subscriptions}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='NewApp'
          component={NewApp}
          options={{
            ...mainStackOptionsSlideInBottom,
            gestureEnabled: false
          }}
        />

        <Stack.Screen
          name='AllUsers'
          component={AllUsers}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='UpdatePaymentWarning'
          component={UpdatePaymentWarning}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='Game'
          component={Game}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='VerifyIdPopup'
          component={VerifyIdPopup}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='IdVerification'
          component={IdVerification}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='RequestedPopup'
          component={RequestedPopup}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='IdVerifiedPopup'
          component={IdVerifiedPopup}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='Stories'
          component={Stories}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='VerifyEmail'
          component={VerifyEmail}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='CheckInbox'
          component={CheckInbox}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='Capture'
          component={Capture}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='CapturedMediaView'
          component={CapturedMediaView}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='SubscriptionStarted'
          component={SubscriptionStarted}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='PermissionBlocked'
          component={PermissionBlocked}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='ProductDetails'
          component={ProductDetails}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='ProductConfirmation'
          component={ProductConfirmation}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='Cart'
          component={Cart}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='OrderSuccessful'
          component={OrderSuccessful}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='Live'
          component={Live}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='WatchTest'
          component={WatchTest}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='Products'
          component={Products}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='SubscribeUserList'
          component={UserList}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='SubscribeSummary'
          component={Summary}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='SubscribeModal'
          component={SubscribeModal}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='Billing'
          component={Billing}
          options={mainStackOptionsSlideInBottom}
        />
        <Stack.Screen
          name='SelectAthleteRequest'
          component={SelectAthleteRequest}
          options={mainStackOptions}
        />
        <Stack.Screen
          name='RequestShoutout'
          component={RequestShoutout}
          options={mainStackOptions}
        />
        <Stack.Screen
          name='RequestFor'
          component={RequestFor}
          options={mainStackOptions}
        />
        <Stack.Screen
          name='RequestInput'
          component={RequestInput}
          options={mainStackOptions}
        />
        <Stack.Screen
          name='BookNow'
          component={BookNow}
          options={mainStackOptions}
        />
        <Stack.Screen
          name='BackToShoutout'
          component={BackToShoutout}
          options={mainStackOptions}
        />
        <Stack.Screen
          name='RequestReview'
          component={RequestReview}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='ShoutoutsHome'
          component={ShoutoutsHome}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='ShoutoutsFromAthlete'
          component={ShoutoutsFromAthlete}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='VideoPreview'
          component={VideoPreview}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='RequestVideoGallery'
          component={RequestVideoGallery}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='RequestList'
          component={RequestList}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='RequestSelection'
          options={mainStackOptionsSlideInBottom}
        >
          {(props) => <RequestSelection {...props} isListDisplayed={false} />}
        </Stack.Screen>

        <Stack.Screen
          name='Record'
          component={Record}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='StartChat'
          component={StartChat}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='EditSportChat'
          component={EditChat}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='LetsChatSports'
          component={LetsChatSports}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='LetsChatSportsComments'
          component={LetsChatSportsComments}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='ViewsOnTheNews'
          component={ViewsOnTheNews}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='ViewsOnTheNewsComments'
          component={ViewsOnTheNewsComments}
          options={mainStackOptions}
        />


        <Stack.Screen
          name='UpdateApp'
          component={UpdateApp}
          options={mainStackOptions}
        />

      </Stack.Navigator>

      {loadingButton === 'navigator' && <Spinner fullScreen />}
      <ComingSoon />
      {showToast && <ToastShoutout />}
    </React.Fragment>
  )
}

export default User
