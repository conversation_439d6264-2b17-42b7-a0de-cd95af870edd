import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import Profile from 'screens/Profile';
import Browse from 'screens/Browse'

const Stack = createNativeStackNavigator();

const BrowseStackNavigator = () => {

  return (
    <Stack.Navigator
      screenOptions={{ headerShown: false }}
    >
      <Stack.Screen name="BrowseNav" component={Browse} />
      <Stack.Screen name="AppProfile" component={Profile} />
    </Stack.Navigator>
  );
};

export default BrowseStackNavigator;