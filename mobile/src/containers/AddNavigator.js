import React, { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs'
import TabNavigator from './TabNavigator'
import Add from 'screens/Add'
import Create from 'screens/Create'

/*
  A navigation wrapper that uses createMaterialTopTabNavigator's
  swipe to navigate handler to navigate to "Add" on swipe left
*/

const Tab = createMaterialTopTabNavigator()

const AddNavigator = ({ }) => {
  const { isLive, addSwipeEnabled } = useSelector(state => state.appState)
  const user = useSelector(state => state?.user)
  const isLoggedIn = !!user?._id

  return (
    <Tab.Navigator
      tabBarPosition='bottom'
      screenOptions={{ swipeEnabled: !isLive && !!addSwipeEnabled }}
      initialRouteName='TabNavigator'
      tabBar={() => null}
    >
      {isLoggedIn &&
        <Tab.Screen
          name='Create'
          component={Create}
          initialParams={{ fromAddNavigator: true }}
        />
      }


      <Tab.Screen
        name='TabNavigator'
        component={TabNavigator}
      />

      {isLoggedIn &&
        <Tab.Screen
          name='Add'
          component={Add}
          initialParams={{ fromAddNavigator: true }}
        />
      }
      
    </Tab.Navigator>

  )
}

export default AddNavigator
