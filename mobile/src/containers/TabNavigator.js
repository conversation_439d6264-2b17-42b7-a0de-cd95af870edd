import React, { useEffect, useCallback, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Platform } from 'react-native'
import { useTranslation } from 'react-i18next'
import { getPaddings } from 'core'
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs'
import { NavigationElem } from 'components'
import { Api } from 'lib'
import { useNavigation } from '@react-navigation/native';


import { Store } from 'screens/Store/screens'
import Requests from '../screens/Requests'
import AdPlayaz from '../screens/AdPlayaz'
import { useRoute, getFocusedRouteNameFromRoute } from '@react-navigation/native';

import DashboardStackNavigator from './DashboardStackNavigator';
import FeedStackNavigator from './FeedStackNavigator';
import BrowseStackNavigator from './BrowseStackNavigator'


const Tab = createBottomTabNavigator()

const TabNavigator = ({ navigation }) => {
  const user = useSelector(state => state?.user),
    [isFetching, setIsFetching] = useState(false),
    hasDashboard = ['athlete', 'team'].includes(user?.type),
    isAndroid = Platform.OS === 'android',
    dispatch = useDispatch(),
    { t } = useTranslation(),
    isLoggedIn = !!user?._id


  const baseHeight = isAndroid ? 68 : 55
  const tabBarHeight = getPaddings().bottom + baseHeight

  const [showDashboard, setShowDashboard] = useState(false);

  useEffect(() => {
    /*
      Show new app prompt screen
    */
    if(!user?.browseTutorialDone && isLoggedIn) {
      setTimeout(() => navigation.navigate('NewApp'), 300)
    }
  }, [])

  useEffect(() => {
    const idVerificationProgress = user?.verified?.idProgress
    if(idVerificationProgress !== 'verified') return

    navigation.navigate('IdVerifiedPopup')
  }, [user?.verified])

  const navigateToComingSoon = useCallback((navigation, e, title) => {
    if (!navigation.isFocused()) {
      e.preventDefault()
      dispatch({
        type: 'SHOW_COMING_SOON',
        payload: {
          comingSoonDetails: {
            title,
            visible: true,
          }
        },
      })
    }
  }, [])

  const getUnreadNotifications = async () => {
    if (isFetching || !isLoggedIn) return

    setIsFetching(true)
    const res = await Api.get('/notifications/getUnread')

    setIsFetching(false)

    if (!res) return

    dispatch({
      type: 'NEW_NOTIFICATIONS',
      payload: res
    })

    dispatch({
      type: 'UPDATE_NOTIF_COUNT',
      payload: res.length
    })
  }

  function resetToTabIfNotCurrent(navigation, tabName) {
    const navState = navigation.getState();
    const currentScreen = getActiveRouteName(navState);


    if (currentScreen !== tabName) {
      navigation.reset({
        index: 0,
        routes: [{ name: tabName }],
      });
    }
  }

  function getActiveRouteName(state) {
    if (!state || !state.routes || state.routes.length === 0) return null;

    const route = state.routes[state.index];
    if (route.state) {
      return getActiveRouteName(route.state);
    }
    return route.name;
  }

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarStyle: { height: tabBarHeight },
        headerShown: false,
        tabBarHideOnKeyboard: true

      }}
    >
      {hasDashboard && (
        <Tab.Screen
          name='Dashboard'
          component={DashboardStackNavigator}
          listeners={({ navigation }) => ({
            tabPress: e => {
              getUnreadNotifications()
              setShowDashboard(true)
              resetToTabIfNotCurrent(navigation, 'DashboardNav');
            }
          })}
          options={{
            tabBarButton: props => (
              <NavigationElem
                {...props}
                accessibilityLabel={'Dashboard'}
                icon='dashboard'
                focused={showDashboard}
              />
            )
          }}
        />
      )}

      <Tab.Screen
        name='Home'
        component={FeedStackNavigator}
        listeners={({ navigation, route }) => ({
          tabPress: (e) => {
            getUnreadNotifications()
            if (navigation.isFocused()) {
              e.preventDefault()

              navigation.setParams({
                ...route.params,
                scrollToTop: true
              })
            }
            resetToTabIfNotCurrent(navigation, 'Home');
          },
        })}
        options={{
          tabBarButton: props => <NavigationElem {...props} accessibilityLabel={'Feed'} icon='home' focused={true} />
        }}
      />

      <Tab.Screen
        name='Browse'
        component={BrowseStackNavigator}
        listeners={({ navigation, route }) => ({
          tabPress: (e) => {
            getUnreadNotifications()
            if (navigation.isFocused()) {
              e.preventDefault()

              navigation.setParams({
                ...route.params,
                scrollToTop: true
              })
            }
            resetToTabIfNotCurrent(navigation, 'Browse');
          },
        })}
        options={{
          tabBarButton: props => <NavigationElem {...props} accessibilityLabel={'Browse'} icon='browse' focused={true}/>
        }}
      />

      <Tab.Screen
        name='Requests'
        component={Requests}
        listeners={({ navigation, route }) => ({
          tabPress: (e) => {
            if (!isLoggedIn) {
              e.preventDefault()
              dispatch({
                type: 'SHOW_PROMPT_MODAL',
                payload: {
                  isPromptModalVisible: true,
                },
              });
              return;
            }
            navigation.setParams({ ...route.params, isListDisplayed: false })
          },
        })}
        options={{
          tabBarButton: props => <NavigationElem {...props} accessibilityLabel={'Requests'} icon='requests' focused={true}/>,
          tabBarStyle: { display: 'none' }
        }}
      />

      <Tab.Screen
        name='Store'
        component={Store}
        listeners={({ navigation, route }) => ({
          tabPress: (e) => {
            navigateToComingSoon(navigation, e, 'Store')
          },
        })}
        options={{
          tabBarButton: props => <NavigationElem {...props} accessibilityLabel={'Store'} icon='store' focused={true}/>
        }}
      />

        <Tab.Screen
          name='AdPlayaz'
          component={AdPlayaz}
          listeners={({ navigation, route }) => ({
            tabPress: (e) => {
              navigateToComingSoon(navigation, e, 'AdPlayaz')
            },
          })}
          options={{
            tabBarButton: props => <NavigationElem {...props} accessibilityLabel={'AdPlayaz'} icon='adPlayaz' focused={true}/>
          }}
        />
    </Tab.Navigator>
  )
}

export default TabNavigator
