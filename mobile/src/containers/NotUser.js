import React, { useState, useEffect, useMemo } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { useTranslation } from 'react-i18next'
import { getLanguage, getCalculatedText } from 'core'
import { createStackNavigator, TransitionPresets } from '@react-navigation/stack'
import { Spinner, NetworkInfo, Col, VideoPreview, PromptAuthModal } from 'components'

import SignUp from 'screens/Auth/SignUp'
import Login from 'screens/Auth/Login'
import AccountLocked from 'screens/AccountLocked'
import PostsFeed from 'screens/PostsFeed'
import {
  CreateNewPassword,
  RecoveryEmailSent,
  NoUserFound,
  ForgotPassword
} from 'screens/ForgotPassword/screens'
import {
  MigratedUser,
  ValidationEmailSent,
  VerifyEmail
} from 'screens/ValidateEmail/screens'
import LetsChatSportsComments from 'screens/LetsChatSportsComments'
import LetsChatSports from 'screens/LetsChatSports'
import ViewsOnTheNewsComments from 'screens/ViewsOnTheNewsComments'
import Comments from 'screens/Comments'
import Shoutouts from 'screens/Shoutouts'
import Stories from 'screens/Stories'
import ViewsOnTheNews from 'screens/ViewsOnTheNews'
import Profile from 'screens/Profile'
import AddShoutout from 'screens/AddShoutout'
import { ComingSoon } from '../screens/Home/components'
import { RequestShoutout } from 'screens/RequestShoutout/screens'
import CTASignUp from '../screens/Auth/CTASignUp'
import { extractUsernameAndCta, getId } from '../core/redirectLinks'
import CTALogin from '../screens/Auth/CTALogin'
import UpdateApp from '../screens/UpdateApp'
import Home from 'screens/Home'
import { Api } from '../lib'
import { store } from '../store'

import AddNavigator from './AddNavigator'
import Browse from '../screens/Browse'
import { AllUsers } from '../screens/Browse/screens'

const Stack = createStackNavigator()

const mainStackOptions = {
  headerShown: false,
  ...TransitionPresets.SlideFromRightIOS
}

const mainStackOptionsSlideInBottom = {
  headerShown: false,
  ...TransitionPresets.ModalSlideFromBottomIOS
}

const NotUser = ({ initialUrl }) => {
  const { loadingButton } = useSelector(state => state.appState),
    dispatch = useDispatch(),
    { t, i18n } = useTranslation()

  const [initialParams, setInitialParams] = useState({})
  const [isFetchingData, setIsFetchingData] = useState(true)
  const [isFetchingHomeData, setIsFetchingHomeData] = useState(true)
  const isPromptModalVisible = useSelector(state => state.globalModalsState.isPromptModalVisible) || false;
  
  useEffect(() => {
    const init = async () => {
      handleLanguage();
      await fetchHomeData();
      fetchData();
    }
    init();
  }, [])

  const fetchHomeData = async () => {
    const res = await Api.get('/public/home/<USER>')

    if (!res) {
      setIsFetchingHomeData(false)
      return
    }

    const homePosts = await Promise.all((res.home?.posts || []).map(async x => ({
      ...x,
      calculatedText: await getCalculatedText(x.caption)
    })))

    res.home.posts = homePosts


    store.dispatch({
      type: 'INITIAL_DATA',
      payload: res
    })
    setIsFetchingHomeData(false)
  }

  const fetchData = async () => {
    //Get Athlete/Fan Data
    if (initialUrl) {
      const { username, ctaAction } = extractUsernameAndCta(initialUrl);
      const res = await getId(username);

      if (res) {
        const { _id, username: extractedUsername, avatar, name, type } = res;
        setInitialParams({ _id, extractedUsername, avatar, ctaAction, name, type });
      }
    }

    setIsFetchingData(false)

  };


  async function handleLanguage() {
    const lang = await getLanguage()

    i18n.changeLanguage(lang)

    dispatch({
      type: 'UPDATE_APP_STATE',
      payload: { language: lang }
    })
  }


  if (isFetchingData || isFetchingHomeData) {
    return (
      <Col bg='#fff'>
        <Spinner fullScreen />
      </Col>
    )
  }


  return (
    <React.Fragment>
      <NetworkInfo />
      <Stack.Navigator
        initialRouteName={'AddNavigator'}
      >
        <Stack.Screen
          name='AddNavigator'
          component={AddNavigator}
          options={mainStackOptions}
        />
        <Stack.Screen
          name='SignUp'
          component={SignUp}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='CTASignUp'
          component={CTASignUp}
          options={mainStackOptions}
          initialParams={initialParams}
        />

        <Stack.Screen
          name='Login'
          component={Login}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='CTALogin'
          component={CTALogin}
          options={mainStackOptions}
          initialParams={initialParams}
        />

        <Stack.Screen
          name='ForgotPassword'
          component={ForgotPassword}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='NoUserFound'
          component={NoUserFound}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='RecoveryEmailSent'
          component={RecoveryEmailSent}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='CreateNewPassword'
          component={CreateNewPassword}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='MigratedUser'
          component={MigratedUser}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='ValidationEmailSent'
          component={ValidationEmailSent}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='VerifyEmail'
          component={VerifyEmail}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='AccountLocked'
          component={AccountLocked}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='PostsFeed'
          component={PostsFeed}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='UpdateApp'
          component={UpdateApp}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='Browse'
          component={Browse}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='AllUsers'
          component={AllUsers}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='LetsChatSportsComments'
          component={LetsChatSportsComments}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='LetsChatSports'
          component={LetsChatSports}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='ViewsOnTheNewsComments'
          component={ViewsOnTheNewsComments}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='Comments'
          component={Comments}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='VideoPreview'
          component={VideoPreview}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='Shoutouts'
          component={Shoutouts}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='AddShoutout'
          component={AddShoutout}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='RequestShoutout'
          component={RequestShoutout}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='Stories'
          component={Stories}
          options={mainStackOptionsSlideInBottom}
        />

        <Stack.Screen
          name='ViewsOnTheNews'
          component={ViewsOnTheNews}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='StackProfile'
          component={Profile}
          options={mainStackOptions}
        />


        <Stack.Screen name="HomeFeed" component={Home} />
      </Stack.Navigator>

      {loadingButton == 'navigator' && <Spinner fullScreen />}

      <ComingSoon />

      {/* only render when visible */}
      {isPromptModalVisible && <PromptAuthModal />}

    </React.Fragment>
  )
}

export default NotUser
