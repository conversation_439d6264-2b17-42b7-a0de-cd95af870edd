import React, { useState } from 'react'
import { useSelector } from 'react-redux'
import { Spinner, Col } from 'components'
import { createStackNavigator } from '@react-navigation/stack'
import {
  FavoriteSports,
  InitialInfo,
  MySports,
  Sports,
  Username,
  SearchTeams
} from 'screens/Onboarding/screens'

const Stack = createStackNavigator()

const mainStackOptions = {
  headerShown: false
}

const OnboardingHandler = ({ user }) => {
  const { loadingButton } = useSelector(state => state.appState)

  function getCurrentRoute() {
    switch (true) {
      case user.onboardingStep === 1:
        return 'Username'

      case user.onboardingStep === 2:
        return 'InitialInfo'

      case user.onboardingStep === 3 && user.type !== 'athlete':
        return 'Sports'

      case user.onboardingStep === 3 && user.type === 'athlete':
        return 'MySports'
    }
  }

  return (
    <React.Fragment>
      {loadingButton === 'navigator' && <Spinner fullScreen />}
      
      <Stack.Navigator initialRouteName={getCurrentRoute()}>
        <Stack.Screen
          name='Username'
          component={Username}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='InitialInfo'
          component={InitialInfo}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='Sports'
          component={Sports}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='FavoriteSports'
          component={FavoriteSports}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='MySports'
          component={MySports}
          options={mainStackOptions}
        />

        <Stack.Screen
          name='SearchTeams'
          component={SearchTeams}
          options={mainStackOptions}
        />
      </Stack.Navigator>
    </React.Fragment>
  )
}

export default OnboardingHandler
