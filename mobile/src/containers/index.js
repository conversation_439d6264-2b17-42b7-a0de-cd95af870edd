import React, { useEffect, useState } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { Platform, StatusBar, LogBox, Dimensions, Linking } from 'react-native'
import splashScreen from 'react-native-splash-screen'
import analytics from '@react-native-firebase/analytics'
import firebase from '@react-native-firebase/app';
import NotUser from 'app/containers/NotUser'
import User from 'app/containers/User'
import { useTranslation } from 'react-i18next'
import { checkIsZoomed } from 'core'
import { LocaleConfig } from 'react-native-calendars'

import 'intl'
import 'intl/locale-data/jsonp/en'
import DeviceInfo from 'react-native-device-info'
import { navigator } from 'core'
import { Api } from 'lib'
import redirectLink from '../core/redirectLinks'
import { Col, Spinner } from '../components'
import { version as packageAppVersion } from "../../package.json"
import { isNewVersionAvailable } from '../utils/commonUtils';

/*
  Much of the packages we use depend ViewPropTypes,
  ignore logs for now
*/
LogBox.ignoreLogs(['ViewPropTypes will be removed'])

/*
  Add month and date data for languages other than EN
  to use with react-native-calendars
*/
LocaleConfig.locales['it'] = {
  monthNames: ['Gennaio','Febbraio','Marzo','Aprile','Maggio','Giugno','Luglio','Agosto','Settembre','Ottobre','Novembre','Dicembre'],
  monthNamesShort: ['Gen', 'Feb', 'Mar', 'Apr', 'Mag', 'Giu', 'Lug', 'Ago', 'Set', 'Ott', 'Nov', 'Dic'],
  dayNames: ['Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato', 'Domenica'],
  dayNamesShort: ['Lun', 'Mar', 'Mer', 'Gio', 'Ven', 'Sab', 'Dom']
}

LocaleConfig.locales['ja'] = {
  monthNames: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
  monthNamesShort: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
  dayNames: ['日', '月', '火', '水', '木', '金', '土'],
  dayNamesShort: ['日', '月', '火', '水', '木', '金', '土'],
}

LocaleConfig.locales['nl'] = {
  monthNames: ['Januari','Februari','Maart','April','Mei','Juni','Juli','Augustus','September','Oktober','November','December'],
  monthNamesShort: ['Jan', 'Febr', 'Mrt', 'Apr', 'Mei', 'Juni', 'Juli', 'Aug', 'Sep', 'Okt', 'Nov', 'Dec'],
  dayNames: ['Maandag', 'Dinsdag', 'Woensdag', 'Donderdag', 'Vrijdag', 'Zaterdag', 'Zondag'],
  dayNamesShort: ['Ma', 'Di', 'Wo', 'Do', 'Vr', 'Za', 'Zo']
}

LocaleConfig.locales['en'] = LocaleConfig.locales['']

const AppNavigator = ({  }) => {

  const user = useSelector(state => state.user),
    deviceInfo = useSelector(state => state.appState.deviceInfo),
    dispatch = useDispatch(),
    { i18n } =  useTranslation(),
    { barStyle, isZoomed } = useSelector(state => state.appState),
    appLanguage = useSelector(state => state.appState.language),
    language = user?.language || appLanguage,
    isLoggedIn = !!user

  const installedAppVersion = DeviceInfo.getVersion() || packageAppVersion;

  const [didCheckUrl, setDidCheckUrl] = useState(false)
  const [initialUrl, setInitialUrl] = useState('')

   const getAppVersion = async () => {
     try {
       const res = await Api.get('/configs/getAppVersion');
       return res;
     } catch (err) {
       console.error('Fetch error (getAppVersion):', err);
     }
   };

   const getConfigs = async () => {
     try {
       const res = await Api.get('/configs/getConfigs');
       return res;
     } catch (err) {
       console.error('Fetch error (getConfigs):', err);
     }
   };

  useEffect(() => {
    (async () => {
      try {
        const appVersionResponse = await getAppVersion();
        const configsResponse = await getConfigs();

        if (!appVersionResponse || !configsResponse) {
          return;
        }

        const majorReleaseData = configsResponse.find(data => data._id === 'MAJOR_RELEASE');
        const isMajorReleaseEnabled = majorReleaseData?.isEnabled || false;

        const isNewVersion = isNewVersionAvailable(appVersionResponse, installedAppVersion);

        if (isMajorReleaseEnabled && isNewVersion) {
          navigator.replace('UpdateApp', {})
        }
      } catch (err) {
        console.error('Fetch error:', err);
      }
    })();
   
    if(Platform.OS === 'android') {
      StatusBar.setBackgroundColor('transparent')
      StatusBar.setTranslucent(true)
    }

    dispatch({
      type: 'UPDATE_APP_STATE',
      payload: { barStyle: 'dark-content' }
    })

    dispatch({
      type: 'LOADING_BUTTON',
      payload: null
    })

    dispatch({
      type: 'UPDATE_DEVICE_INFO',
      payload: { dimensions: Dimensions.get('window') }
    })

    dispatch({ type: 'RESET_LIVE_STREAM_STATE', payload: null })

    if(process.env.NODE_ENV !== 'development') {
      analytics().setAnalyticsCollectionEnabled(true)
    }

    splashScreen.hide()

    // When the component is unmounted, remove the listener
    return () => {}
  }, [])

  useEffect(() => {
    i18n.changeLanguage(language)

    LocaleConfig.defaultLocale = language

    dispatch({
      type: 'UPDATE_APP_STATE',
      payload: {
        isZoomed: checkIsZoomed(),
        language
      }
    })
  }, [isLoggedIn, language])

  /*
    Listens to bar style changes and updates the bar accordingly
  */
  useEffect(() => {
    StatusBar.setBarStyle(barStyle)
  }, [barStyle])

  useEffect(() => {
    const checkInitialUrl = async () => {
      try {
        const url = await Linking.getInitialURL();
        if (url && (
          url.includes('/profile') || 
          url.includes('/post') ||
          url.includes('/reset-password') ||
          url.includes('/forgot-password') ||
          url.includes('/verify-email') ||
          url.includes('/signup') ||
          url.includes('/shared/post')
        )) {
          const decodedString = decodeURIComponent(url);
          dispatch({
            type: 'UPDATE_INITIAL_URL',
            payload: decodedString
          });
          redirectLink(decodedString);
          setInitialUrl(decodedString);
        }
      } catch (err) {
        console.error('Error getting initial URL:', err);
      } finally {
        setDidCheckUrl(true);
      }
    };

    checkInitialUrl();

    const handleDeepLink = ({ url }) => {
      if (url && (
        url.includes('/profile') || 
        url.includes('/post') ||
        url.includes('/reset-password') ||
        url.includes('/forgot-password') ||
        url.includes('/verify-email') ||
        url.includes('/signup') ||
        url.includes('/shared/post')
      )) {
        const decodedString = decodeURIComponent(url);
        dispatch({
          type: 'UPDATE_INITIAL_URL',
          payload: decodedString
        });
        redirectLink(decodedString);
      }
    };

    const subscription = Linking.addEventListener('url', handleDeepLink);
    return () => subscription.remove();
  }, []);

  if (!didCheckUrl) {
    return (
      <Col bg='#fff'>
        <Spinner fullScreen />
      </Col>
    )
  }

  return user?._id ? <User /> : <NotUser initialUrl={initialUrl}/>
}

export default AppNavigator
