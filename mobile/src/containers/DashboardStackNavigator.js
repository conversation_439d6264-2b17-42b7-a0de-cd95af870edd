import React, { useEffect, useState } from 'react';
import { useNavigation, useIsFocused } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import Dashboard from 'screens/Dashboard';
import Profile from 'screens/Profile';

const Stack = createNativeStackNavigator();

const DashboardStackNavigator = () => {
  const navigation = useNavigation();

  return (
    <Stack.Navigator
    initialRouteName='AppProfile'
      screenOptions={{ headerShown: false }}
    >
      <Stack.Screen name="DashboardNav" component={Dashboard} />
      <Stack.Screen name="AppProfile" component={Profile} />
      
    </Stack.Navigator>
  );
};

export default DashboardStackNavigator;