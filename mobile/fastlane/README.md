fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## Android

### android default_changelog

```sh
[bundle exec] fastlane android default_changelog
```

Returns a default changelog.

### android build_and_upload_beta

```sh
[bundle exec] fastlane android build_and_upload_beta
```

Build Android and Deploy to App Center

### android upload_to_app_center

```sh
[bundle exec] fastlane android upload_to_app_center
```



----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
