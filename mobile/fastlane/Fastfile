platform :android do
    desc "Returns a default changelog."
    lane :default_changelog do
      additional_message = "" # Use this to add a custom message to the changelog

      changelog = changelog_from_git_commits(
        merge_commit_filtering: "exclude_merges",
        pretty: "• %s\n",
        commits_count: 5
      )

      changelog = additional_message + changelog
      changelog = changelog.sub(/[\u{1F300}-\u{1F6FF}]/, '')
      Actions.lane_context[SharedValues::FL_CHANGELOG] = changelog
      changelog
    end

    desc "Set google-services.json based on environment"
    lane :set_google_service do
      env = ENV['FIREBASE_PROJECT'] || "p4pv2" 
      puts "Setting google-services.json for environment: #{env}"

      if env == "TAILGATE" 
        # production
        sh "cp ../android/firebase/prod/google-services.json ../android/app/google-services.json"
      elsif env == "p4pv2"
        # development
        sh "cp ../android/firebase/uat/google-services.json ../android/app/google-services.json"
      else
        puts "Invalid FIREBASE_PROJECT! Expected 'prod' or 'uat'. Got: '#{env}'"
        exit 1
      end
    end

    desc "Build Android APK"
    lane :build_android_dev do |options|
      puts ENV["APPCENTER_OWNERNAME"]

      change_log = default_changelog

      version_number = options[:version_number]
      version_code = sh "git rev-list HEAD --count"

      # ./gradlew clean
      gradle(
        project_dir: 'android/',
        task: 'clean',
      )

      # ./gradlew assembleRelease
      gradle(
        project_dir: 'android/',
        task: 'assemble',
        build_type: 'Release',
        properties: {
          "versionCode" => version_code,
          "versionName" => version_number,
          "archivesBaseName" => ENV["APPCENTER_APP_NAME"]
        }
      )
    end

    desc "Build Android AAB"
    lane :build_android_prod do |options|
      puts ENV["APPCENTER_OWNERNAME"]

      change_log = default_changelog

      version_number = options[:version_number]
      version_code = sh "git rev-list HEAD --count"

      gradle(
        project_dir: 'android/',
        task: 'clean',
      )

      gradle(
        project_dir: 'android/',
        task: 'bundle',
        build_type: 'Release',
        properties: {
          "versionCode" => version_code,
          "versionName" => version_number,
          "archivesBaseName" => ENV["APPCENTER_APP_NAME"]
        }
      )

    end

    desc "Deploy to App Center"
    lane :upload_to_app_center do |options|
      puts ENV["APPCENTER_OWNERNAME"]

      change_log = default_changelog

      appcenter_upload(
        api_token: ENV["APPCENTER_API_TOKEN"],
        owner_name: ENV["APPCENTER_OWNERNAME"],
        app_name: ENV["APPCENTER_APP_NAME"],
        file: ENV["APK_OUTPUT_DIRECTORY"] + "app-release.apk",
        notify_testers: true,
        release_notes: change_log
      )
    end

    desc "Deploy to Firebase Distribution"
    lane :upload_to_firebase_distribution do |options|
      puts ENV["APPCENTER_OWNERNAME"]

      change_log = default_changelog
  
      firebase_app_distribution(
        app: ENV["FIREBASE_APP_ID"],
        apk_path: ENV["APK_OUTPUT_DIRECTORY"] + "app-release.apk",
        release_notes: change_log,
        groups: "tooltwist",
        service_credentials_file: "./firebase-p4pv2-key.json"
)
    end

    desc "Deploy to Playstore Alpha"
    lane :upload_to_play_store_alpha do |options|
      puts ENV["APPCENTER_OWNERNAME"]

      change_log = default_changelog

      supply(
        track: 'alpha',
        package_name: ENV["ANDROID_BUNDLE_ID"],
        json_key: ENV["APP_DIRECTORY"] + ENV["JSON_FILE"],
        aab: ENV["AAB_OUTPUT_DIRECTORY"] + "app-release.aab",
      )
    end

    desc "Build and Deploy Android App to App Center"
    lane :build_android_deploy_app_center do |options|
      build_android_dev
      upload_to_app_center
    end

    desc "Build and Deploy Android App to Firebase Distribution"
    lane :build_android_deploy_firebase_distribution do |options|
      set_google_service
      build_android_dev
      upload_to_firebase_distribution
    end

    desc "Build and Deploy Android App to Play Store"
    lane :build_android_deploy_play_store do |options|
      build_android_prod
      upload_to_play_store_alpha
    end
end

platform :ios do
  desc "Returns a default changelog."
  lane :default_changelog do
    additional_message = "" # Use this to add a custom message to the changelog

    changelog = changelog_from_git_commits(
      merge_commit_filtering: "exclude_merges",
      pretty: "• %s\n",
      commits_count: 5
    )
    changelog = additional_message + changelog
    changelog = changelog.sub(/[\u{1F300}-\u{1F6FF}]/, '')
    Actions.lane_context[SharedValues::FL_CHANGELOG] = changelog
    changelog
  end

  desc "Returns a app store connect key"
  lane :app_store_connect_key do
    api_key = app_store_connect_api_key(
      key_id: ENV["ASCK_KEY_ID"],
      issuer_id: ENV["ASCK_ISSUER"],
      key_filepath: ENV["ASCK_KEY_PATH"],
      duration: 1200, # optional
      in_house: false, # optional but may be required if using match/sigh
    )
  end

  desc "Register Devices to Developer Portal"
  lane :prepare_register_devices do
    # api_key = app_store_connect_key

    register_devices(
      devices: {
        "Kim's Device" => "00008120-00026C1C1A83C01E",
        "Kim's iPad" => "548060e6b748cf91e3e10442eb90bed3ffb1a670",
        "Ervy's iPhone" => "00008130-000A4C210130001C",
        "Ianne's Device" => "125daf5137625a0dd96d7ab248a6cf5d6ac71e99",
        "Julius's Device" => "00008030-000609C41EE8802E",
        "TR iPhone 15 Plus" => "00008120-001854621A12201E",
        "GoXpro's Device" => "00008110-001004891A51A01E",
        "GoXpro iPhone 13 Test Device" => "00008110-000825DE0E11A01E",
        "Raymond's Device" => "00008020-000660E41E90003A",
        "Jl's Device" => "00008110-000C045C3609A01E",
        "TR - Carl's iPhone 11 Pro" => "00008030-001508C81450802E",
        "TR - Earl's iPhone XR" => "00008020-001C11443421002E",
        "TR - Daniela's iPhone 12 Pro Max" => "00008101-000611DA010B001E",
        "TR - Omar Device iPhone XS Max" => "00008020-001475291468002E",
        "TR - Joshua Camon iPhone 6 Plus" => "b23dd7181a0fe1c96172d3aebce805aa0bdcbaf6"
      },
      team_id: ENV["TEAM_ID"],
      username: ENV["APPLE_ID"]
    )
  end

  desc "Set GoogleService-Info.plist based on environment"
  lane :set_google_service do
    env = ENV['FIREBASE_PROJECT'] || "p4pv2" 
    puts "🚀 Setting GoogleService-Info.plist for environment: #{env.upcase}"
  
    if env == "TAILGATE"
      # production
      sh "cp ../ios/firebase/prod/GoogleService-Info.plist ../ios/GoogleService-Info.plist"
    elsif env == "p4pv2"
      # development
      sh "cp ../ios/firebase/uat/GoogleService-Info.plist ../ios/GoogleService-Info.plist"
    else
      puts "Invalid FIREBASE_PROJECT! Expected 'prod' or 'uat'. Got: '#{env}'"
      exit 1
    end
  
    puts "✔ Successfully set GoogleService-Info.plist for #{env.upcase}"
  end

  desc "Pre iOS Build"
  lane :prebuild do |options|
    puts ENV["APPCENTER_OWNERNAME"]

    change_log = default_changelog
    # api_key = app_store_connect_key

    clean_build_artifacts
    clear_derived_data
    version_number = options[:version_number]
    
    # gets the total number of commits for project to use as comparable, unique number
    number_of_commits = sh "git rev-list HEAD --count"

    build_number = version_number + "." + number_of_commits
    build_number = build_number.tr("\n","") # trim newlines
    increment_version_number(
      version_number: options[:version_number],
      xcodeproj: "./ios/Playaz4Playaz.xcodeproj"
    )

    increment_build_number(
      build_number: build_number,
      xcodeproj: "./ios/Playaz4Playaz.xcodeproj"
    )
  end

  desc "Build iOS dev"
  lane :build_ios_dev do |options|
    gym(
      scheme: ENV["APP_SCHEME"],
      workspace: ENV["WORKSPACE"],
      output_directory: ENV["OUTPUT_DIRECTORY"],
      output_name: ENV["OUTPUT_FILENAME"],
      clean: true,
      export_method: 'development',
      export_options: {
        provisioningProfiles: {
          "app.playaz4playaz.ios" => "match Development app.playaz4playaz.ios",
        }
      },
      silent: true,
      suppress_xcode_output: true
    )
  end

  desc "Build iOS prod"
  lane :build_ios_prod do |options|
    gym(
      scheme: ENV["APP_SCHEME"],
      workspace: ENV["WORKSPACE"],
      output_directory: ENV["OUTPUT_DIRECTORY"],
      output_name: ENV["OUTPUT_FILENAME"],
      clean: true,
      export_method: 'app-store',
      export_options: {
        provisioningProfiles: {
          "app.playaz4playaz.ios" => "match Appstore app.playaz4playaz.ios",
        }
      },
      silent: true,
      suppress_xcode_output: true
    )
  end

  desc "Deploy to App Center"
  lane :upload_to_app_center do |options|
    puts ENV["APPCENTER_OWNERNAME"]

    change_log = default_changelog

    appcenter_upload(
      api_token: ENV["APPCENTER_API_TOKEN"],
      owner_name: ENV["APPCENTER_OWNERNAME"],
      app_name: ENV["APPCENTER_APP_NAME"],
      ipa: ENV["OUTPUT_DIRECTORY"] + ENV["OUTPUT_FILENAME"] + ".ipa",
      notify_testers: true,
      release_notes: change_log
    )
  end

  desc "Deploy to Firebase Distribution"
  lane :upload_to_firebase_distribution do |options|
    puts "Uploading to Firebase Distribution..."

    change_log = default_changelog

    firebase_app_distribution(
      app: ENV["FIREBASE_APP_ID"],
      ipa_path: ENV["OUTPUT_DIRECTORY"] + ENV["OUTPUT_FILENAME"] + ".ipa", 
      release_notes: change_log,
      groups: "tooltwist",
      service_credentials_file: "./firebase-p4pv2-key.json",
    )
  end

  desc "Deploy to App Store Connect"
  lane :upload_to_appstore do |options|
    ENV["DELIVER_ITMSTRANSPORTER_ADDITIONAL_UPLOAD_PARAMETERS"] = "-t HTTP #"

    deliver(
      precheck_include_in_app_purchases: false
    )
  end

  desc "Build and Deploy iOS App to App Center"
  lane :build_ios_deploy_app_center do |options|
    prepare_register_devices
    prebuild(
      version_number: options[:version_number]
    )
    build_ios_dev
    upload_to_app_center
  end

  desc "Build and Deploy iOS App to Firebase Distribution"
  lane :build_ios_deploy_firebase_distribution do |options|
    prepare_register_devices
    prebuild(
      version_number: options[:version_number]
    )
    
    set_google_service
    build_ios_dev
    upload_to_firebase_distribution
  end

  desc "Build and Deploy iOS App to App Center"
  lane :build_ios_deploy_app_store do |options|
    prebuild(
      version_number: options[:version_number]
    )
    build_ios_prod
    upload_to_appstore
  end

end
