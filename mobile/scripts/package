#!/bin/bash

rootfolder=$(pwd)
androidRoot=$rootfolder/android
androidApp=$androidRoot/app

performSed () {
    sed "s/$1/$2/" $3 > tmpfile
    mv tmpfile $3
}

echo "-------------------------------------------------------------------------------"

# replace versionCode if provided in the parameters
echo "Replacing version code for build.gradle"
versionCode=`git rev-list HEAD --count`

echo "Updating version to $versionCode"
performSed 'versionCode .*' 'versionCode '$versionCode'' $androidApp/build.gradle
echo "DONE!"
echo "-------------------------------------------------------------------------------"

# replace versionName if provided in the parameters
echo "Replacing version name for build.gradle"
versionName=$1
performSed 'versionName ".*"' 'versionName "'$versionName'"' $androidApp/build.gradle
echo "DONE!"
echo "-------------------------------------------------------------------------------"