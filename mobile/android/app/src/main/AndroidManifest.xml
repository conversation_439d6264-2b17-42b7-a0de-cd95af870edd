<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.playaz4playaz">

    <queries>
        <package android:name="com.facebook.katana" />
		<package android:name="com.instagram.android" />
        <package android:name="com.google.android.youtube" />     
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data  android:scheme="https" android:host="youtube.com" />
        </intent>
        <intent> 
            <action android:name="com.google.android.youtube.api.service.START" /> 
        </intent>
	</queries>

  	<uses-permission android:name="android.permission.INTERNET" />
	<uses-feature android:name="android.hardware.camera" />
	<uses-feature android:name="android.hardware.camera.autofocus" />
	<uses-feature android:name="android.hardware.audio.output" />
	<uses-feature android:name="android.hardware.microphone" />

	<uses-permission android:name="android.permission.CAMERA" />
	<uses-permission android:name="android.permission.RECORD_AUDIO" />

	<!-- Devices running Android 12L (API level 32) or lower  -->
	<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
	<!-- Devices running Android 13 (API level 33) or higher -->
	<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
	<uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
	<uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
	<uses-permission android:name="android.permission.BLUETOOTH" android:maxSdkVersion="30" />
	<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" android:maxSdkVersion="30" />
  	<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
	<uses-permission android:name="android.permission.WAKE_LOCK" />
	<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

	<uses-permission android:name="android.permission.BIND_TELECOM_CONNECTION_SERVICE"/>
	<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
	<uses-permission android:name="android.permission.READ_PHONE_STATE" />
	<uses-permission android:name="android.permission.CALL_PHONE" />

	

    <application
      android:requestLegacyExternalStorage="true"
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:largeHeap="true"
	  android:usesCleartextTraffic="true"
      android:hardwareAccelerated="true"
      android:theme="@style/AppTheme"
      android:supportsRtl="true"
      tools:replace="android:allowBackup">
			<service android:name="io.wazo.callkeep.RNCallKeepBackgroundMessagingService" />

			<service android:name="io.wazo.callkeep.VoiceConnectionService"
				android:label="Wazo"
				android:exported="true"
				android:permission="android.permission.BIND_TELECOM_CONNECTION_SERVICE"
				android:foregroundServiceType="camera|microphone"
				tools:ignore="MissingClass">

				<intent-filter>
					<action android:name="android.telecom.ConnectionService" />
				</intent-filter>
			</service>

      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustResize"
        android:exported="true">

        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>


            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- Custom URL Scheme -->
                <data android:scheme="playaz4playaz" />
            </intent-filter>


        <!-- UAT - Remove this when building android to PRODUCTION-->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="p4p-uat.duckdns.org" />
                <data android:pathPattern="/profile/.*/.*" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="p4p-uat.duckdns.org" />
                <data android:pathPattern="/shared/profile/.*/.*" />
            </intent-filter>

             <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="p4p-uat.duckdns.org" />
                <data android:pathPattern="/shared/profile/.*/*/" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="p4p-uat.duckdns.org" />
                <data android:pathPattern="/profile/.*/.*/.*" />
            </intent-filter>

             <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="p4p-uat.duckdns.org" />
                <data android:pathPattern="/profile/.*/post/.*" />
            </intent-filter>

             <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="p4p-uat.duckdns.org" />
                <data android:pathPattern="/profile/.*/shoutout/.*" />
            </intent-filter>


            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="p4p-uat.duckdns.org" />
                <data android:pathPattern="/shared/profile/.*/.*/.*" />
            </intent-filter>

             <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="p4p-uat.duckdns.org" />
                <data android:pathPattern="/shared/profile/.*/post/.*" />
            </intent-filter>

             <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="p4p-uat.duckdns.org" />
                <data android:pathPattern="/shared/profile/.*/shoutout/.*" />
            </intent-filter>

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="p4p-uat.duckdns.org" />
            </intent-filter>
         <!-- END - UAT -->

        <!-- PRODUCTION -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:host="playaz4playaz.com" />
                <data android:pathPattern="/profile/.*/.*" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:host="playaz4playaz.com" />
                <data android:pathPattern="/shared/profile/.*/.*" />
            </intent-filter>
              <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="playaz4playaz.com" />
                <data android:pathPattern="/profile/.*/.*/.*" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="playaz4playaz.com" />
                <data android:pathPattern="/shared/profile/.*/.*/.*" />
            </intent-filter>

             <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="playaz4playaz.com" />
            </intent-filter>
        

        <!-- END PRODUCTION -->

        </activity>

        <!-- Add an activity for Facebook, and an activity and intent filter for Chrome Custom Tabs -->
        <activity android:name="com.facebook.FacebookActivity"
            android:configChanges=
                    "keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:label="@string/app_name" />
        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="@string/fb_login_protocol_scheme" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notification" />
    </application>
</manifest>
