// import org.apache.tools.ant.taskdefs.condition.Os

// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "28.2.13676358"
        kotlinVersion = "2.0.21"
        googlePlayServicesAuthVersion = "19.2.0"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath 'com.google.gms:google-services:4.3.14'
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
    }
}

apply plugin: "com.facebook.react.rootproject"

configurations.all {
    resolutionStrategy {
        force 'androidx.core:core-ktx:1.6.0'
    }
}

allprojects {  
    repositories {
        maven {
            url "$rootDir/../node_modules/react-native-video-cache/android/libs"
        }
    }
}

// allprojects {
//     repositories {
//         maven {
//             // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
//             url("$rootDir/../node_modules/react-native/android")
//         }
//         maven {
//             // Android JSC is installed from npm
//             url("$rootDir/../node_modules/jsc-android/dist")
//         }
//         mavenCentral {
//             // We don't want to fetch react-native from Maven Central as there are
//             // older versions over there.
//             content {
//                 excludeGroup "com.facebook.react"
//             }
//         }
//         jcenter() {
//             content {
//                 includeModule("com.yqritc", "android-scalablevideoview")
//             }
//         }
//         google()
//         maven { url 'https://www.jitpack.io' }
//     }
//     configurations.all {
//         resolutionStrategy {
//             force 'com.android.support:support-v7:27.0.3'
//         }
//     } 
// }
