import { User, Post } from '../../models';
import { IUser } from '../../models/User';
import { IPost } from '../../models/Post';
import { capitalize, shuffleArray, getUserSports, sortByTrending } from '../../lib';
import { Request, Response } from 'express';
import { Types } from 'mongoose';

/**
 * User type for filtering
 */
type UserType = 'athlete' | 'team' | 'fan';

/**
 * Endpoint file interface
 */
interface IEndpointFile {
  method: string;
  action: (req: Request, res: Response) => void | Promise<void>;
  description: string;
  validator: Record<string, any>;
}

/**
 * Request body interface
 */
interface IGetDataBody {
  type?: UserType;
  sports?: string[];
  skip?: number;
  skipIds?: string[];
  fetchStartDate?: Date;
}

/**
 * User data interface for browse results
 */
interface IBrowseUser {
  _id: Types.ObjectId;
  name?: string;
  username?: string;
  avatar?: string;
  bio?: string;
  type?: UserType;
  followersCount?: number;
  sport?: string;
  league?: string;
  sports?: any[];
  lastStoryAt?: Date;
}

/**
 * User with blocking fields interface
 */
interface IUserWithBlocking extends Partial<IUser> {
  favoriteSports?: any[];
  sports?: any[];
  removedSuggestions?: string[];
  following?: Types.ObjectId[];
  blocked?: string[];
  blockedBy?: string[];
}

/**
 * Browse response interface
 */
interface IBrowseResponse {
  users: Record<string, IBrowseUser[]>;
  posts: IPost[];
  latestDate: Date | null;
}

const docs: IEndpointFile = {
  method: 'post',
  action,
  description: 'Get browsing data, it can be initial -no filters- or filtered data.',
  validator: {
    type: '',
    sports: ['']
  }
};

const allTypes: UserType[] = ['athlete', 'team', 'fan'];
const adminId = '6412373e42c50c45d07632d4';

async function fetchUsers(
  type?: UserType | null,
  sports?: RegExp[] | null,
  excludeIds: (string | Types.ObjectId)[] = [],
  isAlphabetical?: boolean
): Promise<IBrowseUser[]> {
  const processedSports = sports && sports.map((x) => new RegExp(x.toString(), 'i'));

  // Create user find query
  let query: any = { dummy: false };
  if (!isAlphabetical) query.avatar = { $ne: null, $exists: true };

  if (type) query.type = type;

  query.$or = [{ type: 'team' }, { _id: { $nin: [...excludeIds, adminId] } }];

  // if(sports && sports.length && type !== 'team') (
  //   query.$or = [
  //     { 'favoriteSports.sport': { $in: sports } },
  //     { 'sports.sport': { $in: sports } },
  //     { 'sport': { $in: sports } },
  //   ]
  // )

  // Find users
  const users = await User.find(query)
    .select('name username avatar bio type followersCount sport league sports lastStoryAt')
    .limit(20)
    .lean()
    .sort('-followersCount');

  return users as IBrowseUser[];
}

async function action(req: Request, res: Response): Promise<void> {
  const { id } = req.user as { id: string; lang?: string };
  const {
    type,
    sports = [],
    skip,
    skipIds = [],
    fetchStartDate = new Date()
  } = req.body as IGetDataBody;

  const user = (await User.findById(id)
    .select('favoriteSports sports removedSuggestions following blocked blockedBy')
    .lean()) as IUserWithBlocking | null;

  if (!user) {
    res.send(false);
    return;
  }

  // const userSports = getUserSports(user)
  //
  // const sportsQuery = sports.length
  //   ? sports
  //   : userSports.length
  //     ? userSports
  //     : null

  /*
    Our get query can contain all user types, type being empty means
    to fetch all user types.
    Create an array to search for each type.
  */
  let fetchedUsers: IBrowseUser[][] = [];
  let users: Record<string, IBrowseUser[]> = {};

  const blockRelatedIds = [...(user.blocked || []), ...(user.blockedBy || [])];
  const excludedUserIds = [...(user.following || []), ...blockRelatedIds];

  if (!skipIds.length) {
    const searchFor = type ? [type] : allTypes;

    /*
      Search users
    */
    fetchedUsers = await Promise.all(searchFor.map((x) => fetchUsers(x, null, excludedUserIds)));

    /*
      Map the original searchFor array to get meaningful object parameters
    */
    searchFor.forEach((x, i) => {
      users[x] = fetchedUsers[i];
    });
  }

  /*
    Build post find query related to type and sports
  */
  let query: any = {};

  if (type) query.ownerType = type;
  // if(sportsQuery && sportsQuery.length) query.sports = { $in: sportsQuery }
  query.isAvatar = false;

  const result = await sortByTrending({
    model: 'post',
    excludedIds: excludedUserIds,
    fetchStartDate
  });

  if (!result) {
    res.send({ users: {}, posts: [], latestDate: null });
    return;
  }

  const { posts, latestDate } = result;

  /**
   * NOTE: temporary fix for broken content until completely fixed
   *
   */
  const filteredPosts = posts.filter((post) => {
    return post.owner || post.metaData || post.youtubeId || (post.media && post.media.length);
  });

  const response: IBrowseResponse = {
    users,
    posts: filteredPosts as IPost[],
    latestDate
  };

  res.send(response);
}

export default docs;
