import { User } from '../../models';
import { IUser } from '../../models/User';
import { shuffleArray } from '../../lib';
import { Request, Response } from 'express';
import { Types } from 'mongoose';

/**
 * User type for filtering
 */
type UserType = 'athlete' | 'team' | 'fan';

/**
 * Endpoint file interface
 */
interface IEndpointFile {
  method: string;
  action: (req: Request, res: Response) => void | Promise<void>;
  description: string;
  validator: Record<string, any>;
}

/**
 * Request body interface
 */
interface IGetMoreUsersBody {
  skip?: number;
  type: UserType;
}

/**
 * User with blocking fields interface
 */
interface IUserWithBlocking extends Partial<IUser> {
  favoriteSports?: any[];
  sports?: any[];
  removedSuggestions?: string[];
  following?: Types.ObjectId[];
  blocked?: string[];
  blockedBy?: string[];
}

/**
 * Browse user result interface
 */
interface IBrowseUser {
  _id: Types.ObjectId;
  name?: string;
  username?: string;
  avatar?: string;
  bio?: string;
  type?: UserType;
  followersCount?: number;
  sport?: string;
  league?: string;
  sports?: any[];
  lastStoryAt?: Date;
}

const docs: IEndpointFile = {
  method: 'post',
  action,
  description: 'Get more users for browsing with pagination.',
  validator: {
    skip: 1,
    type: ''
  }
};

/**
 * Action function for getting more users
 * @param req - Resource request
 * @param res - Response to resource request
 * @returns Promise<void>
 */
async function action(req: Request, res: Response): Promise<void> {
  const { id } = req.user as { id: string; lang?: string };
  const { skip = 0, type } = req.body as IGetMoreUsersBody;

  const user = await User
    .findById(id)
    .select('favoriteSports sports removedSuggestions following blocked blockedBy')
    .lean() as IUserWithBlocking | null;

  if (!user) {
    res.send(false);
    return;
  }

  const blockRelatedIds = [
    ...(user.blocked || []),
    ...(user.blockedBy || [])
  ];

  const users = await User
    .find({ type, dummy: false, _id: { $nin: blockRelatedIds } })
    .select('name username avatar bio type followersCount sport league sports lastStoryAt')
    .limit(30)
    .skip(skip)
    .lean()
    .sort({ name: 1 })
    .collation({ locale: 'en', caseLevel: true }) as IBrowseUser[];

  res.send(users);
}

export default docs;
