import { User, Post } from '../../models'
import { capitalize, shuffleArray, getUserSports, sortByTrending } from '../../lib'

const docs = {
  method: 'post',
  action,
  description: 'Get browsing data, it can be initial -no filters- or filtered data.',
  validator: {
    type: '',
    sports: ['']
  }
}

const allTypes = ['athlete', 'team', 'fan']
const adminId = '6412373e42c50c45d07632d4'

async function fetchUsers(type, sports, excludeIds = [], isAlphabetical) {
  sports = sports && sports.map(x => new RegExp(x, 'i'))

  // Create user find query
  let query = { dummy: false }
  if(!isAlphabetical) query.avatar = { $ne: null, $exists: true }

  if(type) query.type = type

  query.$or = [
    { type: 'team' },
    { _id: { $nin: [...excludeIds, adminId] } }
  ]

  // if(sports && sports.length && type !== 'team') (
  //   query.$or = [
  //     { 'favoriteSports.sport': { $in: sports } },
  //     { 'sports.sport': { $in: sports } },
  //     { 'sport': { $in: sports } },
  //   ]
  // )

  // Find users
  const users = await User
    .find(query)
    .select('name username avatar bio type followersCount sport league sports lastStoryAt')
    .limit(20)
    .lean()
    .sort('-followersCount')

  return users
}

async function action(req, res) {
  const { id } = req.user
  const { type, sports = [], skip, skipIds = [], fetchStartDate = new Date() } = req.body

  const user = await User
    .findById(id)
    .select('favoriteSports sports removedSuggestions following blocked blockedBy')
    .lean()

  if(!user) return res.send(false)

  // const userSports = getUserSports(user)
  //
  // const sportsQuery = sports.length
  //   ? sports
  //   : userSports.length
  //     ? userSports
  //     : null

  /*
    Our get query can contain all user types, type being empty means
    to fetch all user types.
    Create an array to search for each type.
  */
  let fetchedUsers = []
  let users = {}

  const blockRelatedIds = [
    ...(user.blocked || []),
    ...(user.blockedBy || [])
  ]
  const excludedUserIds = [
    ...user.following,
    ...blockRelatedIds
  ]

  if(!skipIds.length) {
    const searchFor = type ? [type] : allTypes

    /*
      Search users
    */
    fetchedUsers = await Promise.all(
      searchFor.map(x => fetchUsers(x, null, excludedUserIds))
    )

    /*
      Map the original searchFor array to get meaningful object parameters
    */
    searchFor.forEach((x, i) => {
      users[x] = fetchedUsers[i]
    })
  }

  /*
    Build post find query related to type and sports
  */
  let query = {}

  if(type) query.ownerType = type
  // if(sportsQuery && sportsQuery.length) query.sports = { $in: sportsQuery }
  query.isAvatar = false

  const { posts, latestDate } = await sortByTrending(
    {
      model: 'post',
      excludedIds: excludedUserIds,
      fetchStartDate
    }
  )

  /**
   * NOTE: temporary fix for broken content until completely fixed
   * 
   */
  const filteredPosts = posts.filter(post => {
    return post.owner ||(post.metaData || post.youtubeId || (post.media && post.media.length))
  })
  
  res.send({
    users, 
    posts: filteredPosts, 
    latestDate 
  })
}

export default docs
