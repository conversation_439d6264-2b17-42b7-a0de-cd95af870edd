import { User, Tag } from '../../models';
import { IUser } from '../../models/User';
import { designatedCurrency } from '../../lib';
import { Request, Response } from 'express';
import { Types } from 'mongoose';

/**
 * User type for filtering
 */
type UserType = 'athlete' | 'team' | 'fan';

/**
 * Search type including tags
 */
type SearchType = UserType | 'tag' | '';

/**
 * Endpoint file interface
 */
interface IEndpointFile {
  method: string;
  action: (req: Request, res: Response) => void | Promise<void>;
  description: string;
  validator: Record<string, any>;
}

/**
 * Request body interface
 */
interface ISearchBody {
  text?: string;
  type?: SearchType;
}

/**
 * User with blocking fields interface
 */
interface IUserWithBlocking extends Partial<IUser> {
  blockedBy?: string[];
}

/**
 * Search user result interface
 */
interface ISearchUserResult {
  _id: Types.ObjectId;
  name?: string;
  username?: string;
  avatar?: string;
  bio?: string;
  type?: UserType;
  lastStoryAt?: Date;
  sports?: any[];
  billing?: any;
  country?: string;
  banned?: boolean;
}

/**
 * User result with currency interface
 */
interface IUserWithCurrency extends Omit<ISearchUserResult, 'country'> {
  currency: string;
}

/**
 * Tag result interface
 */
interface ITagResult {
  _id: Types.ObjectId;
  tag: string;
  count: number;
  createdAt: Date;
}

/**
 * Combined search result type
 */
type SearchResult = IUserWithCurrency | ITagResult;

const USER_TYPES: UserType[] = ['fan', 'athlete', 'team'];

const docs: IEndpointFile = {
  method: 'post',
  action,
  description: 'Search for users -teams, athletes, fans- or tags.',
  validator: {
    text: { type: 'string' },
    type: { type: 'enum', enum: [...USER_TYPES, 'tag', ''] }
  }
};

const adminId = '6412373e42c50c45d07632d4';

/**
 * Action function for searching users and tags
 * @param req - Resource request
 * @param res - Response to resource request
 * @returns Promise<void>
 */
async function action(req: Request, res: Response): Promise<void> {
  const { id } = req.user as { id: string; lang?: string };
  const { text, type } = req.body as ISearchBody;
  const textOrBlank = text ?? '';

  const user = await User
    .findById(id)
    .select('blockedBy')
    .lean() as IUserWithBlocking | null;

  if (!user) {
    res.end();
    return;
  }

  // Build user query with proper type filtering
  const userQuery: any = {
    $or: [
      { name: { $regex: textOrBlank, $options: 'i' } },
      { username: { $regex: textOrBlank, $options: 'i' } },
    ],
    dummy: false,
    _id: { $nin: [...user.blockedBy || [], adminId] }
  };

  // Only add type filter if it's a valid user type (not 'tag' or empty)
  if (type && USER_TYPES.includes(type as UserType)) {
    userQuery.type = type;
  } else {
    // If no type specified or type is 'tag' or empty, search all user types
    userQuery.type = { $in: USER_TYPES };
  }

  const users = await User
    .find(userQuery)
    .select('name username avatar bio type lastStoryAt sports billing country banned')
    .lean() as ISearchUserResult[];

  const tags = !type || type === 'tag'
    ? await Tag
      .find({
        tag: { $regex: textOrBlank, $options: 'i' }
      })
      .lean() as ITagResult[]
    : [];

  const usersWithCurrency: IUserWithCurrency[] = users.map(user => {
    const currency = designatedCurrency(user.country || '');
    const { country, ...userWithoutCountry } = user;
    
    return { ...userWithoutCountry, currency };
  });

  const results: SearchResult[] = [...usersWithCurrency, ...tags];
  res.send(results);
}

export default docs;
