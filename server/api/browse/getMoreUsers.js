import { User } from '../../models'
import { shuffleArray } from '../../lib'

const docs = {
  method: 'post',
  action,
  description: '',
  validator: {
    skip: 1,
    type: ''
  }
}

async function action(req, res) {
  const { id } = req.user
  const { skip = 0, type } = req.body

  const user = await User
    .findById(id)
    .select('favoriteSports sports removedSuggestions following blocked blockedBy')
    .lean()

  if(!user) return res.send(false)

  const blockRelatedIds = [
    ...(user.blocked || []),
    ...(user.blockedBy || [])
  ]

  const users = await User
    .find({ type, dummy: false, _id: { $nin: blockRelatedIds } })
    .select('name username avatar bio type followersCount sport league sports lastStoryAt')
    .limit(30)
    .skip(skip)
    .lean()
    .sort({ name: 1 })
    .collation({ locale: 'en', caseLevel: true })

  res.send(users)
}

export default docs
