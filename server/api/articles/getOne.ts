import { NewsArticle } from '../../models';
import { INewsArticle } from '../../models/NewsArticle';
import { Request, Response } from 'express';

/**
 * Endpoint file interface
 */
interface IEndpointFile {
  method: string;
  action: (req: Request, res: Response) => void | Promise<void>;
  description: string;
  params?: string;
  validator: Record<string, any>;
}

/**
 * Request params interface
 */
interface IGetOneParams {
  articleId: string;
}

/**
 * Response interface
 */
interface IGetOneResponse {
  article: INewsArticle | null;
  relatedArticles: INewsArticle[];
}

const docs: IEndpointFile = {
  method: 'get',
  action,
  description: 'Get a single news article by ID with related articles.',
  params: 'getOne/:articleId',
  validator: {
    articleId: { type: 'objectId', required: true }
  }
};

/**
 * Action function for getting a single article
 * @param req - Resource request
 * @param res - Response to resource request
 * @returns Promise<void>
 */
async function action(req: Request, res: Response): Promise<void> {
  const { articleId } = req.params as unknown as IGetOneParams;

  const article = await NewsArticle.findById(articleId) as INewsArticle | null;

  // const sport = article?.sport

  const relatedArticles = await NewsArticle
    .find()
    .sort({ createdAt: -1 })
    .limit(4) as INewsArticle[];

  const response: IGetOneResponse = {
    article,
    relatedArticles
  };

  res.send(response);
}

export default docs;
