import { NewsArticle } from '../../models';
import { INewsArticle } from '../../models/NewsArticle';
import { Request, Response } from 'express';

/**
 * Endpoint file interface
 */
interface IEndpointFile {
  method: string;
  action: (req: Request, res: Response) => void | Promise<void>;
  description: string;
  validator: Record<string, any>;
}

const docs: IEndpointFile = {
  method: 'get',
  action,
  description: 'Get all news articles sorted by creation date (newest first).',
  validator: {}
};

/**
 * Action function for getting all news articles
 * @param req - Resource request
 * @param res - Response to resource request
 * @returns Promise<void>
 */
async function action(req: Request, res: Response): Promise<void> {
  const articles = await NewsArticle.find().sort('-createdAt') as INewsArticle[];

  res.send(articles);
}

export default docs;
