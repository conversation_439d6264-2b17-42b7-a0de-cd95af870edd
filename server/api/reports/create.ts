import { Report, Story, Post, Shoutout, User } from '../../models';
import { IReport } from '../../models/Report';
import { IStory } from '../../models/Story';
import { IPost } from '../../models/Post';
import { IShoutout } from '../../models/Shoutout';
import { IUser } from '../../models/User';
import { getLocalText, sendMail } from '../../lib';
import config from '../../config';
import { Request, Response } from 'express';

/**
 * Entity type for reports
 */
type EntityType = 'post' | 'shoutout' | 'story';

/**
 * Endpoint file interface
 */
interface IEndpointFile {
  method: string;
  action: (req: Request, res: Response) => void | Promise<void>;
  description: string;
  validator: Record<string, any>;
}

/**
 * Request body interface
 */
interface ICreateReportBody {
  entityType: EntityType;
  entityId: string;
  description?: string;
}

/**
 * Populated user interface for shoutout
 */
interface IPopulatedUser {
  _id: any;
  name?: string;
  username?: string;
  avatar?: string;
  type?: 'fan' | 'athlete' | 'team';
}

/**
 * Populated shoutout interface
 */
interface IPopulatedShoutout extends IShoutout {
  from: IPopulatedUser;
  user: IPopulatedUser;
}

/**
 * Union type for fetched entities
 */
type FetchedEntity = IStory | IPost | IPopulatedShoutout;

const docs: IEndpointFile = {
  method: 'post',
  action,
  description: 'Report a user\'s post, story, or shoutout.',
  validator: {
    entityType: { type: '', required: true },
    entityId: { type: '', required: true },
    description: ''
  }
};

async function action(req: Request, res: Response): Promise<void> {
  const { id, lang } = req.user as { id: string; lang?: string };
  const { entityType, entityId, description } = req.body as ICreateReportBody;

  if (!['post', 'shoutout', 'story'].find(x => x === entityType)) {
    res.end();
    return;
  }

  const fetched = await Promise.all([
    Story
      .findOne({
        'recent._id': entityId
      })
      .select('-recent'),
    Post
      .findById(entityId),
    Shoutout
      .findById(entityId)
      .populate({ path: 'from', select: 'name username avatar type' })
      .populate({ path: 'user', select: 'name username avatar type' })
  ]);

  const data = fetched.filter((x): x is FetchedEntity => x !== null);
  const entity = data[0];

  /*
    This means the content has been removed,
    but users should at least get some sort of prompt
  */
  if (!entity) {
    res
      .status(202)
      .send(await getLocalText('reports.reportReceived', lang || 'en'));
    return;
  }

  const report = new Report({
    owner: id,
    reported: entityType === 'shoutout' 
      ? (entity as IPopulatedShoutout).from._id 
      : (entity as IPost | IStory).owner,
    [entityType]: entityId,
    entityType,
    description
  });

  await report.save();

  if (entityType === 'shoutout' && process.env.NODE_ENV) {
    const user = await User
      .findById(id)
      .select('name username email');

    const shoutoutEntity = entity as IPopulatedShoutout;

    let html = `
          <p>From: <b>${shoutoutEntity.from.name} (${shoutoutEntity.from.username})</b><p/>
          <p>To: <b>${shoutoutEntity.user.name} (${shoutoutEntity.user.username})</b><p/>
          <p>Shoutout: <p/>
          <p>${shoutoutEntity.comment}<p/>
          <br>
        `;

    if (user) {
      html += `
        <p>Reporter: <b>${user.name} (${user.username})</b><p/>
        <p>Description: <p/>
        <p>${description}<p/>
      `;
    }

    setTimeout(async () => {
      await sendMail({
        to: config.SIGNUP_FORWARD_EMAIL,
        subject: 'Report on Shoutout',
        html: html,
      });
    }, 2000);
  }

  console.log(`Reported activity - ${entityType}: ${entityId}`);

  res
    .status(202)
    .send(await getLocalText('reports.reportReceived', lang || 'en'));
}

export default docs;

