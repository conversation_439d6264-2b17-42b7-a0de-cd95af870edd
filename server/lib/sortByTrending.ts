import { IPost } from '@/models/Post';
import { Post, Shoutout } from '../models';
import { capitalize } from './';
import { differenceInDays } from 'date-fns';
import { Model, Types } from 'mongoose';
import { IShoutout } from '@/models/Shoutout';

/**
 * Sort by trending parameters interface
 */
interface ISortByTrendingParams {
  model: string;
  fetchStartDate?: Date;
  skipIds?: (string | Types.ObjectId)[];
  excludedIds?: (string | Types.ObjectId)[];
}

/**
 * Post/Shoutout with score interface
 */
interface IPostWithScore extends Partial<IPost | IShoutout> {
  score: number;
  createdAt: Date;
  reactionsCount?: number;
  commentsCount?: number;
  media?: Array<{ uri?: string }>;
  metaData?: any;
  youtubeId?: string;
}

/**
 * Sort by trending result interface
 */
interface ISortByTrendingResult {
  [key: string]: IPostWithScore[];
  latestDate: Date | null;
}

/**
 * Models map
 */
const models: Record<string, Model<IPost | IShoutout>> = {
  Post,
  Shoutout
};

/**
 * Sorts posts/shoutouts by trending score based on engagement and recency
 * @param params - Sorting parameters
 * @returns Promise with sorted posts/shoutouts and latest date
 */
async function sortByTrending(
  params: ISortByTrendingParams
): Promise<ISortByTrendingResult | void> {
  const { model = '', fetchStartDate = new Date(), skipIds = [], excludedIds = [] } = params;

  if (!model) {
    console.warn('A model is required');
    return;
  }

  const Model = models[capitalize(model)];
  if (!Model) {
    console.warn(`Model ${capitalize(model)} not found`);
    return;
  }

  const now = new Date();
  const isPost = model.toLowerCase() === 'post';

  /*
    Find posts starting from fetchDate
  */
  const posts = (await Model.find({
    createdAt: { $lt: fetchStartDate },
    owner: { $nin: excludedIds },
    from: { $nin: excludedIds },
    athlete: { $nin: excludedIds },
    subscriptionOnly: isPost ? false : { $exists: false }
  })
    .populate([
      {
        path: 'owner',
        select: 'name username avatar bio type lastStoryAt verified.id'
      },
      {
        path: 'originalOwner',
        select: 'name'
      },
      {
        path: 'from',
        select: 'name username avatar bio type lastStoryAt verified.id'
      },
      {
        path: 'user',
        select: 'name username avatar bio type lastStoryAt verified.id'
      }
    ])
    .sort('-createdAt')
    .limit(200)
    .lean()) as (IPost | IShoutout)[];

  /*
    For infinite scroll, send FE the latest post date to send with queries
  */
  const latestDate = posts[posts.length - 1] ? posts[posts.length - 1].createdAt : null;

  const sortedPosts = posts
    .map((po): IPostWithScore | false => {
      // basic date score, lowers the score per days passed
      if (
        isPost &&
        !po.metaData &&
        !po.youtubeId &&
        !(po.media && po.media[0] && po.media[0].uri)
      ) {
        return false;
      }

      const creationScore = 5000 - differenceInDays(now, new Date(po.createdAt));

      // basic engagement score, 10+counts gives more score if item has comment counts, etc
      const engagementScore =
        (po.reactionsCount ? po.reactionsCount + 10 : 0) +
        (po.commentsCount ? po.commentsCount + 10 : 0);

      const score = engagementScore + creationScore;
      return { ...po, score: engagementScore + creationScore } as IPostWithScore;
    })
    .filter((item): item is IPostWithScore => item !== false)
    .sort((x, y) => y.score - x.score);

  return {
    [model.toLowerCase() + 's']: sortedPosts,
    latestDate
  };
}

export default sortByTrending;
