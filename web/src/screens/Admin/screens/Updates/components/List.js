import React, { useState, useEffect, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Api, theme } from 'lib'
import { Col, Row, Input, Text, Spinner2 } from 'components'
import { ListItem } from './'
import { TableComponents } from 'screens/Admin/components'

const List = ({ data, onClick, dataId, loading }) => {

  return (
    <TableComponents.List
      loading={loading || !data}
      Header={() => (
        <React.Fragment>
          <TableComponents.Th first>
            <Text>Type</Text>
          </TableComponents.Th>

          <TableComponents.Th>
            <Text>Sport</Text>
          </TableComponents.Th>

          <TableComponents.Th>
            <Text>Users</Text>
          </TableComponents.Th>

          <TableComponents.Th>
            <Text>Description</Text>
          </TableComponents.Th>

          <TableComponents.Th>
            <Text>Date</Text>
          </TableComponents.Th>

          <TableComponents.Th last>
            <Text>Live</Text>
          </TableComponents.Th>
        </React.Fragment>
      )}
      Items={() => (!loading && data)
        ? data.map((item, i) => (
          <ListItem
            key={i}
            dataId={dataId}
            onClick={onClick}
            item={item}
          />
        ))
        : null
      }
    />
  )
}

export default List
