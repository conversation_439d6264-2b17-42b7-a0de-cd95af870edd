import React, { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import styled from 'styled-components'
import { Api, theme } from 'lib'
import { Col, Text, Row, Icon, Clickable, ComingUpItem } from 'components'
import { Header, EventPanel } from 'screens/Admin/components'
import { useInfiniteScroll } from 'core'
import { List, ListItem } from './components'

const EventUpdates = (props) => {
  const [panelVisible, setPanelVisible] = useState(false)
  const [dataId, setDataId] = useState(null)
  const isMobile = useSelector(state => state.appState.isMobile)
  const updates = useSelector(state => {
    return state.admin?.updates
      // .filter(x => new Date(x.date) > new Date())
      .sort((a, b) => new Date(b.date) - new Date(a.date))
  })
  const navigate = useNavigate()

  function handlePanel(item) {
    if(item) setDataId(item)
    setPanelVisible(!panelVisible)
    if(panelVisible) setDataId(null)
  }

  if(!updates) return null

  return (
    <Col
      wid='100%'
      center
    >
      <Col
        ht='100%'
        wid='calc(100vw - 190px)'
        noFlex
        start
      >
        <Header>
          <Row center>
            <Text h3>Updates</Text>

            <Clickable onClick={() => handlePanel(false)}>
              <Icon marg='0 0 0 8px' type='add' dimensions={18}/>
            </Clickable>
          </Row>
        </Header>

        <Col
          start
          noFlex
          marg='54px 0 0'
          wid='100%'
          ht='100vh'
          id='admin-updates-list'
          scrollY
        >
          <List
            data={updates}
            onClick={handlePanel}
            dataId={dataId}
          />
        </Col>
      </Col>

      <EventPanel
        visible={panelVisible}
        closeModal={handlePanel}
        dataId={dataId}
      />
    </Col>
  )
}

// <Col
//   bg={theme.GREY_20}
//   ht='100%'
//   wid='calc(100vw - 190px)'
//   marg='77px 0 0'
// >
//   <Col start>
//     <Row pad='24px' wrapFlex gap='24px' start>
//       {updates?.map((x, i) => (
//         <Clickable onClick={() => handlePanel(x)} key={x._id}>
//           <ComingUpItem item={x} key={i} admin />
//         </Clickable>
//       ))}
//     </Row>
//   </Col>
// </Col>
//

// </Col>


export default EventUpdates
