import React, { useState, useEffect, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Api, theme } from 'lib'
import { Col, Row, Input, Text, Spinner2 } from 'components'
import { ListItem, VerifyUser } from './'
import { TableComponents } from 'screens/Admin/components'

const List = ({
  data,
  onClick,
  onSort,
  sortBy,
  dataId,
  loading
}) => {

  return (
    <TableComponents.List
      loading={loading || !data}
      Header={() => (
        <React.Fragment>
          <TableComponents.Th first>
            <Text>Avatar</Text>
          </TableComponents.Th>

          <TableComponents.Th
            onSort={onSort}
            sortBy={sortBy}
            sortingKey='name'
          >
            <Text>User info</Text>
          </TableComponents.Th>

          <TableComponents.Th>
            <Text>Email</Text>
          </TableComponents.Th>

          <TableComponents.Th>
            <Text>Type</Text>
          </TableComponents.Th>

          <TableComponents.Th>
            <Text>Email verified</Text>
          </TableComponents.Th>

          <TableComponents.Th>
            <Text>ID verified</Text>
          </TableComponents.Th>

          <TableComponents.Th
            onSort={onSort}
            sortBy={sortBy}
            sortingKey='createdAt'
            last
          >
            <Text>Joined</Text>
          </TableComponents.Th>
        </React.Fragment>
      )}
      Items={() => (!loading && data)
        ? (
          data.map((item, i) => (
            <ListItem
              key={i}
              dataId={dataId}
              onClick={onClick}
              item={item}
            />
          ))
        )
        : null
      }
    />
  )
}

export default List
