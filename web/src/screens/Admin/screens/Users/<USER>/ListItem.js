import React, { useState, memo, useEffect } from 'react'
import styled from 'styled-components'
import { useTranslation } from 'react-i18next'
import { isThisYear, format } from 'date-fns'
import { theme, capitalize, normalizeDate } from 'lib'
import { Col, Row, Text, Avatar, Divider, Clickable } from 'components'
import { TableComponents } from 'screens/Admin/components'

const ListItem = memo(({ item, onClick = null, dataId }) => {
  const [avatar, setAvatar] = useState(item.avatar)
  const isSelected = dataId === item._id
  const { t } = useTranslation()
  const createdAt = new Date(item.createdAt)

  useEffect(() => {
    if(item.avatar === avatar) return
    
    setAvatar(item.avatar)
  }, [item.avatar])

  return (
    <TableComponents.Tr onClick={() => onClick(item._id)} isSelected={isSelected}>
      <TableComponents.Td
        isSelected={isSelected}
        first
      >
        <Col style={{ zIndex: -1, position: 'relative' }}>
          <Avatar user={item} avatar={avatar} tiny />
        </Col>
      </TableComponents.Td>

      <TableComponents.Td isSelected={isSelected} >
        <Text>{item.name}</Text>
        <Text c1 col={theme.GREY_60}>@{item.username}</Text>
      </TableComponents.Td>

      <TableComponents.Td isSelected={isSelected}>
        <Col noFlex>
          <Text>{item?.email?.includes('@') ? item.email : ''}</Text>
        </Col>
      </TableComponents.Td>

      <TableComponents.Td isSelected={isSelected}>
        <Col noFlex>
          <Text>{capitalize(item.type || '')}</Text>
        </Col>
      </TableComponents.Td>

      <TableComponents.Td isSelected={isSelected}>
        <Text>
          {item?.verified?.email ? 'Verified' : 'Not verified'}
        </Text>
      </TableComponents.Td>

      <TableComponents.Td
        isSelected={isSelected}
        last
      >
        <Text>
          {item?.verified?.id
            ? 'Verified'
            : 'Not verified'
          }
        </Text>
      </TableComponents.Td>

      <TableComponents.Td isSelected={isSelected}>
        <Col noFlex>
          <Text>{isThisYear(createdAt) ? '' : `${format(createdAt, 'yyyy')} `}{normalizeDate(item.createdAt, null, t)}</Text>
        </Col>
      </TableComponents.Td>
    </TableComponents.Tr>
  )
})

export default ListItem
